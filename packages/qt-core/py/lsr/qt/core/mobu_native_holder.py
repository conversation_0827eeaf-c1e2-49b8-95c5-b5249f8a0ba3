import pyfbsdk as fb
from PySide2 import QtWidgets
from pyfbsdk_additions import FBDestroyToolByName


fb_sys = fb.FBSystem()

if fb_sys.Version < 20000:
    from PySide2 import shiboken2
else:
    import shiboken2


class nativeWidgetHolder(fb.FBWidgetHolder):
    """
    native Widget Holder
    """
    def __init__(self, widget):
        fb.FBWidgetHolder.__init__(self)
        self.widget = widget

    def WidgetCreate(self, pWidgetParent):
        """
        FBWidgetHolder::WidgetCreate() virtual function can be overridden so that
        you have more flexibility in bridging native QT UI with the MotionBuilder UI
        framework. You can also do this using Python

        Args:
            pWidgetParent (long): pWidgetParent ptr

        Returns:
            cpp pointer
        """
        self.widget.setParent(shiboken2.wrapInstance(pWidgetParent, QtWidgets.QWidget))
        return shiboken2.getCppPointer(self.widget)[0]


class NativeQtWidgetTool(fb.FBTool):
    """
    Native Qt Widget Tool
    """
    def __init__(self, name, window):
        fb.FBTool.__init__(self, name)
        self.main_widget = window()
        self.main_widget.show()
        self.mNativeWidgetHolder = nativeWidgetHolder(self.main_widget)
        self.BuildLayout()

        # set FBWidgetHolder size
        size = self.main_widget.size()
        self.StartSizeX = size.width()
        self.StartSizeY = size.height()

    def BuildLayout(self):
        """
        Build Layout

        Returns:
            None
        """
        x = fb.FBAddRegionParam(5, fb.FBAttachType.kFBAttachLeft, "")
        y = fb.FBAddRegionParam(5, fb.FBAttachType.kFBAttachTop, "")
        w = fb.FBAddRegionParam(-5, fb.FBAttachType.kFBAttachRight, "")
        h = fb.FBAddRegionParam(-5, fb.FBAttachType.kFBAttachBottom, "")
        self.AddRegion("main", "main", x, y, w, h)
        self.SetControl("main", self.mNativeWidgetHolder)

    @classmethod
    def holderShow(cls, *args, **kwargs):
        """
        holder show a qt widget, like maya UI dock
        Args:
            *args (str, widget):
            **kwargs (dict):

        Returns:
            None
        """
        if len(args) == 2:
            name = args[0]
            window = args[1]
        else:
            name = kwargs['name']
            window = kwargs['window']
        FBDestroyToolByName(name)
        tool = cls(name, window)
        fb.ShowTool(tool)

    @classmethod
    def launchShow(cls, *args, **kwargs):
        """
        launch show a widget, common method

        Args:
            *args ():
            **kwargs ():

        Returns:

        """
        if len(args) == 1:
            window = args[0]
        else:
            window = kwargs['window']
        try:
            window.launch()
        except BaseException as e:
            print(e.message)
