# This file is automatically generated by tools/idna-data
# vim: set fileencoding=utf-8 :

"""IDNA Mapping Table from UTS46."""


__version__ = "13.0.0"
def _seg_0():
    return [
    (0x0, '3'),
    (0x1, '3'),
    (0x2, '3'),
    (0x3, '3'),
    (0x4, '3'),
    (0x5, '3'),
    (0x6, '3'),
    (0x7, '3'),
    (0x8, '3'),
    (0x9, '3'),
    (0xA, '3'),
    (0xB, '3'),
    (0xC, '3'),
    (0xD, '3'),
    (0xE, '3'),
    (0xF, '3'),
    (0x10, '3'),
    (0x11, '3'),
    (0x12, '3'),
    (0x13, '3'),
    (0x14, '3'),
    (0x15, '3'),
    (0x16, '3'),
    (0x17, '3'),
    (0x18, '3'),
    (0x19, '3'),
    (0x1A, '3'),
    (0x1B, '3'),
    (0x1C, '3'),
    (0x1D, '3'),
    (0x1E, '3'),
    (0x1F, '3'),
    (0x20, '3'),
    (0x21, '3'),
    (0x22, '3'),
    (0x23, '3'),
    (0x24, '3'),
    (0x25, '3'),
    (0x26, '3'),
    (0x27, '3'),
    (0x28, '3'),
    (0x29, '3'),
    (0x2A, '3'),
    (0x2B, '3'),
    (0x2C, '3'),
    (0x2D, 'V'),
    (0x2E, 'V'),
    (0x2F, '3'),
    (0x30, 'V'),
    (0x31, 'V'),
    (0x32, 'V'),
    (0x33, 'V'),
    (0x34, 'V'),
    (0x35, 'V'),
    (0x36, 'V'),
    (0x37, 'V'),
    (0x38, 'V'),
    (0x39, 'V'),
    (0x3A, '3'),
    (0x3B, '3'),
    (0x3C, '3'),
    (0x3D, '3'),
    (0x3E, '3'),
    (0x3F, '3'),
    (0x40, '3'),
    (0x41, 'M', u'a'),
    (0x42, 'M', u'b'),
    (0x43, 'M', u'c'),
    (0x44, 'M', u'd'),
    (0x45, 'M', u'e'),
    (0x46, 'M', u'f'),
    (0x47, 'M', u'g'),
    (0x48, 'M', u'h'),
    (0x49, 'M', u'i'),
    (0x4A, 'M', u'j'),
    (0x4B, 'M', u'k'),
    (0x4C, 'M', u'l'),
    (0x4D, 'M', u'm'),
    (0x4E, 'M', u'n'),
    (0x4F, 'M', u'o'),
    (0x50, 'M', u'p'),
    (0x51, 'M', u'q'),
    (0x52, 'M', u'r'),
    (0x53, 'M', u's'),
    (0x54, 'M', u't'),
    (0x55, 'M', u'u'),
    (0x56, 'M', u'v'),
    (0x57, 'M', u'w'),
    (0x58, 'M', u'x'),
    (0x59, 'M', u'y'),
    (0x5A, 'M', u'z'),
    (0x5B, '3'),
    (0x5C, '3'),
    (0x5D, '3'),
    (0x5E, '3'),
    (0x5F, '3'),
    (0x60, '3'),
    (0x61, 'V'),
    (0x62, 'V'),
    (0x63, 'V'),
    ]

def _seg_1():
    return [
    (0x64, 'V'),
    (0x65, 'V'),
    (0x66, 'V'),
    (0x67, 'V'),
    (0x68, 'V'),
    (0x69, 'V'),
    (0x6A, 'V'),
    (0x6B, 'V'),
    (0x6C, 'V'),
    (0x6D, 'V'),
    (0x6E, 'V'),
    (0x6F, 'V'),
    (0x70, 'V'),
    (0x71, 'V'),
    (0x72, 'V'),
    (0x73, 'V'),
    (0x74, 'V'),
    (0x75, 'V'),
    (0x76, 'V'),
    (0x77, 'V'),
    (0x78, 'V'),
    (0x79, 'V'),
    (0x7A, 'V'),
    (0x7B, '3'),
    (0x7C, '3'),
    (0x7D, '3'),
    (0x7E, '3'),
    (0x7F, '3'),
    (0x80, 'X'),
    (0x81, 'X'),
    (0x82, 'X'),
    (0x83, 'X'),
    (0x84, 'X'),
    (0x85, 'X'),
    (0x86, 'X'),
    (0x87, 'X'),
    (0x88, 'X'),
    (0x89, 'X'),
    (0x8A, 'X'),
    (0x8B, 'X'),
    (0x8C, 'X'),
    (0x8D, 'X'),
    (0x8E, 'X'),
    (0x8F, 'X'),
    (0x90, 'X'),
    (0x91, 'X'),
    (0x92, 'X'),
    (0x93, 'X'),
    (0x94, 'X'),
    (0x95, 'X'),
    (0x96, 'X'),
    (0x97, 'X'),
    (0x98, 'X'),
    (0x99, 'X'),
    (0x9A, 'X'),
    (0x9B, 'X'),
    (0x9C, 'X'),
    (0x9D, 'X'),
    (0x9E, 'X'),
    (0x9F, 'X'),
    (0xA0, '3', u' '),
    (0xA1, 'V'),
    (0xA2, 'V'),
    (0xA3, 'V'),
    (0xA4, 'V'),
    (0xA5, 'V'),
    (0xA6, 'V'),
    (0xA7, 'V'),
    (0xA8, '3', u' ̈'),
    (0xA9, 'V'),
    (0xAA, 'M', u'a'),
    (0xAB, 'V'),
    (0xAC, 'V'),
    (0xAD, 'I'),
    (0xAE, 'V'),
    (0xAF, '3', u' ̄'),
    (0xB0, 'V'),
    (0xB1, 'V'),
    (0xB2, 'M', u'2'),
    (0xB3, 'M', u'3'),
    (0xB4, '3', u' ́'),
    (0xB5, 'M', u'μ'),
    (0xB6, 'V'),
    (0xB7, 'V'),
    (0xB8, '3', u' ̧'),
    (0xB9, 'M', u'1'),
    (0xBA, 'M', u'o'),
    (0xBB, 'V'),
    (0xBC, 'M', u'1⁄4'),
    (0xBD, 'M', u'1⁄2'),
    (0xBE, 'M', u'3⁄4'),
    (0xBF, 'V'),
    (0xC0, 'M', u'à'),
    (0xC1, 'M', u'á'),
    (0xC2, 'M', u'â'),
    (0xC3, 'M', u'ã'),
    (0xC4, 'M', u'ä'),
    (0xC5, 'M', u'å'),
    (0xC6, 'M', u'æ'),
    (0xC7, 'M', u'ç'),
    ]

def _seg_2():
    return [
    (0xC8, 'M', u'è'),
    (0xC9, 'M', u'é'),
    (0xCA, 'M', u'ê'),
    (0xCB, 'M', u'ë'),
    (0xCC, 'M', u'ì'),
    (0xCD, 'M', u'í'),
    (0xCE, 'M', u'î'),
    (0xCF, 'M', u'ï'),
    (0xD0, 'M', u'ð'),
    (0xD1, 'M', u'ñ'),
    (0xD2, 'M', u'ò'),
    (0xD3, 'M', u'ó'),
    (0xD4, 'M', u'ô'),
    (0xD5, 'M', u'õ'),
    (0xD6, 'M', u'ö'),
    (0xD7, 'V'),
    (0xD8, 'M', u'ø'),
    (0xD9, 'M', u'ù'),
    (0xDA, 'M', u'ú'),
    (0xDB, 'M', u'û'),
    (0xDC, 'M', u'ü'),
    (0xDD, 'M', u'ý'),
    (0xDE, 'M', u'þ'),
    (0xDF, 'D', u'ss'),
    (0xE0, 'V'),
    (0xE1, 'V'),
    (0xE2, 'V'),
    (0xE3, 'V'),
    (0xE4, 'V'),
    (0xE5, 'V'),
    (0xE6, 'V'),
    (0xE7, 'V'),
    (0xE8, 'V'),
    (0xE9, 'V'),
    (0xEA, 'V'),
    (0xEB, 'V'),
    (0xEC, 'V'),
    (0xED, 'V'),
    (0xEE, 'V'),
    (0xEF, 'V'),
    (0xF0, 'V'),
    (0xF1, 'V'),
    (0xF2, 'V'),
    (0xF3, 'V'),
    (0xF4, 'V'),
    (0xF5, 'V'),
    (0xF6, 'V'),
    (0xF7, 'V'),
    (0xF8, 'V'),
    (0xF9, 'V'),
    (0xFA, 'V'),
    (0xFB, 'V'),
    (0xFC, 'V'),
    (0xFD, 'V'),
    (0xFE, 'V'),
    (0xFF, 'V'),
    (0x100, 'M', u'ā'),
    (0x101, 'V'),
    (0x102, 'M', u'ă'),
    (0x103, 'V'),
    (0x104, 'M', u'ą'),
    (0x105, 'V'),
    (0x106, 'M', u'ć'),
    (0x107, 'V'),
    (0x108, 'M', u'ĉ'),
    (0x109, 'V'),
    (0x10A, 'M', u'ċ'),
    (0x10B, 'V'),
    (0x10C, 'M', u'č'),
    (0x10D, 'V'),
    (0x10E, 'M', u'ď'),
    (0x10F, 'V'),
    (0x110, 'M', u'đ'),
    (0x111, 'V'),
    (0x112, 'M', u'ē'),
    (0x113, 'V'),
    (0x114, 'M', u'ĕ'),
    (0x115, 'V'),
    (0x116, 'M', u'ė'),
    (0x117, 'V'),
    (0x118, 'M', u'ę'),
    (0x119, 'V'),
    (0x11A, 'M', u'ě'),
    (0x11B, 'V'),
    (0x11C, 'M', u'ĝ'),
    (0x11D, 'V'),
    (0x11E, 'M', u'ğ'),
    (0x11F, 'V'),
    (0x120, 'M', u'ġ'),
    (0x121, 'V'),
    (0x122, 'M', u'ģ'),
    (0x123, 'V'),
    (0x124, 'M', u'ĥ'),
    (0x125, 'V'),
    (0x126, 'M', u'ħ'),
    (0x127, 'V'),
    (0x128, 'M', u'ĩ'),
    (0x129, 'V'),
    (0x12A, 'M', u'ī'),
    (0x12B, 'V'),
    ]

def _seg_3():
    return [
    (0x12C, 'M', u'ĭ'),
    (0x12D, 'V'),
    (0x12E, 'M', u'į'),
    (0x12F, 'V'),
    (0x130, 'M', u'i̇'),
    (0x131, 'V'),
    (0x132, 'M', u'ij'),
    (0x134, 'M', u'ĵ'),
    (0x135, 'V'),
    (0x136, 'M', u'ķ'),
    (0x137, 'V'),
    (0x139, 'M', u'ĺ'),
    (0x13A, 'V'),
    (0x13B, 'M', u'ļ'),
    (0x13C, 'V'),
    (0x13D, 'M', u'ľ'),
    (0x13E, 'V'),
    (0x13F, 'M', u'l·'),
    (0x141, 'M', u'ł'),
    (0x142, 'V'),
    (0x143, 'M', u'ń'),
    (0x144, 'V'),
    (0x145, 'M', u'ņ'),
    (0x146, 'V'),
    (0x147, 'M', u'ň'),
    (0x148, 'V'),
    (0x149, 'M', u'ʼn'),
    (0x14A, 'M', u'ŋ'),
    (0x14B, 'V'),
    (0x14C, 'M', u'ō'),
    (0x14D, 'V'),
    (0x14E, 'M', u'ŏ'),
    (0x14F, 'V'),
    (0x150, 'M', u'ő'),
    (0x151, 'V'),
    (0x152, 'M', u'œ'),
    (0x153, 'V'),
    (0x154, 'M', u'ŕ'),
    (0x155, 'V'),
    (0x156, 'M', u'ŗ'),
    (0x157, 'V'),
    (0x158, 'M', u'ř'),
    (0x159, 'V'),
    (0x15A, 'M', u'ś'),
    (0x15B, 'V'),
    (0x15C, 'M', u'ŝ'),
    (0x15D, 'V'),
    (0x15E, 'M', u'ş'),
    (0x15F, 'V'),
    (0x160, 'M', u'š'),
    (0x161, 'V'),
    (0x162, 'M', u'ţ'),
    (0x163, 'V'),
    (0x164, 'M', u'ť'),
    (0x165, 'V'),
    (0x166, 'M', u'ŧ'),
    (0x167, 'V'),
    (0x168, 'M', u'ũ'),
    (0x169, 'V'),
    (0x16A, 'M', u'ū'),
    (0x16B, 'V'),
    (0x16C, 'M', u'ŭ'),
    (0x16D, 'V'),
    (0x16E, 'M', u'ů'),
    (0x16F, 'V'),
    (0x170, 'M', u'ű'),
    (0x171, 'V'),
    (0x172, 'M', u'ų'),
    (0x173, 'V'),
    (0x174, 'M', u'ŵ'),
    (0x175, 'V'),
    (0x176, 'M', u'ŷ'),
    (0x177, 'V'),
    (0x178, 'M', u'ÿ'),
    (0x179, 'M', u'ź'),
    (0x17A, 'V'),
    (0x17B, 'M', u'ż'),
    (0x17C, 'V'),
    (0x17D, 'M', u'ž'),
    (0x17E, 'V'),
    (0x17F, 'M', u's'),
    (0x180, 'V'),
    (0x181, 'M', u'ɓ'),
    (0x182, 'M', u'ƃ'),
    (0x183, 'V'),
    (0x184, 'M', u'ƅ'),
    (0x185, 'V'),
    (0x186, 'M', u'ɔ'),
    (0x187, 'M', u'ƈ'),
    (0x188, 'V'),
    (0x189, 'M', u'ɖ'),
    (0x18A, 'M', u'ɗ'),
    (0x18B, 'M', u'ƌ'),
    (0x18C, 'V'),
    (0x18E, 'M', u'ǝ'),
    (0x18F, 'M', u'ə'),
    (0x190, 'M', u'ɛ'),
    (0x191, 'M', u'ƒ'),
    (0x192, 'V'),
    (0x193, 'M', u'ɠ'),
    ]

def _seg_4():
    return [
    (0x194, 'M', u'ɣ'),
    (0x195, 'V'),
    (0x196, 'M', u'ɩ'),
    (0x197, 'M', u'ɨ'),
    (0x198, 'M', u'ƙ'),
    (0x199, 'V'),
    (0x19C, 'M', u'ɯ'),
    (0x19D, 'M', u'ɲ'),
    (0x19E, 'V'),
    (0x19F, 'M', u'ɵ'),
    (0x1A0, 'M', u'ơ'),
    (0x1A1, 'V'),
    (0x1A2, 'M', u'ƣ'),
    (0x1A3, 'V'),
    (0x1A4, 'M', u'ƥ'),
    (0x1A5, 'V'),
    (0x1A6, 'M', u'ʀ'),
    (0x1A7, 'M', u'ƨ'),
    (0x1A8, 'V'),
    (0x1A9, 'M', u'ʃ'),
    (0x1AA, 'V'),
    (0x1AC, 'M', u'ƭ'),
    (0x1AD, 'V'),
    (0x1AE, 'M', u'ʈ'),
    (0x1AF, 'M', u'ư'),
    (0x1B0, 'V'),
    (0x1B1, 'M', u'ʊ'),
    (0x1B2, 'M', u'ʋ'),
    (0x1B3, 'M', u'ƴ'),
    (0x1B4, 'V'),
    (0x1B5, 'M', u'ƶ'),
    (0x1B6, 'V'),
    (0x1B7, 'M', u'ʒ'),
    (0x1B8, 'M', u'ƹ'),
    (0x1B9, 'V'),
    (0x1BC, 'M', u'ƽ'),
    (0x1BD, 'V'),
    (0x1C4, 'M', u'dž'),
    (0x1C7, 'M', u'lj'),
    (0x1CA, 'M', u'nj'),
    (0x1CD, 'M', u'ǎ'),
    (0x1CE, 'V'),
    (0x1CF, 'M', u'ǐ'),
    (0x1D0, 'V'),
    (0x1D1, 'M', u'ǒ'),
    (0x1D2, 'V'),
    (0x1D3, 'M', u'ǔ'),
    (0x1D4, 'V'),
    (0x1D5, 'M', u'ǖ'),
    (0x1D6, 'V'),
    (0x1D7, 'M', u'ǘ'),
    (0x1D8, 'V'),
    (0x1D9, 'M', u'ǚ'),
    (0x1DA, 'V'),
    (0x1DB, 'M', u'ǜ'),
    (0x1DC, 'V'),
    (0x1DE, 'M', u'ǟ'),
    (0x1DF, 'V'),
    (0x1E0, 'M', u'ǡ'),
    (0x1E1, 'V'),
    (0x1E2, 'M', u'ǣ'),
    (0x1E3, 'V'),
    (0x1E4, 'M', u'ǥ'),
    (0x1E5, 'V'),
    (0x1E6, 'M', u'ǧ'),
    (0x1E7, 'V'),
    (0x1E8, 'M', u'ǩ'),
    (0x1E9, 'V'),
    (0x1EA, 'M', u'ǫ'),
    (0x1EB, 'V'),
    (0x1EC, 'M', u'ǭ'),
    (0x1ED, 'V'),
    (0x1EE, 'M', u'ǯ'),
    (0x1EF, 'V'),
    (0x1F1, 'M', u'dz'),
    (0x1F4, 'M', u'ǵ'),
    (0x1F5, 'V'),
    (0x1F6, 'M', u'ƕ'),
    (0x1F7, 'M', u'ƿ'),
    (0x1F8, 'M', u'ǹ'),
    (0x1F9, 'V'),
    (0x1FA, 'M', u'ǻ'),
    (0x1FB, 'V'),
    (0x1FC, 'M', u'ǽ'),
    (0x1FD, 'V'),
    (0x1FE, 'M', u'ǿ'),
    (0x1FF, 'V'),
    (0x200, 'M', u'ȁ'),
    (0x201, 'V'),
    (0x202, 'M', u'ȃ'),
    (0x203, 'V'),
    (0x204, 'M', u'ȅ'),
    (0x205, 'V'),
    (0x206, 'M', u'ȇ'),
    (0x207, 'V'),
    (0x208, 'M', u'ȉ'),
    (0x209, 'V'),
    (0x20A, 'M', u'ȋ'),
    (0x20B, 'V'),
    (0x20C, 'M', u'ȍ'),
    ]

def _seg_5():
    return [
    (0x20D, 'V'),
    (0x20E, 'M', u'ȏ'),
    (0x20F, 'V'),
    (0x210, 'M', u'ȑ'),
    (0x211, 'V'),
    (0x212, 'M', u'ȓ'),
    (0x213, 'V'),
    (0x214, 'M', u'ȕ'),
    (0x215, 'V'),
    (0x216, 'M', u'ȗ'),
    (0x217, 'V'),
    (0x218, 'M', u'ș'),
    (0x219, 'V'),
    (0x21A, 'M', u'ț'),
    (0x21B, 'V'),
    (0x21C, 'M', u'ȝ'),
    (0x21D, 'V'),
    (0x21E, 'M', u'ȟ'),
    (0x21F, 'V'),
    (0x220, 'M', u'ƞ'),
    (0x221, 'V'),
    (0x222, 'M', u'ȣ'),
    (0x223, 'V'),
    (0x224, 'M', u'ȥ'),
    (0x225, 'V'),
    (0x226, 'M', u'ȧ'),
    (0x227, 'V'),
    (0x228, 'M', u'ȩ'),
    (0x229, 'V'),
    (0x22A, 'M', u'ȫ'),
    (0x22B, 'V'),
    (0x22C, 'M', u'ȭ'),
    (0x22D, 'V'),
    (0x22E, 'M', u'ȯ'),
    (0x22F, 'V'),
    (0x230, 'M', u'ȱ'),
    (0x231, 'V'),
    (0x232, 'M', u'ȳ'),
    (0x233, 'V'),
    (0x23A, 'M', u'ⱥ'),
    (0x23B, 'M', u'ȼ'),
    (0x23C, 'V'),
    (0x23D, 'M', u'ƚ'),
    (0x23E, 'M', u'ⱦ'),
    (0x23F, 'V'),
    (0x241, 'M', u'ɂ'),
    (0x242, 'V'),
    (0x243, 'M', u'ƀ'),
    (0x244, 'M', u'ʉ'),
    (0x245, 'M', u'ʌ'),
    (0x246, 'M', u'ɇ'),
    (0x247, 'V'),
    (0x248, 'M', u'ɉ'),
    (0x249, 'V'),
    (0x24A, 'M', u'ɋ'),
    (0x24B, 'V'),
    (0x24C, 'M', u'ɍ'),
    (0x24D, 'V'),
    (0x24E, 'M', u'ɏ'),
    (0x24F, 'V'),
    (0x2B0, 'M', u'h'),
    (0x2B1, 'M', u'ɦ'),
    (0x2B2, 'M', u'j'),
    (0x2B3, 'M', u'r'),
    (0x2B4, 'M', u'ɹ'),
    (0x2B5, 'M', u'ɻ'),
    (0x2B6, 'M', u'ʁ'),
    (0x2B7, 'M', u'w'),
    (0x2B8, 'M', u'y'),
    (0x2B9, 'V'),
    (0x2D8, '3', u' ̆'),
    (0x2D9, '3', u' ̇'),
    (0x2DA, '3', u' ̊'),
    (0x2DB, '3', u' ̨'),
    (0x2DC, '3', u' ̃'),
    (0x2DD, '3', u' ̋'),
    (0x2DE, 'V'),
    (0x2E0, 'M', u'ɣ'),
    (0x2E1, 'M', u'l'),
    (0x2E2, 'M', u's'),
    (0x2E3, 'M', u'x'),
    (0x2E4, 'M', u'ʕ'),
    (0x2E5, 'V'),
    (0x340, 'M', u'̀'),
    (0x341, 'M', u'́'),
    (0x342, 'V'),
    (0x343, 'M', u'̓'),
    (0x344, 'M', u'̈́'),
    (0x345, 'M', u'ι'),
    (0x346, 'V'),
    (0x34F, 'I'),
    (0x350, 'V'),
    (0x370, 'M', u'ͱ'),
    (0x371, 'V'),
    (0x372, 'M', u'ͳ'),
    (0x373, 'V'),
    (0x374, 'M', u'ʹ'),
    (0x375, 'V'),
    (0x376, 'M', u'ͷ'),
    (0x377, 'V'),
    ]

def _seg_6():
    return [
    (0x378, 'X'),
    (0x37A, '3', u' ι'),
    (0x37B, 'V'),
    (0x37E, '3', u';'),
    (0x37F, 'M', u'ϳ'),
    (0x380, 'X'),
    (0x384, '3', u' ́'),
    (0x385, '3', u' ̈́'),
    (0x386, 'M', u'ά'),
    (0x387, 'M', u'·'),
    (0x388, 'M', u'έ'),
    (0x389, 'M', u'ή'),
    (0x38A, 'M', u'ί'),
    (0x38B, 'X'),
    (0x38C, 'M', u'ό'),
    (0x38D, 'X'),
    (0x38E, 'M', u'ύ'),
    (0x38F, 'M', u'ώ'),
    (0x390, 'V'),
    (0x391, 'M', u'α'),
    (0x392, 'M', u'β'),
    (0x393, 'M', u'γ'),
    (0x394, 'M', u'δ'),
    (0x395, 'M', u'ε'),
    (0x396, 'M', u'ζ'),
    (0x397, 'M', u'η'),
    (0x398, 'M', u'θ'),
    (0x399, 'M', u'ι'),
    (0x39A, 'M', u'κ'),
    (0x39B, 'M', u'λ'),
    (0x39C, 'M', u'μ'),
    (0x39D, 'M', u'ν'),
    (0x39E, 'M', u'ξ'),
    (0x39F, 'M', u'ο'),
    (0x3A0, 'M', u'π'),
    (0x3A1, 'M', u'ρ'),
    (0x3A2, 'X'),
    (0x3A3, 'M', u'σ'),
    (0x3A4, 'M', u'τ'),
    (0x3A5, 'M', u'υ'),
    (0x3A6, 'M', u'φ'),
    (0x3A7, 'M', u'χ'),
    (0x3A8, 'M', u'ψ'),
    (0x3A9, 'M', u'ω'),
    (0x3AA, 'M', u'ϊ'),
    (0x3AB, 'M', u'ϋ'),
    (0x3AC, 'V'),
    (0x3C2, 'D', u'σ'),
    (0x3C3, 'V'),
    (0x3CF, 'M', u'ϗ'),
    (0x3D0, 'M', u'β'),
    (0x3D1, 'M', u'θ'),
    (0x3D2, 'M', u'υ'),
    (0x3D3, 'M', u'ύ'),
    (0x3D4, 'M', u'ϋ'),
    (0x3D5, 'M', u'φ'),
    (0x3D6, 'M', u'π'),
    (0x3D7, 'V'),
    (0x3D8, 'M', u'ϙ'),
    (0x3D9, 'V'),
    (0x3DA, 'M', u'ϛ'),
    (0x3DB, 'V'),
    (0x3DC, 'M', u'ϝ'),
    (0x3DD, 'V'),
    (0x3DE, 'M', u'ϟ'),
    (0x3DF, 'V'),
    (0x3E0, 'M', u'ϡ'),
    (0x3E1, 'V'),
    (0x3E2, 'M', u'ϣ'),
    (0x3E3, 'V'),
    (0x3E4, 'M', u'ϥ'),
    (0x3E5, 'V'),
    (0x3E6, 'M', u'ϧ'),
    (0x3E7, 'V'),
    (0x3E8, 'M', u'ϩ'),
    (0x3E9, 'V'),
    (0x3EA, 'M', u'ϫ'),
    (0x3EB, 'V'),
    (0x3EC, 'M', u'ϭ'),
    (0x3ED, 'V'),
    (0x3EE, 'M', u'ϯ'),
    (0x3EF, 'V'),
    (0x3F0, 'M', u'κ'),
    (0x3F1, 'M', u'ρ'),
    (0x3F2, 'M', u'σ'),
    (0x3F3, 'V'),
    (0x3F4, 'M', u'θ'),
    (0x3F5, 'M', u'ε'),
    (0x3F6, 'V'),
    (0x3F7, 'M', u'ϸ'),
    (0x3F8, 'V'),
    (0x3F9, 'M', u'σ'),
    (0x3FA, 'M', u'ϻ'),
    (0x3FB, 'V'),
    (0x3FD, 'M', u'ͻ'),
    (0x3FE, 'M', u'ͼ'),
    (0x3FF, 'M', u'ͽ'),
    (0x400, 'M', u'ѐ'),
    (0x401, 'M', u'ё'),
    (0x402, 'M', u'ђ'),
    ]

def _seg_7():
    return [
    (0x403, 'M', u'ѓ'),
    (0x404, 'M', u'є'),
    (0x405, 'M', u'ѕ'),
    (0x406, 'M', u'і'),
    (0x407, 'M', u'ї'),
    (0x408, 'M', u'ј'),
    (0x409, 'M', u'љ'),
    (0x40A, 'M', u'њ'),
    (0x40B, 'M', u'ћ'),
    (0x40C, 'M', u'ќ'),
    (0x40D, 'M', u'ѝ'),
    (0x40E, 'M', u'ў'),
    (0x40F, 'M', u'џ'),
    (0x410, 'M', u'а'),
    (0x411, 'M', u'б'),
    (0x412, 'M', u'в'),
    (0x413, 'M', u'г'),
    (0x414, 'M', u'д'),
    (0x415, 'M', u'е'),
    (0x416, 'M', u'ж'),
    (0x417, 'M', u'з'),
    (0x418, 'M', u'и'),
    (0x419, 'M', u'й'),
    (0x41A, 'M', u'к'),
    (0x41B, 'M', u'л'),
    (0x41C, 'M', u'м'),
    (0x41D, 'M', u'н'),
    (0x41E, 'M', u'о'),
    (0x41F, 'M', u'п'),
    (0x420, 'M', u'р'),
    (0x421, 'M', u'с'),
    (0x422, 'M', u'т'),
    (0x423, 'M', u'у'),
    (0x424, 'M', u'ф'),
    (0x425, 'M', u'х'),
    (0x426, 'M', u'ц'),
    (0x427, 'M', u'ч'),
    (0x428, 'M', u'ш'),
    (0x429, 'M', u'щ'),
    (0x42A, 'M', u'ъ'),
    (0x42B, 'M', u'ы'),
    (0x42C, 'M', u'ь'),
    (0x42D, 'M', u'э'),
    (0x42E, 'M', u'ю'),
    (0x42F, 'M', u'я'),
    (0x430, 'V'),
    (0x460, 'M', u'ѡ'),
    (0x461, 'V'),
    (0x462, 'M', u'ѣ'),
    (0x463, 'V'),
    (0x464, 'M', u'ѥ'),
    (0x465, 'V'),
    (0x466, 'M', u'ѧ'),
    (0x467, 'V'),
    (0x468, 'M', u'ѩ'),
    (0x469, 'V'),
    (0x46A, 'M', u'ѫ'),
    (0x46B, 'V'),
    (0x46C, 'M', u'ѭ'),
    (0x46D, 'V'),
    (0x46E, 'M', u'ѯ'),
    (0x46F, 'V'),
    (0x470, 'M', u'ѱ'),
    (0x471, 'V'),
    (0x472, 'M', u'ѳ'),
    (0x473, 'V'),
    (0x474, 'M', u'ѵ'),
    (0x475, 'V'),
    (0x476, 'M', u'ѷ'),
    (0x477, 'V'),
    (0x478, 'M', u'ѹ'),
    (0x479, 'V'),
    (0x47A, 'M', u'ѻ'),
    (0x47B, 'V'),
    (0x47C, 'M', u'ѽ'),
    (0x47D, 'V'),
    (0x47E, 'M', u'ѿ'),
    (0x47F, 'V'),
    (0x480, 'M', u'ҁ'),
    (0x481, 'V'),
    (0x48A, 'M', u'ҋ'),
    (0x48B, 'V'),
    (0x48C, 'M', u'ҍ'),
    (0x48D, 'V'),
    (0x48E, 'M', u'ҏ'),
    (0x48F, 'V'),
    (0x490, 'M', u'ґ'),
    (0x491, 'V'),
    (0x492, 'M', u'ғ'),
    (0x493, 'V'),
    (0x494, 'M', u'ҕ'),
    (0x495, 'V'),
    (0x496, 'M', u'җ'),
    (0x497, 'V'),
    (0x498, 'M', u'ҙ'),
    (0x499, 'V'),
    (0x49A, 'M', u'қ'),
    (0x49B, 'V'),
    (0x49C, 'M', u'ҝ'),
    (0x49D, 'V'),
    ]

def _seg_8():
    return [
    (0x49E, 'M', u'ҟ'),
    (0x49F, 'V'),
    (0x4A0, 'M', u'ҡ'),
    (0x4A1, 'V'),
    (0x4A2, 'M', u'ң'),
    (0x4A3, 'V'),
    (0x4A4, 'M', u'ҥ'),
    (0x4A5, 'V'),
    (0x4A6, 'M', u'ҧ'),
    (0x4A7, 'V'),
    (0x4A8, 'M', u'ҩ'),
    (0x4A9, 'V'),
    (0x4AA, 'M', u'ҫ'),
    (0x4AB, 'V'),
    (0x4AC, 'M', u'ҭ'),
    (0x4AD, 'V'),
    (0x4AE, 'M', u'ү'),
    (0x4AF, 'V'),
    (0x4B0, 'M', u'ұ'),
    (0x4B1, 'V'),
    (0x4B2, 'M', u'ҳ'),
    (0x4B3, 'V'),
    (0x4B4, 'M', u'ҵ'),
    (0x4B5, 'V'),
    (0x4B6, 'M', u'ҷ'),
    (0x4B7, 'V'),
    (0x4B8, 'M', u'ҹ'),
    (0x4B9, 'V'),
    (0x4BA, 'M', u'һ'),
    (0x4BB, 'V'),
    (0x4BC, 'M', u'ҽ'),
    (0x4BD, 'V'),
    (0x4BE, 'M', u'ҿ'),
    (0x4BF, 'V'),
    (0x4C0, 'X'),
    (0x4C1, 'M', u'ӂ'),
    (0x4C2, 'V'),
    (0x4C3, 'M', u'ӄ'),
    (0x4C4, 'V'),
    (0x4C5, 'M', u'ӆ'),
    (0x4C6, 'V'),
    (0x4C7, 'M', u'ӈ'),
    (0x4C8, 'V'),
    (0x4C9, 'M', u'ӊ'),
    (0x4CA, 'V'),
    (0x4CB, 'M', u'ӌ'),
    (0x4CC, 'V'),
    (0x4CD, 'M', u'ӎ'),
    (0x4CE, 'V'),
    (0x4D0, 'M', u'ӑ'),
    (0x4D1, 'V'),
    (0x4D2, 'M', u'ӓ'),
    (0x4D3, 'V'),
    (0x4D4, 'M', u'ӕ'),
    (0x4D5, 'V'),
    (0x4D6, 'M', u'ӗ'),
    (0x4D7, 'V'),
    (0x4D8, 'M', u'ә'),
    (0x4D9, 'V'),
    (0x4DA, 'M', u'ӛ'),
    (0x4DB, 'V'),
    (0x4DC, 'M', u'ӝ'),
    (0x4DD, 'V'),
    (0x4DE, 'M', u'ӟ'),
    (0x4DF, 'V'),
    (0x4E0, 'M', u'ӡ'),
    (0x4E1, 'V'),
    (0x4E2, 'M', u'ӣ'),
    (0x4E3, 'V'),
    (0x4E4, 'M', u'ӥ'),
    (0x4E5, 'V'),
    (0x4E6, 'M', u'ӧ'),
    (0x4E7, 'V'),
    (0x4E8, 'M', u'ө'),
    (0x4E9, 'V'),
    (0x4EA, 'M', u'ӫ'),
    (0x4EB, 'V'),
    (0x4EC, 'M', u'ӭ'),
    (0x4ED, 'V'),
    (0x4EE, 'M', u'ӯ'),
    (0x4EF, 'V'),
    (0x4F0, 'M', u'ӱ'),
    (0x4F1, 'V'),
    (0x4F2, 'M', u'ӳ'),
    (0x4F3, 'V'),
    (0x4F4, 'M', u'ӵ'),
    (0x4F5, 'V'),
    (0x4F6, 'M', u'ӷ'),
    (0x4F7, 'V'),
    (0x4F8, 'M', u'ӹ'),
    (0x4F9, 'V'),
    (0x4FA, 'M', u'ӻ'),
    (0x4FB, 'V'),
    (0x4FC, 'M', u'ӽ'),
    (0x4FD, 'V'),
    (0x4FE, 'M', u'ӿ'),
    (0x4FF, 'V'),
    (0x500, 'M', u'ԁ'),
    (0x501, 'V'),
    (0x502, 'M', u'ԃ'),
    ]

def _seg_9():
    return [
    (0x503, 'V'),
    (0x504, 'M', u'ԅ'),
    (0x505, 'V'),
    (0x506, 'M', u'ԇ'),
    (0x507, 'V'),
    (0x508, 'M', u'ԉ'),
    (0x509, 'V'),
    (0x50A, 'M', u'ԋ'),
    (0x50B, 'V'),
    (0x50C, 'M', u'ԍ'),
    (0x50D, 'V'),
    (0x50E, 'M', u'ԏ'),
    (0x50F, 'V'),
    (0x510, 'M', u'ԑ'),
    (0x511, 'V'),
    (0x512, 'M', u'ԓ'),
    (0x513, 'V'),
    (0x514, 'M', u'ԕ'),
    (0x515, 'V'),
    (0x516, 'M', u'ԗ'),
    (0x517, 'V'),
    (0x518, 'M', u'ԙ'),
    (0x519, 'V'),
    (0x51A, 'M', u'ԛ'),
    (0x51B, 'V'),
    (0x51C, 'M', u'ԝ'),
    (0x51D, 'V'),
    (0x51E, 'M', u'ԟ'),
    (0x51F, 'V'),
    (0x520, 'M', u'ԡ'),
    (0x521, 'V'),
    (0x522, 'M', u'ԣ'),
    (0x523, 'V'),
    (0x524, 'M', u'ԥ'),
    (0x525, 'V'),
    (0x526, 'M', u'ԧ'),
    (0x527, 'V'),
    (0x528, 'M', u'ԩ'),
    (0x529, 'V'),
    (0x52A, 'M', u'ԫ'),
    (0x52B, 'V'),
    (0x52C, 'M', u'ԭ'),
    (0x52D, 'V'),
    (0x52E, 'M', u'ԯ'),
    (0x52F, 'V'),
    (0x530, 'X'),
    (0x531, 'M', u'ա'),
    (0x532, 'M', u'բ'),
    (0x533, 'M', u'գ'),
    (0x534, 'M', u'դ'),
    (0x535, 'M', u'ե'),
    (0x536, 'M', u'զ'),
    (0x537, 'M', u'է'),
    (0x538, 'M', u'ը'),
    (0x539, 'M', u'թ'),
    (0x53A, 'M', u'ժ'),
    (0x53B, 'M', u'ի'),
    (0x53C, 'M', u'լ'),
    (0x53D, 'M', u'խ'),
    (0x53E, 'M', u'ծ'),
    (0x53F, 'M', u'կ'),
    (0x540, 'M', u'հ'),
    (0x541, 'M', u'ձ'),
    (0x542, 'M', u'ղ'),
    (0x543, 'M', u'ճ'),
    (0x544, 'M', u'մ'),
    (0x545, 'M', u'յ'),
    (0x546, 'M', u'ն'),
    (0x547, 'M', u'շ'),
    (0x548, 'M', u'ո'),
    (0x549, 'M', u'չ'),
    (0x54A, 'M', u'պ'),
    (0x54B, 'M', u'ջ'),
    (0x54C, 'M', u'ռ'),
    (0x54D, 'M', u'ս'),
    (0x54E, 'M', u'վ'),
    (0x54F, 'M', u'տ'),
    (0x550, 'M', u'ր'),
    (0x551, 'M', u'ց'),
    (0x552, 'M', u'ւ'),
    (0x553, 'M', u'փ'),
    (0x554, 'M', u'ք'),
    (0x555, 'M', u'օ'),
    (0x556, 'M', u'ֆ'),
    (0x557, 'X'),
    (0x559, 'V'),
    (0x587, 'M', u'եւ'),
    (0x588, 'V'),
    (0x58B, 'X'),
    (0x58D, 'V'),
    (0x590, 'X'),
    (0x591, 'V'),
    (0x5C8, 'X'),
    (0x5D0, 'V'),
    (0x5EB, 'X'),
    (0x5EF, 'V'),
    (0x5F5, 'X'),
    (0x606, 'V'),
    (0x61C, 'X'),
    (0x61E, 'V'),
    ]

def _seg_10():
    return [
    (0x675, 'M', u'اٴ'),
    (0x676, 'M', u'وٴ'),
    (0x677, 'M', u'ۇٴ'),
    (0x678, 'M', u'يٴ'),
    (0x679, 'V'),
    (0x6DD, 'X'),
    (0x6DE, 'V'),
    (0x70E, 'X'),
    (0x710, 'V'),
    (0x74B, 'X'),
    (0x74D, 'V'),
    (0x7B2, 'X'),
    (0x7C0, 'V'),
    (0x7FB, 'X'),
    (0x7FD, 'V'),
    (0x82E, 'X'),
    (0x830, 'V'),
    (0x83F, 'X'),
    (0x840, 'V'),
    (0x85C, 'X'),
    (0x85E, 'V'),
    (0x85F, 'X'),
    (0x860, 'V'),
    (0x86B, 'X'),
    (0x8A0, 'V'),
    (0x8B5, 'X'),
    (0x8B6, 'V'),
    (0x8C8, 'X'),
    (0x8D3, 'V'),
    (0x8E2, 'X'),
    (0x8E3, 'V'),
    (0x958, 'M', u'क़'),
    (0x959, 'M', u'ख़'),
    (0x95A, 'M', u'ग़'),
    (0x95B, 'M', u'ज़'),
    (0x95C, 'M', u'ड़'),
    (0x95D, 'M', u'ढ़'),
    (0x95E, 'M', u'फ़'),
    (0x95F, 'M', u'य़'),
    (0x960, 'V'),
    (0x984, 'X'),
    (0x985, 'V'),
    (0x98D, 'X'),
    (0x98F, 'V'),
    (0x991, 'X'),
    (0x993, 'V'),
    (0x9A9, 'X'),
    (0x9AA, 'V'),
    (0x9B1, 'X'),
    (0x9B2, 'V'),
    (0x9B3, 'X'),
    (0x9B6, 'V'),
    (0x9BA, 'X'),
    (0x9BC, 'V'),
    (0x9C5, 'X'),
    (0x9C7, 'V'),
    (0x9C9, 'X'),
    (0x9CB, 'V'),
    (0x9CF, 'X'),
    (0x9D7, 'V'),
    (0x9D8, 'X'),
    (0x9DC, 'M', u'ড়'),
    (0x9DD, 'M', u'ঢ়'),
    (0x9DE, 'X'),
    (0x9DF, 'M', u'য়'),
    (0x9E0, 'V'),
    (0x9E4, 'X'),
    (0x9E6, 'V'),
    (0x9FF, 'X'),
    (0xA01, 'V'),
    (0xA04, 'X'),
    (0xA05, 'V'),
    (0xA0B, 'X'),
    (0xA0F, 'V'),
    (0xA11, 'X'),
    (0xA13, 'V'),
    (0xA29, 'X'),
    (0xA2A, 'V'),
    (0xA31, 'X'),
    (0xA32, 'V'),
    (0xA33, 'M', u'ਲ਼'),
    (0xA34, 'X'),
    (0xA35, 'V'),
    (0xA36, 'M', u'ਸ਼'),
    (0xA37, 'X'),
    (0xA38, 'V'),
    (0xA3A, 'X'),
    (0xA3C, 'V'),
    (0xA3D, 'X'),
    (0xA3E, 'V'),
    (0xA43, 'X'),
    (0xA47, 'V'),
    (0xA49, 'X'),
    (0xA4B, 'V'),
    (0xA4E, 'X'),
    (0xA51, 'V'),
    (0xA52, 'X'),
    (0xA59, 'M', u'ਖ਼'),
    (0xA5A, 'M', u'ਗ਼'),
    (0xA5B, 'M', u'ਜ਼'),
    ]

def _seg_11():
    return [
    (0xA5C, 'V'),
    (0xA5D, 'X'),
    (0xA5E, 'M', u'ਫ਼'),
    (0xA5F, 'X'),
    (0xA66, 'V'),
    (0xA77, 'X'),
    (0xA81, 'V'),
    (0xA84, 'X'),
    (0xA85, 'V'),
    (0xA8E, 'X'),
    (0xA8F, 'V'),
    (0xA92, 'X'),
    (0xA93, 'V'),
    (0xAA9, 'X'),
    (0xAAA, 'V'),
    (0xAB1, 'X'),
    (0xAB2, 'V'),
    (0xAB4, 'X'),
    (0xAB5, 'V'),
    (0xABA, 'X'),
    (0xABC, 'V'),
    (0xAC6, 'X'),
    (0xAC7, 'V'),
    (0xACA, 'X'),
    (0xACB, 'V'),
    (0xACE, 'X'),
    (0xAD0, 'V'),
    (0xAD1, 'X'),
    (0xAE0, 'V'),
    (0xAE4, 'X'),
    (0xAE6, 'V'),
    (0xAF2, 'X'),
    (0xAF9, 'V'),
    (0xB00, 'X'),
    (0xB01, 'V'),
    (0xB04, 'X'),
    (0xB05, 'V'),
    (0xB0D, 'X'),
    (0xB0F, 'V'),
    (0xB11, 'X'),
    (0xB13, 'V'),
    (0xB29, 'X'),
    (0xB2A, 'V'),
    (0xB31, 'X'),
    (0xB32, 'V'),
    (0xB34, 'X'),
    (0xB35, 'V'),
    (0xB3A, 'X'),
    (0xB3C, 'V'),
    (0xB45, 'X'),
    (0xB47, 'V'),
    (0xB49, 'X'),
    (0xB4B, 'V'),
    (0xB4E, 'X'),
    (0xB55, 'V'),
    (0xB58, 'X'),
    (0xB5C, 'M', u'ଡ଼'),
    (0xB5D, 'M', u'ଢ଼'),
    (0xB5E, 'X'),
    (0xB5F, 'V'),
    (0xB64, 'X'),
    (0xB66, 'V'),
    (0xB78, 'X'),
    (0xB82, 'V'),
    (0xB84, 'X'),
    (0xB85, 'V'),
    (0xB8B, 'X'),
    (0xB8E, 'V'),
    (0xB91, 'X'),
    (0xB92, 'V'),
    (0xB96, 'X'),
    (0xB99, 'V'),
    (0xB9B, 'X'),
    (0xB9C, 'V'),
    (0xB9D, 'X'),
    (0xB9E, 'V'),
    (0xBA0, 'X'),
    (0xBA3, 'V'),
    (0xBA5, 'X'),
    (0xBA8, 'V'),
    (0xBAB, 'X'),
    (0xBAE, 'V'),
    (0xBBA, 'X'),
    (0xBBE, 'V'),
    (0xBC3, 'X'),
    (0xBC6, 'V'),
    (0xBC9, 'X'),
    (0xBCA, 'V'),
    (0xBCE, 'X'),
    (0xBD0, 'V'),
    (0xBD1, 'X'),
    (0xBD7, 'V'),
    (0xBD8, 'X'),
    (0xBE6, 'V'),
    (0xBFB, 'X'),
    (0xC00, 'V'),
    (0xC0D, 'X'),
    (0xC0E, 'V'),
    (0xC11, 'X'),
    (0xC12, 'V'),
    ]

def _seg_12():
    return [
    (0xC29, 'X'),
    (0xC2A, 'V'),
    (0xC3A, 'X'),
    (0xC3D, 'V'),
    (0xC45, 'X'),
    (0xC46, 'V'),
    (0xC49, 'X'),
    (0xC4A, 'V'),
    (0xC4E, 'X'),
    (0xC55, 'V'),
    (0xC57, 'X'),
    (0xC58, 'V'),
    (0xC5B, 'X'),
    (0xC60, 'V'),
    (0xC64, 'X'),
    (0xC66, 'V'),
    (0xC70, 'X'),
    (0xC77, 'V'),
    (0xC8D, 'X'),
    (0xC8E, 'V'),
    (0xC91, 'X'),
    (0xC92, 'V'),
    (0xCA9, 'X'),
    (0xCAA, 'V'),
    (0xCB4, 'X'),
    (0xCB5, 'V'),
    (0xCBA, 'X'),
    (0xCBC, 'V'),
    (0xCC5, 'X'),
    (0xCC6, 'V'),
    (0xCC9, 'X'),
    (0xCCA, 'V'),
    (0xCCE, 'X'),
    (0xCD5, 'V'),
    (0xCD7, 'X'),
    (0xCDE, 'V'),
    (0xCDF, 'X'),
    (0xCE0, 'V'),
    (0xCE4, 'X'),
    (0xCE6, 'V'),
    (0xCF0, 'X'),
    (0xCF1, 'V'),
    (0xCF3, 'X'),
    (0xD00, 'V'),
    (0xD0D, 'X'),
    (0xD0E, 'V'),
    (0xD11, 'X'),
    (0xD12, 'V'),
    (0xD45, 'X'),
    (0xD46, 'V'),
    (0xD49, 'X'),
    (0xD4A, 'V'),
    (0xD50, 'X'),
    (0xD54, 'V'),
    (0xD64, 'X'),
    (0xD66, 'V'),
    (0xD80, 'X'),
    (0xD81, 'V'),
    (0xD84, 'X'),
    (0xD85, 'V'),
    (0xD97, 'X'),
    (0xD9A, 'V'),
    (0xDB2, 'X'),
    (0xDB3, 'V'),
    (0xDBC, 'X'),
    (0xDBD, 'V'),
    (0xDBE, 'X'),
    (0xDC0, 'V'),
    (0xDC7, 'X'),
    (0xDCA, 'V'),
    (0xDCB, 'X'),
    (0xDCF, 'V'),
    (0xDD5, 'X'),
    (0xDD6, 'V'),
    (0xDD7, 'X'),
    (0xDD8, 'V'),
    (0xDE0, 'X'),
    (0xDE6, 'V'),
    (0xDF0, 'X'),
    (0xDF2, 'V'),
    (0xDF5, 'X'),
    (0xE01, 'V'),
    (0xE33, 'M', u'ํา'),
    (0xE34, 'V'),
    (0xE3B, 'X'),
    (0xE3F, 'V'),
    (0xE5C, 'X'),
    (0xE81, 'V'),
    (0xE83, 'X'),
    (0xE84, 'V'),
    (0xE85, 'X'),
    (0xE86, 'V'),
    (0xE8B, 'X'),
    (0xE8C, 'V'),
    (0xEA4, 'X'),
    (0xEA5, 'V'),
    (0xEA6, 'X'),
    (0xEA7, 'V'),
    (0xEB3, 'M', u'ໍາ'),
    (0xEB4, 'V'),
    ]

def _seg_13():
    return [
    (0xEBE, 'X'),
    (0xEC0, 'V'),
    (0xEC5, 'X'),
    (0xEC6, 'V'),
    (0xEC7, 'X'),
    (0xEC8, 'V'),
    (0xECE, 'X'),
    (0xED0, 'V'),
    (0xEDA, 'X'),
    (0xEDC, 'M', u'ຫນ'),
    (0xEDD, 'M', u'ຫມ'),
    (0xEDE, 'V'),
    (0xEE0, 'X'),
    (0xF00, 'V'),
    (0xF0C, 'M', u'་'),
    (0xF0D, 'V'),
    (0xF43, 'M', u'གྷ'),
    (0xF44, 'V'),
    (0xF48, 'X'),
    (0xF49, 'V'),
    (0xF4D, 'M', u'ཌྷ'),
    (0xF4E, 'V'),
    (0xF52, 'M', u'དྷ'),
    (0xF53, 'V'),
    (0xF57, 'M', u'བྷ'),
    (0xF58, 'V'),
    (0xF5C, 'M', u'ཛྷ'),
    (0xF5D, 'V'),
    (0xF69, 'M', u'ཀྵ'),
    (0xF6A, 'V'),
    (0xF6D, 'X'),
    (0xF71, 'V'),
    (0xF73, 'M', u'ཱི'),
    (0xF74, 'V'),
    (0xF75, 'M', u'ཱུ'),
    (0xF76, 'M', u'ྲྀ'),
    (0xF77, 'M', u'ྲཱྀ'),
    (0xF78, 'M', u'ླྀ'),
    (0xF79, 'M', u'ླཱྀ'),
    (0xF7A, 'V'),
    (0xF81, 'M', u'ཱྀ'),
    (0xF82, 'V'),
    (0xF93, 'M', u'ྒྷ'),
    (0xF94, 'V'),
    (0xF98, 'X'),
    (0xF99, 'V'),
    (0xF9D, 'M', u'ྜྷ'),
    (0xF9E, 'V'),
    (0xFA2, 'M', u'ྡྷ'),
    (0xFA3, 'V'),
    (0xFA7, 'M', u'ྦྷ'),
    (0xFA8, 'V'),
    (0xFAC, 'M', u'ྫྷ'),
    (0xFAD, 'V'),
    (0xFB9, 'M', u'ྐྵ'),
    (0xFBA, 'V'),
    (0xFBD, 'X'),
    (0xFBE, 'V'),
    (0xFCD, 'X'),
    (0xFCE, 'V'),
    (0xFDB, 'X'),
    (0x1000, 'V'),
    (0x10A0, 'X'),
    (0x10C7, 'M', u'ⴧ'),
    (0x10C8, 'X'),
    (0x10CD, 'M', u'ⴭ'),
    (0x10CE, 'X'),
    (0x10D0, 'V'),
    (0x10FC, 'M', u'ნ'),
    (0x10FD, 'V'),
    (0x115F, 'X'),
    (0x1161, 'V'),
    (0x1249, 'X'),
    (0x124A, 'V'),
    (0x124E, 'X'),
    (0x1250, 'V'),
    (0x1257, 'X'),
    (0x1258, 'V'),
    (0x1259, 'X'),
    (0x125A, 'V'),
    (0x125E, 'X'),
    (0x1260, 'V'),
    (0x1289, 'X'),
    (0x128A, 'V'),
    (0x128E, 'X'),
    (0x1290, 'V'),
    (0x12B1, 'X'),
    (0x12B2, 'V'),
    (0x12B6, 'X'),
    (0x12B8, 'V'),
    (0x12BF, 'X'),
    (0x12C0, 'V'),
    (0x12C1, 'X'),
    (0x12C2, 'V'),
    (0x12C6, 'X'),
    (0x12C8, 'V'),
    (0x12D7, 'X'),
    (0x12D8, 'V'),
    (0x1311, 'X'),
    (0x1312, 'V'),
    ]

def _seg_14():
    return [
    (0x1316, 'X'),
    (0x1318, 'V'),
    (0x135B, 'X'),
    (0x135D, 'V'),
    (0x137D, 'X'),
    (0x1380, 'V'),
    (0x139A, 'X'),
    (0x13A0, 'V'),
    (0x13F6, 'X'),
    (0x13F8, 'M', u'Ᏸ'),
    (0x13F9, 'M', u'Ᏹ'),
    (0x13FA, 'M', u'Ᏺ'),
    (0x13FB, 'M', u'Ᏻ'),
    (0x13FC, 'M', u'Ᏼ'),
    (0x13FD, 'M', u'Ᏽ'),
    (0x13FE, 'X'),
    (0x1400, 'V'),
    (0x1680, 'X'),
    (0x1681, 'V'),
    (0x169D, 'X'),
    (0x16A0, 'V'),
    (0x16F9, 'X'),
    (0x1700, 'V'),
    (0x170D, 'X'),
    (0x170E, 'V'),
    (0x1715, 'X'),
    (0x1720, 'V'),
    (0x1737, 'X'),
    (0x1740, 'V'),
    (0x1754, 'X'),
    (0x1760, 'V'),
    (0x176D, 'X'),
    (0x176E, 'V'),
    (0x1771, 'X'),
    (0x1772, 'V'),
    (0x1774, 'X'),
    (0x1780, 'V'),
    (0x17B4, 'X'),
    (0x17B6, 'V'),
    (0x17DE, 'X'),
    (0x17E0, 'V'),
    (0x17EA, 'X'),
    (0x17F0, 'V'),
    (0x17FA, 'X'),
    (0x1800, 'V'),
    (0x1806, 'X'),
    (0x1807, 'V'),
    (0x180B, 'I'),
    (0x180E, 'X'),
    (0x1810, 'V'),
    (0x181A, 'X'),
    (0x1820, 'V'),
    (0x1879, 'X'),
    (0x1880, 'V'),
    (0x18AB, 'X'),
    (0x18B0, 'V'),
    (0x18F6, 'X'),
    (0x1900, 'V'),
    (0x191F, 'X'),
    (0x1920, 'V'),
    (0x192C, 'X'),
    (0x1930, 'V'),
    (0x193C, 'X'),
    (0x1940, 'V'),
    (0x1941, 'X'),
    (0x1944, 'V'),
    (0x196E, 'X'),
    (0x1970, 'V'),
    (0x1975, 'X'),
    (0x1980, 'V'),
    (0x19AC, 'X'),
    (0x19B0, 'V'),
    (0x19CA, 'X'),
    (0x19D0, 'V'),
    (0x19DB, 'X'),
    (0x19DE, 'V'),
    (0x1A1C, 'X'),
    (0x1A1E, 'V'),
    (0x1A5F, 'X'),
    (0x1A60, 'V'),
    (0x1A7D, 'X'),
    (0x1A7F, 'V'),
    (0x1A8A, 'X'),
    (0x1A90, 'V'),
    (0x1A9A, 'X'),
    (0x1AA0, 'V'),
    (0x1AAE, 'X'),
    (0x1AB0, 'V'),
    (0x1AC1, 'X'),
    (0x1B00, 'V'),
    (0x1B4C, 'X'),
    (0x1B50, 'V'),
    (0x1B7D, 'X'),
    (0x1B80, 'V'),
    (0x1BF4, 'X'),
    (0x1BFC, 'V'),
    (0x1C38, 'X'),
    (0x1C3B, 'V'),
    (0x1C4A, 'X'),
    (0x1C4D, 'V'),
    ]

def _seg_15():
    return [
    (0x1C80, 'M', u'в'),
    (0x1C81, 'M', u'д'),
    (0x1C82, 'M', u'о'),
    (0x1C83, 'M', u'с'),
    (0x1C84, 'M', u'т'),
    (0x1C86, 'M', u'ъ'),
    (0x1C87, 'M', u'ѣ'),
    (0x1C88, 'M', u'ꙋ'),
    (0x1C89, 'X'),
    (0x1C90, 'M', u'ა'),
    (0x1C91, 'M', u'ბ'),
    (0x1C92, 'M', u'გ'),
    (0x1C93, 'M', u'დ'),
    (0x1C94, 'M', u'ე'),
    (0x1C95, 'M', u'ვ'),
    (0x1C96, 'M', u'ზ'),
    (0x1C97, 'M', u'თ'),
    (0x1C98, 'M', u'ი'),
    (0x1C99, 'M', u'კ'),
    (0x1C9A, 'M', u'ლ'),
    (0x1C9B, 'M', u'მ'),
    (0x1C9C, 'M', u'ნ'),
    (0x1C9D, 'M', u'ო'),
    (0x1C9E, 'M', u'პ'),
    (0x1C9F, 'M', u'ჟ'),
    (0x1CA0, 'M', u'რ'),
    (0x1CA1, 'M', u'ს'),
    (0x1CA2, 'M', u'ტ'),
    (0x1CA3, 'M', u'უ'),
    (0x1CA4, 'M', u'ფ'),
    (0x1CA5, 'M', u'ქ'),
    (0x1CA6, 'M', u'ღ'),
    (0x1CA7, 'M', u'ყ'),
    (0x1CA8, 'M', u'შ'),
    (0x1CA9, 'M', u'ჩ'),
    (0x1CAA, 'M', u'ც'),
    (0x1CAB, 'M', u'ძ'),
    (0x1CAC, 'M', u'წ'),
    (0x1CAD, 'M', u'ჭ'),
    (0x1CAE, 'M', u'ხ'),
    (0x1CAF, 'M', u'ჯ'),
    (0x1CB0, 'M', u'ჰ'),
    (0x1CB1, 'M', u'ჱ'),
    (0x1CB2, 'M', u'ჲ'),
    (0x1CB3, 'M', u'ჳ'),
    (0x1CB4, 'M', u'ჴ'),
    (0x1CB5, 'M', u'ჵ'),
    (0x1CB6, 'M', u'ჶ'),
    (0x1CB7, 'M', u'ჷ'),
    (0x1CB8, 'M', u'ჸ'),
    (0x1CB9, 'M', u'ჹ'),
    (0x1CBA, 'M', u'ჺ'),
    (0x1CBB, 'X'),
    (0x1CBD, 'M', u'ჽ'),
    (0x1CBE, 'M', u'ჾ'),
    (0x1CBF, 'M', u'ჿ'),
    (0x1CC0, 'V'),
    (0x1CC8, 'X'),
    (0x1CD0, 'V'),
    (0x1CFB, 'X'),
    (0x1D00, 'V'),
    (0x1D2C, 'M', u'a'),
    (0x1D2D, 'M', u'æ'),
    (0x1D2E, 'M', u'b'),
    (0x1D2F, 'V'),
    (0x1D30, 'M', u'd'),
    (0x1D31, 'M', u'e'),
    (0x1D32, 'M', u'ǝ'),
    (0x1D33, 'M', u'g'),
    (0x1D34, 'M', u'h'),
    (0x1D35, 'M', u'i'),
    (0x1D36, 'M', u'j'),
    (0x1D37, 'M', u'k'),
    (0x1D38, 'M', u'l'),
    (0x1D39, 'M', u'm'),
    (0x1D3A, 'M', u'n'),
    (0x1D3B, 'V'),
    (0x1D3C, 'M', u'o'),
    (0x1D3D, 'M', u'ȣ'),
    (0x1D3E, 'M', u'p'),
    (0x1D3F, 'M', u'r'),
    (0x1D40, 'M', u't'),
    (0x1D41, 'M', u'u'),
    (0x1D42, 'M', u'w'),
    (0x1D43, 'M', u'a'),
    (0x1D44, 'M', u'ɐ'),
    (0x1D45, 'M', u'ɑ'),
    (0x1D46, 'M', u'ᴂ'),
    (0x1D47, 'M', u'b'),
    (0x1D48, 'M', u'd'),
    (0x1D49, 'M', u'e'),
    (0x1D4A, 'M', u'ə'),
    (0x1D4B, 'M', u'ɛ'),
    (0x1D4C, 'M', u'ɜ'),
    (0x1D4D, 'M', u'g'),
    (0x1D4E, 'V'),
    (0x1D4F, 'M', u'k'),
    (0x1D50, 'M', u'm'),
    (0x1D51, 'M', u'ŋ'),
    (0x1D52, 'M', u'o'),
    ]

def _seg_16():
    return [
    (0x1D53, 'M', u'ɔ'),
    (0x1D54, 'M', u'ᴖ'),
    (0x1D55, 'M', u'ᴗ'),
    (0x1D56, 'M', u'p'),
    (0x1D57, 'M', u't'),
    (0x1D58, 'M', u'u'),
    (0x1D59, 'M', u'ᴝ'),
    (0x1D5A, 'M', u'ɯ'),
    (0x1D5B, 'M', u'v'),
    (0x1D5C, 'M', u'ᴥ'),
    (0x1D5D, 'M', u'β'),
    (0x1D5E, 'M', u'γ'),
    (0x1D5F, 'M', u'δ'),
    (0x1D60, 'M', u'φ'),
    (0x1D61, 'M', u'χ'),
    (0x1D62, 'M', u'i'),
    (0x1D63, 'M', u'r'),
    (0x1D64, 'M', u'u'),
    (0x1D65, 'M', u'v'),
    (0x1D66, 'M', u'β'),
    (0x1D67, 'M', u'γ'),
    (0x1D68, 'M', u'ρ'),
    (0x1D69, 'M', u'φ'),
    (0x1D6A, 'M', u'χ'),
    (0x1D6B, 'V'),
    (0x1D78, 'M', u'н'),
    (0x1D79, 'V'),
    (0x1D9B, 'M', u'ɒ'),
    (0x1D9C, 'M', u'c'),
    (0x1D9D, 'M', u'ɕ'),
    (0x1D9E, 'M', u'ð'),
    (0x1D9F, 'M', u'ɜ'),
    (0x1DA0, 'M', u'f'),
    (0x1DA1, 'M', u'ɟ'),
    (0x1DA2, 'M', u'ɡ'),
    (0x1DA3, 'M', u'ɥ'),
    (0x1DA4, 'M', u'ɨ'),
    (0x1DA5, 'M', u'ɩ'),
    (0x1DA6, 'M', u'ɪ'),
    (0x1DA7, 'M', u'ᵻ'),
    (0x1DA8, 'M', u'ʝ'),
    (0x1DA9, 'M', u'ɭ'),
    (0x1DAA, 'M', u'ᶅ'),
    (0x1DAB, 'M', u'ʟ'),
    (0x1DAC, 'M', u'ɱ'),
    (0x1DAD, 'M', u'ɰ'),
    (0x1DAE, 'M', u'ɲ'),
    (0x1DAF, 'M', u'ɳ'),
    (0x1DB0, 'M', u'ɴ'),
    (0x1DB1, 'M', u'ɵ'),
    (0x1DB2, 'M', u'ɸ'),
    (0x1DB3, 'M', u'ʂ'),
    (0x1DB4, 'M', u'ʃ'),
    (0x1DB5, 'M', u'ƫ'),
    (0x1DB6, 'M', u'ʉ'),
    (0x1DB7, 'M', u'ʊ'),
    (0x1DB8, 'M', u'ᴜ'),
    (0x1DB9, 'M', u'ʋ'),
    (0x1DBA, 'M', u'ʌ'),
    (0x1DBB, 'M', u'z'),
    (0x1DBC, 'M', u'ʐ'),
    (0x1DBD, 'M', u'ʑ'),
    (0x1DBE, 'M', u'ʒ'),
    (0x1DBF, 'M', u'θ'),
    (0x1DC0, 'V'),
    (0x1DFA, 'X'),
    (0x1DFB, 'V'),
    (0x1E00, 'M', u'ḁ'),
    (0x1E01, 'V'),
    (0x1E02, 'M', u'ḃ'),
    (0x1E03, 'V'),
    (0x1E04, 'M', u'ḅ'),
    (0x1E05, 'V'),
    (0x1E06, 'M', u'ḇ'),
    (0x1E07, 'V'),
    (0x1E08, 'M', u'ḉ'),
    (0x1E09, 'V'),
    (0x1E0A, 'M', u'ḋ'),
    (0x1E0B, 'V'),
    (0x1E0C, 'M', u'ḍ'),
    (0x1E0D, 'V'),
    (0x1E0E, 'M', u'ḏ'),
    (0x1E0F, 'V'),
    (0x1E10, 'M', u'ḑ'),
    (0x1E11, 'V'),
    (0x1E12, 'M', u'ḓ'),
    (0x1E13, 'V'),
    (0x1E14, 'M', u'ḕ'),
    (0x1E15, 'V'),
    (0x1E16, 'M', u'ḗ'),
    (0x1E17, 'V'),
    (0x1E18, 'M', u'ḙ'),
    (0x1E19, 'V'),
    (0x1E1A, 'M', u'ḛ'),
    (0x1E1B, 'V'),
    (0x1E1C, 'M', u'ḝ'),
    (0x1E1D, 'V'),
    (0x1E1E, 'M', u'ḟ'),
    (0x1E1F, 'V'),
    (0x1E20, 'M', u'ḡ'),
    ]

def _seg_17():
    return [
    (0x1E21, 'V'),
    (0x1E22, 'M', u'ḣ'),
    (0x1E23, 'V'),
    (0x1E24, 'M', u'ḥ'),
    (0x1E25, 'V'),
    (0x1E26, 'M', u'ḧ'),
    (0x1E27, 'V'),
    (0x1E28, 'M', u'ḩ'),
    (0x1E29, 'V'),
    (0x1E2A, 'M', u'ḫ'),
    (0x1E2B, 'V'),
    (0x1E2C, 'M', u'ḭ'),
    (0x1E2D, 'V'),
    (0x1E2E, 'M', u'ḯ'),
    (0x1E2F, 'V'),
    (0x1E30, 'M', u'ḱ'),
    (0x1E31, 'V'),
    (0x1E32, 'M', u'ḳ'),
    (0x1E33, 'V'),
    (0x1E34, 'M', u'ḵ'),
    (0x1E35, 'V'),
    (0x1E36, 'M', u'ḷ'),
    (0x1E37, 'V'),
    (0x1E38, 'M', u'ḹ'),
    (0x1E39, 'V'),
    (0x1E3A, 'M', u'ḻ'),
    (0x1E3B, 'V'),
    (0x1E3C, 'M', u'ḽ'),
    (0x1E3D, 'V'),
    (0x1E3E, 'M', u'ḿ'),
    (0x1E3F, 'V'),
    (0x1E40, 'M', u'ṁ'),
    (0x1E41, 'V'),
    (0x1E42, 'M', u'ṃ'),
    (0x1E43, 'V'),
    (0x1E44, 'M', u'ṅ'),
    (0x1E45, 'V'),
    (0x1E46, 'M', u'ṇ'),
    (0x1E47, 'V'),
    (0x1E48, 'M', u'ṉ'),
    (0x1E49, 'V'),
    (0x1E4A, 'M', u'ṋ'),
    (0x1E4B, 'V'),
    (0x1E4C, 'M', u'ṍ'),
    (0x1E4D, 'V'),
    (0x1E4E, 'M', u'ṏ'),
    (0x1E4F, 'V'),
    (0x1E50, 'M', u'ṑ'),
    (0x1E51, 'V'),
    (0x1E52, 'M', u'ṓ'),
    (0x1E53, 'V'),
    (0x1E54, 'M', u'ṕ'),
    (0x1E55, 'V'),
    (0x1E56, 'M', u'ṗ'),
    (0x1E57, 'V'),
    (0x1E58, 'M', u'ṙ'),
    (0x1E59, 'V'),
    (0x1E5A, 'M', u'ṛ'),
    (0x1E5B, 'V'),
    (0x1E5C, 'M', u'ṝ'),
    (0x1E5D, 'V'),
    (0x1E5E, 'M', u'ṟ'),
    (0x1E5F, 'V'),
    (0x1E60, 'M', u'ṡ'),
    (0x1E61, 'V'),
    (0x1E62, 'M', u'ṣ'),
    (0x1E63, 'V'),
    (0x1E64, 'M', u'ṥ'),
    (0x1E65, 'V'),
    (0x1E66, 'M', u'ṧ'),
    (0x1E67, 'V'),
    (0x1E68, 'M', u'ṩ'),
    (0x1E69, 'V'),
    (0x1E6A, 'M', u'ṫ'),
    (0x1E6B, 'V'),
    (0x1E6C, 'M', u'ṭ'),
    (0x1E6D, 'V'),
    (0x1E6E, 'M', u'ṯ'),
    (0x1E6F, 'V'),
    (0x1E70, 'M', u'ṱ'),
    (0x1E71, 'V'),
    (0x1E72, 'M', u'ṳ'),
    (0x1E73, 'V'),
    (0x1E74, 'M', u'ṵ'),
    (0x1E75, 'V'),
    (0x1E76, 'M', u'ṷ'),
    (0x1E77, 'V'),
    (0x1E78, 'M', u'ṹ'),
    (0x1E79, 'V'),
    (0x1E7A, 'M', u'ṻ'),
    (0x1E7B, 'V'),
    (0x1E7C, 'M', u'ṽ'),
    (0x1E7D, 'V'),
    (0x1E7E, 'M', u'ṿ'),
    (0x1E7F, 'V'),
    (0x1E80, 'M', u'ẁ'),
    (0x1E81, 'V'),
    (0x1E82, 'M', u'ẃ'),
    (0x1E83, 'V'),
    (0x1E84, 'M', u'ẅ'),
    ]

def _seg_18():
    return [
    (0x1E85, 'V'),
    (0x1E86, 'M', u'ẇ'),
    (0x1E87, 'V'),
    (0x1E88, 'M', u'ẉ'),
    (0x1E89, 'V'),
    (0x1E8A, 'M', u'ẋ'),
    (0x1E8B, 'V'),
    (0x1E8C, 'M', u'ẍ'),
    (0x1E8D, 'V'),
    (0x1E8E, 'M', u'ẏ'),
    (0x1E8F, 'V'),
    (0x1E90, 'M', u'ẑ'),
    (0x1E91, 'V'),
    (0x1E92, 'M', u'ẓ'),
    (0x1E93, 'V'),
    (0x1E94, 'M', u'ẕ'),
    (0x1E95, 'V'),
    (0x1E9A, 'M', u'aʾ'),
    (0x1E9B, 'M', u'ṡ'),
    (0x1E9C, 'V'),
    (0x1E9E, 'M', u'ss'),
    (0x1E9F, 'V'),
    (0x1EA0, 'M', u'ạ'),
    (0x1EA1, 'V'),
    (0x1EA2, 'M', u'ả'),
    (0x1EA3, 'V'),
    (0x1EA4, 'M', u'ấ'),
    (0x1EA5, 'V'),
    (0x1EA6, 'M', u'ầ'),
    (0x1EA7, 'V'),
    (0x1EA8, 'M', u'ẩ'),
    (0x1EA9, 'V'),
    (0x1EAA, 'M', u'ẫ'),
    (0x1EAB, 'V'),
    (0x1EAC, 'M', u'ậ'),
    (0x1EAD, 'V'),
    (0x1EAE, 'M', u'ắ'),
    (0x1EAF, 'V'),
    (0x1EB0, 'M', u'ằ'),
    (0x1EB1, 'V'),
    (0x1EB2, 'M', u'ẳ'),
    (0x1EB3, 'V'),
    (0x1EB4, 'M', u'ẵ'),
    (0x1EB5, 'V'),
    (0x1EB6, 'M', u'ặ'),
    (0x1EB7, 'V'),
    (0x1EB8, 'M', u'ẹ'),
    (0x1EB9, 'V'),
    (0x1EBA, 'M', u'ẻ'),
    (0x1EBB, 'V'),
    (0x1EBC, 'M', u'ẽ'),
    (0x1EBD, 'V'),
    (0x1EBE, 'M', u'ế'),
    (0x1EBF, 'V'),
    (0x1EC0, 'M', u'ề'),
    (0x1EC1, 'V'),
    (0x1EC2, 'M', u'ể'),
    (0x1EC3, 'V'),
    (0x1EC4, 'M', u'ễ'),
    (0x1EC5, 'V'),
    (0x1EC6, 'M', u'ệ'),
    (0x1EC7, 'V'),
    (0x1EC8, 'M', u'ỉ'),
    (0x1EC9, 'V'),
    (0x1ECA, 'M', u'ị'),
    (0x1ECB, 'V'),
    (0x1ECC, 'M', u'ọ'),
    (0x1ECD, 'V'),
    (0x1ECE, 'M', u'ỏ'),
    (0x1ECF, 'V'),
    (0x1ED0, 'M', u'ố'),
    (0x1ED1, 'V'),
    (0x1ED2, 'M', u'ồ'),
    (0x1ED3, 'V'),
    (0x1ED4, 'M', u'ổ'),
    (0x1ED5, 'V'),
    (0x1ED6, 'M', u'ỗ'),
    (0x1ED7, 'V'),
    (0x1ED8, 'M', u'ộ'),
    (0x1ED9, 'V'),
    (0x1EDA, 'M', u'ớ'),
    (0x1EDB, 'V'),
    (0x1EDC, 'M', u'ờ'),
    (0x1EDD, 'V'),
    (0x1EDE, 'M', u'ở'),
    (0x1EDF, 'V'),
    (0x1EE0, 'M', u'ỡ'),
    (0x1EE1, 'V'),
    (0x1EE2, 'M', u'ợ'),
    (0x1EE3, 'V'),
    (0x1EE4, 'M', u'ụ'),
    (0x1EE5, 'V'),
    (0x1EE6, 'M', u'ủ'),
    (0x1EE7, 'V'),
    (0x1EE8, 'M', u'ứ'),
    (0x1EE9, 'V'),
    (0x1EEA, 'M', u'ừ'),
    (0x1EEB, 'V'),
    (0x1EEC, 'M', u'ử'),
    (0x1EED, 'V'),
    ]

def _seg_19():
    return [
    (0x1EEE, 'M', u'ữ'),
    (0x1EEF, 'V'),
    (0x1EF0, 'M', u'ự'),
    (0x1EF1, 'V'),
    (0x1EF2, 'M', u'ỳ'),
    (0x1EF3, 'V'),
    (0x1EF4, 'M', u'ỵ'),
    (0x1EF5, 'V'),
    (0x1EF6, 'M', u'ỷ'),
    (0x1EF7, 'V'),
    (0x1EF8, 'M', u'ỹ'),
    (0x1EF9, 'V'),
    (0x1EFA, 'M', u'ỻ'),
    (0x1EFB, 'V'),
    (0x1EFC, 'M', u'ỽ'),
    (0x1EFD, 'V'),
    (0x1EFE, 'M', u'ỿ'),
    (0x1EFF, 'V'),
    (0x1F08, 'M', u'ἀ'),
    (0x1F09, 'M', u'ἁ'),
    (0x1F0A, 'M', u'ἂ'),
    (0x1F0B, 'M', u'ἃ'),
    (0x1F0C, 'M', u'ἄ'),
    (0x1F0D, 'M', u'ἅ'),
    (0x1F0E, 'M', u'ἆ'),
    (0x1F0F, 'M', u'ἇ'),
    (0x1F10, 'V'),
    (0x1F16, 'X'),
    (0x1F18, 'M', u'ἐ'),
    (0x1F19, 'M', u'ἑ'),
    (0x1F1A, 'M', u'ἒ'),
    (0x1F1B, 'M', u'ἓ'),
    (0x1F1C, 'M', u'ἔ'),
    (0x1F1D, 'M', u'ἕ'),
    (0x1F1E, 'X'),
    (0x1F20, 'V'),
    (0x1F28, 'M', u'ἠ'),
    (0x1F29, 'M', u'ἡ'),
    (0x1F2A, 'M', u'ἢ'),
    (0x1F2B, 'M', u'ἣ'),
    (0x1F2C, 'M', u'ἤ'),
    (0x1F2D, 'M', u'ἥ'),
    (0x1F2E, 'M', u'ἦ'),
    (0x1F2F, 'M', u'ἧ'),
    (0x1F30, 'V'),
    (0x1F38, 'M', u'ἰ'),
    (0x1F39, 'M', u'ἱ'),
    (0x1F3A, 'M', u'ἲ'),
    (0x1F3B, 'M', u'ἳ'),
    (0x1F3C, 'M', u'ἴ'),
    (0x1F3D, 'M', u'ἵ'),
    (0x1F3E, 'M', u'ἶ'),
    (0x1F3F, 'M', u'ἷ'),
    (0x1F40, 'V'),
    (0x1F46, 'X'),
    (0x1F48, 'M', u'ὀ'),
    (0x1F49, 'M', u'ὁ'),
    (0x1F4A, 'M', u'ὂ'),
    (0x1F4B, 'M', u'ὃ'),
    (0x1F4C, 'M', u'ὄ'),
    (0x1F4D, 'M', u'ὅ'),
    (0x1F4E, 'X'),
    (0x1F50, 'V'),
    (0x1F58, 'X'),
    (0x1F59, 'M', u'ὑ'),
    (0x1F5A, 'X'),
    (0x1F5B, 'M', u'ὓ'),
    (0x1F5C, 'X'),
    (0x1F5D, 'M', u'ὕ'),
    (0x1F5E, 'X'),
    (0x1F5F, 'M', u'ὗ'),
    (0x1F60, 'V'),
    (0x1F68, 'M', u'ὠ'),
    (0x1F69, 'M', u'ὡ'),
    (0x1F6A, 'M', u'ὢ'),
    (0x1F6B, 'M', u'ὣ'),
    (0x1F6C, 'M', u'ὤ'),
    (0x1F6D, 'M', u'ὥ'),
    (0x1F6E, 'M', u'ὦ'),
    (0x1F6F, 'M', u'ὧ'),
    (0x1F70, 'V'),
    (0x1F71, 'M', u'ά'),
    (0x1F72, 'V'),
    (0x1F73, 'M', u'έ'),
    (0x1F74, 'V'),
    (0x1F75, 'M', u'ή'),
    (0x1F76, 'V'),
    (0x1F77, 'M', u'ί'),
    (0x1F78, 'V'),
    (0x1F79, 'M', u'ό'),
    (0x1F7A, 'V'),
    (0x1F7B, 'M', u'ύ'),
    (0x1F7C, 'V'),
    (0x1F7D, 'M', u'ώ'),
    (0x1F7E, 'X'),
    (0x1F80, 'M', u'ἀι'),
    (0x1F81, 'M', u'ἁι'),
    (0x1F82, 'M', u'ἂι'),
    (0x1F83, 'M', u'ἃι'),
    (0x1F84, 'M', u'ἄι'),
    ]

def _seg_20():
    return [
    (0x1F85, 'M', u'ἅι'),
    (0x1F86, 'M', u'ἆι'),
    (0x1F87, 'M', u'ἇι'),
    (0x1F88, 'M', u'ἀι'),
    (0x1F89, 'M', u'ἁι'),
    (0x1F8A, 'M', u'ἂι'),
    (0x1F8B, 'M', u'ἃι'),
    (0x1F8C, 'M', u'ἄι'),
    (0x1F8D, 'M', u'ἅι'),
    (0x1F8E, 'M', u'ἆι'),
    (0x1F8F, 'M', u'ἇι'),
    (0x1F90, 'M', u'ἠι'),
    (0x1F91, 'M', u'ἡι'),
    (0x1F92, 'M', u'ἢι'),
    (0x1F93, 'M', u'ἣι'),
    (0x1F94, 'M', u'ἤι'),
    (0x1F95, 'M', u'ἥι'),
    (0x1F96, 'M', u'ἦι'),
    (0x1F97, 'M', u'ἧι'),
    (0x1F98, 'M', u'ἠι'),
    (0x1F99, 'M', u'ἡι'),
    (0x1F9A, 'M', u'ἢι'),
    (0x1F9B, 'M', u'ἣι'),
    (0x1F9C, 'M', u'ἤι'),
    (0x1F9D, 'M', u'ἥι'),
    (0x1F9E, 'M', u'ἦι'),
    (0x1F9F, 'M', u'ἧι'),
    (0x1FA0, 'M', u'ὠι'),
    (0x1FA1, 'M', u'ὡι'),
    (0x1FA2, 'M', u'ὢι'),
    (0x1FA3, 'M', u'ὣι'),
    (0x1FA4, 'M', u'ὤι'),
    (0x1FA5, 'M', u'ὥι'),
    (0x1FA6, 'M', u'ὦι'),
    (0x1FA7, 'M', u'ὧι'),
    (0x1FA8, 'M', u'ὠι'),
    (0x1FA9, 'M', u'ὡι'),
    (0x1FAA, 'M', u'ὢι'),
    (0x1FAB, 'M', u'ὣι'),
    (0x1FAC, 'M', u'ὤι'),
    (0x1FAD, 'M', u'ὥι'),
    (0x1FAE, 'M', u'ὦι'),
    (0x1FAF, 'M', u'ὧι'),
    (0x1FB0, 'V'),
    (0x1FB2, 'M', u'ὰι'),
    (0x1FB3, 'M', u'αι'),
    (0x1FB4, 'M', u'άι'),
    (0x1FB5, 'X'),
    (0x1FB6, 'V'),
    (0x1FB7, 'M', u'ᾶι'),
    (0x1FB8, 'M', u'ᾰ'),
    (0x1FB9, 'M', u'ᾱ'),
    (0x1FBA, 'M', u'ὰ'),
    (0x1FBB, 'M', u'ά'),
    (0x1FBC, 'M', u'αι'),
    (0x1FBD, '3', u' ̓'),
    (0x1FBE, 'M', u'ι'),
    (0x1FBF, '3', u' ̓'),
    (0x1FC0, '3', u' ͂'),
    (0x1FC1, '3', u' ̈͂'),
    (0x1FC2, 'M', u'ὴι'),
    (0x1FC3, 'M', u'ηι'),
    (0x1FC4, 'M', u'ήι'),
    (0x1FC5, 'X'),
    (0x1FC6, 'V'),
    (0x1FC7, 'M', u'ῆι'),
    (0x1FC8, 'M', u'ὲ'),
    (0x1FC9, 'M', u'έ'),
    (0x1FCA, 'M', u'ὴ'),
    (0x1FCB, 'M', u'ή'),
    (0x1FCC, 'M', u'ηι'),
    (0x1FCD, '3', u' ̓̀'),
    (0x1FCE, '3', u' ̓́'),
    (0x1FCF, '3', u' ̓͂'),
    (0x1FD0, 'V'),
    (0x1FD3, 'M', u'ΐ'),
    (0x1FD4, 'X'),
    (0x1FD6, 'V'),
    (0x1FD8, 'M', u'ῐ'),
    (0x1FD9, 'M', u'ῑ'),
    (0x1FDA, 'M', u'ὶ'),
    (0x1FDB, 'M', u'ί'),
    (0x1FDC, 'X'),
    (0x1FDD, '3', u' ̔̀'),
    (0x1FDE, '3', u' ̔́'),
    (0x1FDF, '3', u' ̔͂'),
    (0x1FE0, 'V'),
    (0x1FE3, 'M', u'ΰ'),
    (0x1FE4, 'V'),
    (0x1FE8, 'M', u'ῠ'),
    (0x1FE9, 'M', u'ῡ'),
    (0x1FEA, 'M', u'ὺ'),
    (0x1FEB, 'M', u'ύ'),
    (0x1FEC, 'M', u'ῥ'),
    (0x1FED, '3', u' ̈̀'),
    (0x1FEE, '3', u' ̈́'),
    (0x1FEF, '3', u'`'),
    (0x1FF0, 'X'),
    (0x1FF2, 'M', u'ὼι'),
    (0x1FF3, 'M', u'ωι'),
    ]

def _seg_21():
    return [
    (0x1FF4, 'M', u'ώι'),
    (0x1FF5, 'X'),
    (0x1FF6, 'V'),
    (0x1FF7, 'M', u'ῶι'),
    (0x1FF8, 'M', u'ὸ'),
    (0x1FF9, 'M', u'ό'),
    (0x1FFA, 'M', u'ὼ'),
    (0x1FFB, 'M', u'ώ'),
    (0x1FFC, 'M', u'ωι'),
    (0x1FFD, '3', u' ́'),
    (0x1FFE, '3', u' ̔'),
    (0x1FFF, 'X'),
    (0x2000, '3', u' '),
    (0x200B, 'I'),
    (0x200C, 'D', u''),
    (0x200E, 'X'),
    (0x2010, 'V'),
    (0x2011, 'M', u'‐'),
    (0x2012, 'V'),
    (0x2017, '3', u' ̳'),
    (0x2018, 'V'),
    (0x2024, 'X'),
    (0x2027, 'V'),
    (0x2028, 'X'),
    (0x202F, '3', u' '),
    (0x2030, 'V'),
    (0x2033, 'M', u'′′'),
    (0x2034, 'M', u'′′′'),
    (0x2035, 'V'),
    (0x2036, 'M', u'‵‵'),
    (0x2037, 'M', u'‵‵‵'),
    (0x2038, 'V'),
    (0x203C, '3', u'!!'),
    (0x203D, 'V'),
    (0x203E, '3', u' ̅'),
    (0x203F, 'V'),
    (0x2047, '3', u'??'),
    (0x2048, '3', u'?!'),
    (0x2049, '3', u'!?'),
    (0x204A, 'V'),
    (0x2057, 'M', u'′′′′'),
    (0x2058, 'V'),
    (0x205F, '3', u' '),
    (0x2060, 'I'),
    (0x2061, 'X'),
    (0x2064, 'I'),
    (0x2065, 'X'),
    (0x2070, 'M', u'0'),
    (0x2071, 'M', u'i'),
    (0x2072, 'X'),
    (0x2074, 'M', u'4'),
    (0x2075, 'M', u'5'),
    (0x2076, 'M', u'6'),
    (0x2077, 'M', u'7'),
    (0x2078, 'M', u'8'),
    (0x2079, 'M', u'9'),
    (0x207A, '3', u'+'),
    (0x207B, 'M', u'−'),
    (0x207C, '3', u'='),
    (0x207D, '3', u'('),
    (0x207E, '3', u')'),
    (0x207F, 'M', u'n'),
    (0x2080, 'M', u'0'),
    (0x2081, 'M', u'1'),
    (0x2082, 'M', u'2'),
    (0x2083, 'M', u'3'),
    (0x2084, 'M', u'4'),
    (0x2085, 'M', u'5'),
    (0x2086, 'M', u'6'),
    (0x2087, 'M', u'7'),
    (0x2088, 'M', u'8'),
    (0x2089, 'M', u'9'),
    (0x208A, '3', u'+'),
    (0x208B, 'M', u'−'),
    (0x208C, '3', u'='),
    (0x208D, '3', u'('),
    (0x208E, '3', u')'),
    (0x208F, 'X'),
    (0x2090, 'M', u'a'),
    (0x2091, 'M', u'e'),
    (0x2092, 'M', u'o'),
    (0x2093, 'M', u'x'),
    (0x2094, 'M', u'ə'),
    (0x2095, 'M', u'h'),
    (0x2096, 'M', u'k'),
    (0x2097, 'M', u'l'),
    (0x2098, 'M', u'm'),
    (0x2099, 'M', u'n'),
    (0x209A, 'M', u'p'),
    (0x209B, 'M', u's'),
    (0x209C, 'M', u't'),
    (0x209D, 'X'),
    (0x20A0, 'V'),
    (0x20A8, 'M', u'rs'),
    (0x20A9, 'V'),
    (0x20C0, 'X'),
    (0x20D0, 'V'),
    (0x20F1, 'X'),
    (0x2100, '3', u'a/c'),
    (0x2101, '3', u'a/s'),
    ]

def _seg_22():
    return [
    (0x2102, 'M', u'c'),
    (0x2103, 'M', u'°c'),
    (0x2104, 'V'),
    (0x2105, '3', u'c/o'),
    (0x2106, '3', u'c/u'),
    (0x2107, 'M', u'ɛ'),
    (0x2108, 'V'),
    (0x2109, 'M', u'°f'),
    (0x210A, 'M', u'g'),
    (0x210B, 'M', u'h'),
    (0x210F, 'M', u'ħ'),
    (0x2110, 'M', u'i'),
    (0x2112, 'M', u'l'),
    (0x2114, 'V'),
    (0x2115, 'M', u'n'),
    (0x2116, 'M', u'no'),
    (0x2117, 'V'),
    (0x2119, 'M', u'p'),
    (0x211A, 'M', u'q'),
    (0x211B, 'M', u'r'),
    (0x211E, 'V'),
    (0x2120, 'M', u'sm'),
    (0x2121, 'M', u'tel'),
    (0x2122, 'M', u'tm'),
    (0x2123, 'V'),
    (0x2124, 'M', u'z'),
    (0x2125, 'V'),
    (0x2126, 'M', u'ω'),
    (0x2127, 'V'),
    (0x2128, 'M', u'z'),
    (0x2129, 'V'),
    (0x212A, 'M', u'k'),
    (0x212B, 'M', u'å'),
    (0x212C, 'M', u'b'),
    (0x212D, 'M', u'c'),
    (0x212E, 'V'),
    (0x212F, 'M', u'e'),
    (0x2131, 'M', u'f'),
    (0x2132, 'X'),
    (0x2133, 'M', u'm'),
    (0x2134, 'M', u'o'),
    (0x2135, 'M', u'א'),
    (0x2136, 'M', u'ב'),
    (0x2137, 'M', u'ג'),
    (0x2138, 'M', u'ד'),
    (0x2139, 'M', u'i'),
    (0x213A, 'V'),
    (0x213B, 'M', u'fax'),
    (0x213C, 'M', u'π'),
    (0x213D, 'M', u'γ'),
    (0x213F, 'M', u'π'),
    (0x2140, 'M', u'∑'),
    (0x2141, 'V'),
    (0x2145, 'M', u'd'),
    (0x2147, 'M', u'e'),
    (0x2148, 'M', u'i'),
    (0x2149, 'M', u'j'),
    (0x214A, 'V'),
    (0x2150, 'M', u'1⁄7'),
    (0x2151, 'M', u'1⁄9'),
    (0x2152, 'M', u'1⁄10'),
    (0x2153, 'M', u'1⁄3'),
    (0x2154, 'M', u'2⁄3'),
    (0x2155, 'M', u'1⁄5'),
    (0x2156, 'M', u'2⁄5'),
    (0x2157, 'M', u'3⁄5'),
    (0x2158, 'M', u'4⁄5'),
    (0x2159, 'M', u'1⁄6'),
    (0x215A, 'M', u'5⁄6'),
    (0x215B, 'M', u'1⁄8'),
    (0x215C, 'M', u'3⁄8'),
    (0x215D, 'M', u'5⁄8'),
    (0x215E, 'M', u'7⁄8'),
    (0x215F, 'M', u'1⁄'),
    (0x2160, 'M', u'i'),
    (0x2161, 'M', u'ii'),
    (0x2162, 'M', u'iii'),
    (0x2163, 'M', u'iv'),
    (0x2164, 'M', u'v'),
    (0x2165, 'M', u'vi'),
    (0x2166, 'M', u'vii'),
    (0x2167, 'M', u'viii'),
    (0x2168, 'M', u'ix'),
    (0x2169, 'M', u'x'),
    (0x216A, 'M', u'xi'),
    (0x216B, 'M', u'xii'),
    (0x216C, 'M', u'l'),
    (0x216D, 'M', u'c'),
    (0x216E, 'M', u'd'),
    (0x216F, 'M', u'm'),
    (0x2170, 'M', u'i'),
    (0x2171, 'M', u'ii'),
    (0x2172, 'M', u'iii'),
    (0x2173, 'M', u'iv'),
    (0x2174, 'M', u'v'),
    (0x2175, 'M', u'vi'),
    (0x2176, 'M', u'vii'),
    (0x2177, 'M', u'viii'),
    (0x2178, 'M', u'ix'),
    (0x2179, 'M', u'x'),
    ]

def _seg_23():
    return [
    (0x217A, 'M', u'xi'),
    (0x217B, 'M', u'xii'),
    (0x217C, 'M', u'l'),
    (0x217D, 'M', u'c'),
    (0x217E, 'M', u'd'),
    (0x217F, 'M', u'm'),
    (0x2180, 'V'),
    (0x2183, 'X'),
    (0x2184, 'V'),
    (0x2189, 'M', u'0⁄3'),
    (0x218A, 'V'),
    (0x218C, 'X'),
    (0x2190, 'V'),
    (0x222C, 'M', u'∫∫'),
    (0x222D, 'M', u'∫∫∫'),
    (0x222E, 'V'),
    (0x222F, 'M', u'∮∮'),
    (0x2230, 'M', u'∮∮∮'),
    (0x2231, 'V'),
    (0x2260, '3'),
    (0x2261, 'V'),
    (0x226E, '3'),
    (0x2270, 'V'),
    (0x2329, 'M', u'〈'),
    (0x232A, 'M', u'〉'),
    (0x232B, 'V'),
    (0x2427, 'X'),
    (0x2440, 'V'),
    (0x244B, 'X'),
    (0x2460, 'M', u'1'),
    (0x2461, 'M', u'2'),
    (0x2462, 'M', u'3'),
    (0x2463, 'M', u'4'),
    (0x2464, 'M', u'5'),
    (0x2465, 'M', u'6'),
    (0x2466, 'M', u'7'),
    (0x2467, 'M', u'8'),
    (0x2468, 'M', u'9'),
    (0x2469, 'M', u'10'),
    (0x246A, 'M', u'11'),
    (0x246B, 'M', u'12'),
    (0x246C, 'M', u'13'),
    (0x246D, 'M', u'14'),
    (0x246E, 'M', u'15'),
    (0x246F, 'M', u'16'),
    (0x2470, 'M', u'17'),
    (0x2471, 'M', u'18'),
    (0x2472, 'M', u'19'),
    (0x2473, 'M', u'20'),
    (0x2474, '3', u'(1)'),
    (0x2475, '3', u'(2)'),
    (0x2476, '3', u'(3)'),
    (0x2477, '3', u'(4)'),
    (0x2478, '3', u'(5)'),
    (0x2479, '3', u'(6)'),
    (0x247A, '3', u'(7)'),
    (0x247B, '3', u'(8)'),
    (0x247C, '3', u'(9)'),
    (0x247D, '3', u'(10)'),
    (0x247E, '3', u'(11)'),
    (0x247F, '3', u'(12)'),
    (0x2480, '3', u'(13)'),
    (0x2481, '3', u'(14)'),
    (0x2482, '3', u'(15)'),
    (0x2483, '3', u'(16)'),
    (0x2484, '3', u'(17)'),
    (0x2485, '3', u'(18)'),
    (0x2486, '3', u'(19)'),
    (0x2487, '3', u'(20)'),
    (0x2488, 'X'),
    (0x249C, '3', u'(a)'),
    (0x249D, '3', u'(b)'),
    (0x249E, '3', u'(c)'),
    (0x249F, '3', u'(d)'),
    (0x24A0, '3', u'(e)'),
    (0x24A1, '3', u'(f)'),
    (0x24A2, '3', u'(g)'),
    (0x24A3, '3', u'(h)'),
    (0x24A4, '3', u'(i)'),
    (0x24A5, '3', u'(j)'),
    (0x24A6, '3', u'(k)'),
    (0x24A7, '3', u'(l)'),
    (0x24A8, '3', u'(m)'),
    (0x24A9, '3', u'(n)'),
    (0x24AA, '3', u'(o)'),
    (0x24AB, '3', u'(p)'),
    (0x24AC, '3', u'(q)'),
    (0x24AD, '3', u'(r)'),
    (0x24AE, '3', u'(s)'),
    (0x24AF, '3', u'(t)'),
    (0x24B0, '3', u'(u)'),
    (0x24B1, '3', u'(v)'),
    (0x24B2, '3', u'(w)'),
    (0x24B3, '3', u'(x)'),
    (0x24B4, '3', u'(y)'),
    (0x24B5, '3', u'(z)'),
    (0x24B6, 'M', u'a'),
    (0x24B7, 'M', u'b'),
    (0x24B8, 'M', u'c'),
    (0x24B9, 'M', u'd'),
    ]

def _seg_24():
    return [
    (0x24BA, 'M', u'e'),
    (0x24BB, 'M', u'f'),
    (0x24BC, 'M', u'g'),
    (0x24BD, 'M', u'h'),
    (0x24BE, 'M', u'i'),
    (0x24BF, 'M', u'j'),
    (0x24C0, 'M', u'k'),
    (0x24C1, 'M', u'l'),
    (0x24C2, 'M', u'm'),
    (0x24C3, 'M', u'n'),
    (0x24C4, 'M', u'o'),
    (0x24C5, 'M', u'p'),
    (0x24C6, 'M', u'q'),
    (0x24C7, 'M', u'r'),
    (0x24C8, 'M', u's'),
    (0x24C9, 'M', u't'),
    (0x24CA, 'M', u'u'),
    (0x24CB, 'M', u'v'),
    (0x24CC, 'M', u'w'),
    (0x24CD, 'M', u'x'),
    (0x24CE, 'M', u'y'),
    (0x24CF, 'M', u'z'),
    (0x24D0, 'M', u'a'),
    (0x24D1, 'M', u'b'),
    (0x24D2, 'M', u'c'),
    (0x24D3, 'M', u'd'),
    (0x24D4, 'M', u'e'),
    (0x24D5, 'M', u'f'),
    (0x24D6, 'M', u'g'),
    (0x24D7, 'M', u'h'),
    (0x24D8, 'M', u'i'),
    (0x24D9, 'M', u'j'),
    (0x24DA, 'M', u'k'),
    (0x24DB, 'M', u'l'),
    (0x24DC, 'M', u'm'),
    (0x24DD, 'M', u'n'),
    (0x24DE, 'M', u'o'),
    (0x24DF, 'M', u'p'),
    (0x24E0, 'M', u'q'),
    (0x24E1, 'M', u'r'),
    (0x24E2, 'M', u's'),
    (0x24E3, 'M', u't'),
    (0x24E4, 'M', u'u'),
    (0x24E5, 'M', u'v'),
    (0x24E6, 'M', u'w'),
    (0x24E7, 'M', u'x'),
    (0x24E8, 'M', u'y'),
    (0x24E9, 'M', u'z'),
    (0x24EA, 'M', u'0'),
    (0x24EB, 'V'),
    (0x2A0C, 'M', u'∫∫∫∫'),
    (0x2A0D, 'V'),
    (0x2A74, '3', u'::='),
    (0x2A75, '3', u'=='),
    (0x2A76, '3', u'==='),
    (0x2A77, 'V'),
    (0x2ADC, 'M', u'⫝̸'),
    (0x2ADD, 'V'),
    (0x2B74, 'X'),
    (0x2B76, 'V'),
    (0x2B96, 'X'),
    (0x2B97, 'V'),
    (0x2C00, 'M', u'ⰰ'),
    (0x2C01, 'M', u'ⰱ'),
    (0x2C02, 'M', u'ⰲ'),
    (0x2C03, 'M', u'ⰳ'),
    (0x2C04, 'M', u'ⰴ'),
    (0x2C05, 'M', u'ⰵ'),
    (0x2C06, 'M', u'ⰶ'),
    (0x2C07, 'M', u'ⰷ'),
    (0x2C08, 'M', u'ⰸ'),
    (0x2C09, 'M', u'ⰹ'),
    (0x2C0A, 'M', u'ⰺ'),
    (0x2C0B, 'M', u'ⰻ'),
    (0x2C0C, 'M', u'ⰼ'),
    (0x2C0D, 'M', u'ⰽ'),
    (0x2C0E, 'M', u'ⰾ'),
    (0x2C0F, 'M', u'ⰿ'),
    (0x2C10, 'M', u'ⱀ'),
    (0x2C11, 'M', u'ⱁ'),
    (0x2C12, 'M', u'ⱂ'),
    (0x2C13, 'M', u'ⱃ'),
    (0x2C14, 'M', u'ⱄ'),
    (0x2C15, 'M', u'ⱅ'),
    (0x2C16, 'M', u'ⱆ'),
    (0x2C17, 'M', u'ⱇ'),
    (0x2C18, 'M', u'ⱈ'),
    (0x2C19, 'M', u'ⱉ'),
    (0x2C1A, 'M', u'ⱊ'),
    (0x2C1B, 'M', u'ⱋ'),
    (0x2C1C, 'M', u'ⱌ'),
    (0x2C1D, 'M', u'ⱍ'),
    (0x2C1E, 'M', u'ⱎ'),
    (0x2C1F, 'M', u'ⱏ'),
    (0x2C20, 'M', u'ⱐ'),
    (0x2C21, 'M', u'ⱑ'),
    (0x2C22, 'M', u'ⱒ'),
    (0x2C23, 'M', u'ⱓ'),
    (0x2C24, 'M', u'ⱔ'),
    (0x2C25, 'M', u'ⱕ'),
    ]

def _seg_25():
    return [
    (0x2C26, 'M', u'ⱖ'),
    (0x2C27, 'M', u'ⱗ'),
    (0x2C28, 'M', u'ⱘ'),
    (0x2C29, 'M', u'ⱙ'),
    (0x2C2A, 'M', u'ⱚ'),
    (0x2C2B, 'M', u'ⱛ'),
    (0x2C2C, 'M', u'ⱜ'),
    (0x2C2D, 'M', u'ⱝ'),
    (0x2C2E, 'M', u'ⱞ'),
    (0x2C2F, 'X'),
    (0x2C30, 'V'),
    (0x2C5F, 'X'),
    (0x2C60, 'M', u'ⱡ'),
    (0x2C61, 'V'),
    (0x2C62, 'M', u'ɫ'),
    (0x2C63, 'M', u'ᵽ'),
    (0x2C64, 'M', u'ɽ'),
    (0x2C65, 'V'),
    (0x2C67, 'M', u'ⱨ'),
    (0x2C68, 'V'),
    (0x2C69, 'M', u'ⱪ'),
    (0x2C6A, 'V'),
    (0x2C6B, 'M', u'ⱬ'),
    (0x2C6C, 'V'),
    (0x2C6D, 'M', u'ɑ'),
    (0x2C6E, 'M', u'ɱ'),
    (0x2C6F, 'M', u'ɐ'),
    (0x2C70, 'M', u'ɒ'),
    (0x2C71, 'V'),
    (0x2C72, 'M', u'ⱳ'),
    (0x2C73, 'V'),
    (0x2C75, 'M', u'ⱶ'),
    (0x2C76, 'V'),
    (0x2C7C, 'M', u'j'),
    (0x2C7D, 'M', u'v'),
    (0x2C7E, 'M', u'ȿ'),
    (0x2C7F, 'M', u'ɀ'),
    (0x2C80, 'M', u'ⲁ'),
    (0x2C81, 'V'),
    (0x2C82, 'M', u'ⲃ'),
    (0x2C83, 'V'),
    (0x2C84, 'M', u'ⲅ'),
    (0x2C85, 'V'),
    (0x2C86, 'M', u'ⲇ'),
    (0x2C87, 'V'),
    (0x2C88, 'M', u'ⲉ'),
    (0x2C89, 'V'),
    (0x2C8A, 'M', u'ⲋ'),
    (0x2C8B, 'V'),
    (0x2C8C, 'M', u'ⲍ'),
    (0x2C8D, 'V'),
    (0x2C8E, 'M', u'ⲏ'),
    (0x2C8F, 'V'),
    (0x2C90, 'M', u'ⲑ'),
    (0x2C91, 'V'),
    (0x2C92, 'M', u'ⲓ'),
    (0x2C93, 'V'),
    (0x2C94, 'M', u'ⲕ'),
    (0x2C95, 'V'),
    (0x2C96, 'M', u'ⲗ'),
    (0x2C97, 'V'),
    (0x2C98, 'M', u'ⲙ'),
    (0x2C99, 'V'),
    (0x2C9A, 'M', u'ⲛ'),
    (0x2C9B, 'V'),
    (0x2C9C, 'M', u'ⲝ'),
    (0x2C9D, 'V'),
    (0x2C9E, 'M', u'ⲟ'),
    (0x2C9F, 'V'),
    (0x2CA0, 'M', u'ⲡ'),
    (0x2CA1, 'V'),
    (0x2CA2, 'M', u'ⲣ'),
    (0x2CA3, 'V'),
    (0x2CA4, 'M', u'ⲥ'),
    (0x2CA5, 'V'),
    (0x2CA6, 'M', u'ⲧ'),
    (0x2CA7, 'V'),
    (0x2CA8, 'M', u'ⲩ'),
    (0x2CA9, 'V'),
    (0x2CAA, 'M', u'ⲫ'),
    (0x2CAB, 'V'),
    (0x2CAC, 'M', u'ⲭ'),
    (0x2CAD, 'V'),
    (0x2CAE, 'M', u'ⲯ'),
    (0x2CAF, 'V'),
    (0x2CB0, 'M', u'ⲱ'),
    (0x2CB1, 'V'),
    (0x2CB2, 'M', u'ⲳ'),
    (0x2CB3, 'V'),
    (0x2CB4, 'M', u'ⲵ'),
    (0x2CB5, 'V'),
    (0x2CB6, 'M', u'ⲷ'),
    (0x2CB7, 'V'),
    (0x2CB8, 'M', u'ⲹ'),
    (0x2CB9, 'V'),
    (0x2CBA, 'M', u'ⲻ'),
    (0x2CBB, 'V'),
    (0x2CBC, 'M', u'ⲽ'),
    (0x2CBD, 'V'),
    (0x2CBE, 'M', u'ⲿ'),
    ]

def _seg_26():
    return [
    (0x2CBF, 'V'),
    (0x2CC0, 'M', u'ⳁ'),
    (0x2CC1, 'V'),
    (0x2CC2, 'M', u'ⳃ'),
    (0x2CC3, 'V'),
    (0x2CC4, 'M', u'ⳅ'),
    (0x2CC5, 'V'),
    (0x2CC6, 'M', u'ⳇ'),
    (0x2CC7, 'V'),
    (0x2CC8, 'M', u'ⳉ'),
    (0x2CC9, 'V'),
    (0x2CCA, 'M', u'ⳋ'),
    (0x2CCB, 'V'),
    (0x2CCC, 'M', u'ⳍ'),
    (0x2CCD, 'V'),
    (0x2CCE, 'M', u'ⳏ'),
    (0x2CCF, 'V'),
    (0x2CD0, 'M', u'ⳑ'),
    (0x2CD1, 'V'),
    (0x2CD2, 'M', u'ⳓ'),
    (0x2CD3, 'V'),
    (0x2CD4, 'M', u'ⳕ'),
    (0x2CD5, 'V'),
    (0x2CD6, 'M', u'ⳗ'),
    (0x2CD7, 'V'),
    (0x2CD8, 'M', u'ⳙ'),
    (0x2CD9, 'V'),
    (0x2CDA, 'M', u'ⳛ'),
    (0x2CDB, 'V'),
    (0x2CDC, 'M', u'ⳝ'),
    (0x2CDD, 'V'),
    (0x2CDE, 'M', u'ⳟ'),
    (0x2CDF, 'V'),
    (0x2CE0, 'M', u'ⳡ'),
    (0x2CE1, 'V'),
    (0x2CE2, 'M', u'ⳣ'),
    (0x2CE3, 'V'),
    (0x2CEB, 'M', u'ⳬ'),
    (0x2CEC, 'V'),
    (0x2CED, 'M', u'ⳮ'),
    (0x2CEE, 'V'),
    (0x2CF2, 'M', u'ⳳ'),
    (0x2CF3, 'V'),
    (0x2CF4, 'X'),
    (0x2CF9, 'V'),
    (0x2D26, 'X'),
    (0x2D27, 'V'),
    (0x2D28, 'X'),
    (0x2D2D, 'V'),
    (0x2D2E, 'X'),
    (0x2D30, 'V'),
    (0x2D68, 'X'),
    (0x2D6F, 'M', u'ⵡ'),
    (0x2D70, 'V'),
    (0x2D71, 'X'),
    (0x2D7F, 'V'),
    (0x2D97, 'X'),
    (0x2DA0, 'V'),
    (0x2DA7, 'X'),
    (0x2DA8, 'V'),
    (0x2DAF, 'X'),
    (0x2DB0, 'V'),
    (0x2DB7, 'X'),
    (0x2DB8, 'V'),
    (0x2DBF, 'X'),
    (0x2DC0, 'V'),
    (0x2DC7, 'X'),
    (0x2DC8, 'V'),
    (0x2DCF, 'X'),
    (0x2DD0, 'V'),
    (0x2DD7, 'X'),
    (0x2DD8, 'V'),
    (0x2DDF, 'X'),
    (0x2DE0, 'V'),
    (0x2E53, 'X'),
    (0x2E80, 'V'),
    (0x2E9A, 'X'),
    (0x2E9B, 'V'),
    (0x2E9F, 'M', u'母'),
    (0x2EA0, 'V'),
    (0x2EF3, 'M', u'龟'),
    (0x2EF4, 'X'),
    (0x2F00, 'M', u'一'),
    (0x2F01, 'M', u'丨'),
    (0x2F02, 'M', u'丶'),
    (0x2F03, 'M', u'丿'),
    (0x2F04, 'M', u'乙'),
    (0x2F05, 'M', u'亅'),
    (0x2F06, 'M', u'二'),
    (0x2F07, 'M', u'亠'),
    (0x2F08, 'M', u'人'),
    (0x2F09, 'M', u'儿'),
    (0x2F0A, 'M', u'入'),
    (0x2F0B, 'M', u'八'),
    (0x2F0C, 'M', u'冂'),
    (0x2F0D, 'M', u'冖'),
    (0x2F0E, 'M', u'冫'),
    (0x2F0F, 'M', u'几'),
    (0x2F10, 'M', u'凵'),
    (0x2F11, 'M', u'刀'),
    ]

def _seg_27():
    return [
    (0x2F12, 'M', u'力'),
    (0x2F13, 'M', u'勹'),
    (0x2F14, 'M', u'匕'),
    (0x2F15, 'M', u'匚'),
    (0x2F16, 'M', u'匸'),
    (0x2F17, 'M', u'十'),
    (0x2F18, 'M', u'卜'),
    (0x2F19, 'M', u'卩'),
    (0x2F1A, 'M', u'厂'),
    (0x2F1B, 'M', u'厶'),
    (0x2F1C, 'M', u'又'),
    (0x2F1D, 'M', u'口'),
    (0x2F1E, 'M', u'囗'),
    (0x2F1F, 'M', u'土'),
    (0x2F20, 'M', u'士'),
    (0x2F21, 'M', u'夂'),
    (0x2F22, 'M', u'夊'),
    (0x2F23, 'M', u'夕'),
    (0x2F24, 'M', u'大'),
    (0x2F25, 'M', u'女'),
    (0x2F26, 'M', u'子'),
    (0x2F27, 'M', u'宀'),
    (0x2F28, 'M', u'寸'),
    (0x2F29, 'M', u'小'),
    (0x2F2A, 'M', u'尢'),
    (0x2F2B, 'M', u'尸'),
    (0x2F2C, 'M', u'屮'),
    (0x2F2D, 'M', u'山'),
    (0x2F2E, 'M', u'巛'),
    (0x2F2F, 'M', u'工'),
    (0x2F30, 'M', u'己'),
    (0x2F31, 'M', u'巾'),
    (0x2F32, 'M', u'干'),
    (0x2F33, 'M', u'幺'),
    (0x2F34, 'M', u'广'),
    (0x2F35, 'M', u'廴'),
    (0x2F36, 'M', u'廾'),
    (0x2F37, 'M', u'弋'),
    (0x2F38, 'M', u'弓'),
    (0x2F39, 'M', u'彐'),
    (0x2F3A, 'M', u'彡'),
    (0x2F3B, 'M', u'彳'),
    (0x2F3C, 'M', u'心'),
    (0x2F3D, 'M', u'戈'),
    (0x2F3E, 'M', u'戶'),
    (0x2F3F, 'M', u'手'),
    (0x2F40, 'M', u'支'),
    (0x2F41, 'M', u'攴'),
    (0x2F42, 'M', u'文'),
    (0x2F43, 'M', u'斗'),
    (0x2F44, 'M', u'斤'),
    (0x2F45, 'M', u'方'),
    (0x2F46, 'M', u'无'),
    (0x2F47, 'M', u'日'),
    (0x2F48, 'M', u'曰'),
    (0x2F49, 'M', u'月'),
    (0x2F4A, 'M', u'木'),
    (0x2F4B, 'M', u'欠'),
    (0x2F4C, 'M', u'止'),
    (0x2F4D, 'M', u'歹'),
    (0x2F4E, 'M', u'殳'),
    (0x2F4F, 'M', u'毋'),
    (0x2F50, 'M', u'比'),
    (0x2F51, 'M', u'毛'),
    (0x2F52, 'M', u'氏'),
    (0x2F53, 'M', u'气'),
    (0x2F54, 'M', u'水'),
    (0x2F55, 'M', u'火'),
    (0x2F56, 'M', u'爪'),
    (0x2F57, 'M', u'父'),
    (0x2F58, 'M', u'爻'),
    (0x2F59, 'M', u'爿'),
    (0x2F5A, 'M', u'片'),
    (0x2F5B, 'M', u'牙'),
    (0x2F5C, 'M', u'牛'),
    (0x2F5D, 'M', u'犬'),
    (0x2F5E, 'M', u'玄'),
    (0x2F5F, 'M', u'玉'),
    (0x2F60, 'M', u'瓜'),
    (0x2F61, 'M', u'瓦'),
    (0x2F62, 'M', u'甘'),
    (0x2F63, 'M', u'生'),
    (0x2F64, 'M', u'用'),
    (0x2F65, 'M', u'田'),
    (0x2F66, 'M', u'疋'),
    (0x2F67, 'M', u'疒'),
    (0x2F68, 'M', u'癶'),
    (0x2F69, 'M', u'白'),
    (0x2F6A, 'M', u'皮'),
    (0x2F6B, 'M', u'皿'),
    (0x2F6C, 'M', u'目'),
    (0x2F6D, 'M', u'矛'),
    (0x2F6E, 'M', u'矢'),
    (0x2F6F, 'M', u'石'),
    (0x2F70, 'M', u'示'),
    (0x2F71, 'M', u'禸'),
    (0x2F72, 'M', u'禾'),
    (0x2F73, 'M', u'穴'),
    (0x2F74, 'M', u'立'),
    (0x2F75, 'M', u'竹'),
    ]

def _seg_28():
    return [
    (0x2F76, 'M', u'米'),
    (0x2F77, 'M', u'糸'),
    (0x2F78, 'M', u'缶'),
    (0x2F79, 'M', u'网'),
    (0x2F7A, 'M', u'羊'),
    (0x2F7B, 'M', u'羽'),
    (0x2F7C, 'M', u'老'),
    (0x2F7D, 'M', u'而'),
    (0x2F7E, 'M', u'耒'),
    (0x2F7F, 'M', u'耳'),
    (0x2F80, 'M', u'聿'),
    (0x2F81, 'M', u'肉'),
    (0x2F82, 'M', u'臣'),
    (0x2F83, 'M', u'自'),
    (0x2F84, 'M', u'至'),
    (0x2F85, 'M', u'臼'),
    (0x2F86, 'M', u'舌'),
    (0x2F87, 'M', u'舛'),
    (0x2F88, 'M', u'舟'),
    (0x2F89, 'M', u'艮'),
    (0x2F8A, 'M', u'色'),
    (0x2F8B, 'M', u'艸'),
    (0x2F8C, 'M', u'虍'),
    (0x2F8D, 'M', u'虫'),
    (0x2F8E, 'M', u'血'),
    (0x2F8F, 'M', u'行'),
    (0x2F90, 'M', u'衣'),
    (0x2F91, 'M', u'襾'),
    (0x2F92, 'M', u'見'),
    (0x2F93, 'M', u'角'),
    (0x2F94, 'M', u'言'),
    (0x2F95, 'M', u'谷'),
    (0x2F96, 'M', u'豆'),
    (0x2F97, 'M', u'豕'),
    (0x2F98, 'M', u'豸'),
    (0x2F99, 'M', u'貝'),
    (0x2F9A, 'M', u'赤'),
    (0x2F9B, 'M', u'走'),
    (0x2F9C, 'M', u'足'),
    (0x2F9D, 'M', u'身'),
    (0x2F9E, 'M', u'車'),
    (0x2F9F, 'M', u'辛'),
    (0x2FA0, 'M', u'辰'),
    (0x2FA1, 'M', u'辵'),
    (0x2FA2, 'M', u'邑'),
    (0x2FA3, 'M', u'酉'),
    (0x2FA4, 'M', u'釆'),
    (0x2FA5, 'M', u'里'),
    (0x2FA6, 'M', u'金'),
    (0x2FA7, 'M', u'長'),
    (0x2FA8, 'M', u'門'),
    (0x2FA9, 'M', u'阜'),
    (0x2FAA, 'M', u'隶'),
    (0x2FAB, 'M', u'隹'),
    (0x2FAC, 'M', u'雨'),
    (0x2FAD, 'M', u'靑'),
    (0x2FAE, 'M', u'非'),
    (0x2FAF, 'M', u'面'),
    (0x2FB0, 'M', u'革'),
    (0x2FB1, 'M', u'韋'),
    (0x2FB2, 'M', u'韭'),
    (0x2FB3, 'M', u'音'),
    (0x2FB4, 'M', u'頁'),
    (0x2FB5, 'M', u'風'),
    (0x2FB6, 'M', u'飛'),
    (0x2FB7, 'M', u'食'),
    (0x2FB8, 'M', u'首'),
    (0x2FB9, 'M', u'香'),
    (0x2FBA, 'M', u'馬'),
    (0x2FBB, 'M', u'骨'),
    (0x2FBC, 'M', u'高'),
    (0x2FBD, 'M', u'髟'),
    (0x2FBE, 'M', u'鬥'),
    (0x2FBF, 'M', u'鬯'),
    (0x2FC0, 'M', u'鬲'),
    (0x2FC1, 'M', u'鬼'),
    (0x2FC2, 'M', u'魚'),
    (0x2FC3, 'M', u'鳥'),
    (0x2FC4, 'M', u'鹵'),
    (0x2FC5, 'M', u'鹿'),
    (0x2FC6, 'M', u'麥'),
    (0x2FC7, 'M', u'麻'),
    (0x2FC8, 'M', u'黃'),
    (0x2FC9, 'M', u'黍'),
    (0x2FCA, 'M', u'黑'),
    (0x2FCB, 'M', u'黹'),
    (0x2FCC, 'M', u'黽'),
    (0x2FCD, 'M', u'鼎'),
    (0x2FCE, 'M', u'鼓'),
    (0x2FCF, 'M', u'鼠'),
    (0x2FD0, 'M', u'鼻'),
    (0x2FD1, 'M', u'齊'),
    (0x2FD2, 'M', u'齒'),
    (0x2FD3, 'M', u'龍'),
    (0x2FD4, 'M', u'龜'),
    (0x2FD5, 'M', u'龠'),
    (0x2FD6, 'X'),
    (0x3000, '3', u' '),
    (0x3001, 'V'),
    (0x3002, 'M', u'.'),
    ]

def _seg_29():
    return [
    (0x3003, 'V'),
    (0x3036, 'M', u'〒'),
    (0x3037, 'V'),
    (0x3038, 'M', u'十'),
    (0x3039, 'M', u'卄'),
    (0x303A, 'M', u'卅'),
    (0x303B, 'V'),
    (0x3040, 'X'),
    (0x3041, 'V'),
    (0x3097, 'X'),
    (0x3099, 'V'),
    (0x309B, '3', u' ゙'),
    (0x309C, '3', u' ゚'),
    (0x309D, 'V'),
    (0x309F, 'M', u'より'),
    (0x30A0, 'V'),
    (0x30FF, 'M', u'コト'),
    (0x3100, 'X'),
    (0x3105, 'V'),
    (0x3130, 'X'),
    (0x3131, 'M', u'ᄀ'),
    (0x3132, 'M', u'ᄁ'),
    (0x3133, 'M', u'ᆪ'),
    (0x3134, 'M', u'ᄂ'),
    (0x3135, 'M', u'ᆬ'),
    (0x3136, 'M', u'ᆭ'),
    (0x3137, 'M', u'ᄃ'),
    (0x3138, 'M', u'ᄄ'),
    (0x3139, 'M', u'ᄅ'),
    (0x313A, 'M', u'ᆰ'),
    (0x313B, 'M', u'ᆱ'),
    (0x313C, 'M', u'ᆲ'),
    (0x313D, 'M', u'ᆳ'),
    (0x313E, 'M', u'ᆴ'),
    (0x313F, 'M', u'ᆵ'),
    (0x3140, 'M', u'ᄚ'),
    (0x3141, 'M', u'ᄆ'),
    (0x3142, 'M', u'ᄇ'),
    (0x3143, 'M', u'ᄈ'),
    (0x3144, 'M', u'ᄡ'),
    (0x3145, 'M', u'ᄉ'),
    (0x3146, 'M', u'ᄊ'),
    (0x3147, 'M', u'ᄋ'),
    (0x3148, 'M', u'ᄌ'),
    (0x3149, 'M', u'ᄍ'),
    (0x314A, 'M', u'ᄎ'),
    (0x314B, 'M', u'ᄏ'),
    (0x314C, 'M', u'ᄐ'),
    (0x314D, 'M', u'ᄑ'),
    (0x314E, 'M', u'ᄒ'),
    (0x314F, 'M', u'ᅡ'),
    (0x3150, 'M', u'ᅢ'),
    (0x3151, 'M', u'ᅣ'),
    (0x3152, 'M', u'ᅤ'),
    (0x3153, 'M', u'ᅥ'),
    (0x3154, 'M', u'ᅦ'),
    (0x3155, 'M', u'ᅧ'),
    (0x3156, 'M', u'ᅨ'),
    (0x3157, 'M', u'ᅩ'),
    (0x3158, 'M', u'ᅪ'),
    (0x3159, 'M', u'ᅫ'),
    (0x315A, 'M', u'ᅬ'),
    (0x315B, 'M', u'ᅭ'),
    (0x315C, 'M', u'ᅮ'),
    (0x315D, 'M', u'ᅯ'),
    (0x315E, 'M', u'ᅰ'),
    (0x315F, 'M', u'ᅱ'),
    (0x3160, 'M', u'ᅲ'),
    (0x3161, 'M', u'ᅳ'),
    (0x3162, 'M', u'ᅴ'),
    (0x3163, 'M', u'ᅵ'),
    (0x3164, 'X'),
    (0x3165, 'M', u'ᄔ'),
    (0x3166, 'M', u'ᄕ'),
    (0x3167, 'M', u'ᇇ'),
    (0x3168, 'M', u'ᇈ'),
    (0x3169, 'M', u'ᇌ'),
    (0x316A, 'M', u'ᇎ'),
    (0x316B, 'M', u'ᇓ'),
    (0x316C, 'M', u'ᇗ'),
    (0x316D, 'M', u'ᇙ'),
    (0x316E, 'M', u'ᄜ'),
    (0x316F, 'M', u'ᇝ'),
    (0x3170, 'M', u'ᇟ'),
    (0x3171, 'M', u'ᄝ'),
    (0x3172, 'M', u'ᄞ'),
    (0x3173, 'M', u'ᄠ'),
    (0x3174, 'M', u'ᄢ'),
    (0x3175, 'M', u'ᄣ'),
    (0x3176, 'M', u'ᄧ'),
    (0x3177, 'M', u'ᄩ'),
    (0x3178, 'M', u'ᄫ'),
    (0x3179, 'M', u'ᄬ'),
    (0x317A, 'M', u'ᄭ'),
    (0x317B, 'M', u'ᄮ'),
    (0x317C, 'M', u'ᄯ'),
    (0x317D, 'M', u'ᄲ'),
    (0x317E, 'M', u'ᄶ'),
    (0x317F, 'M', u'ᅀ'),
    (0x3180, 'M', u'ᅇ'),
    ]

def _seg_30():
    return [
    (0x3181, 'M', u'ᅌ'),
    (0x3182, 'M', u'ᇱ'),
    (0x3183, 'M', u'ᇲ'),
    (0x3184, 'M', u'ᅗ'),
    (0x3185, 'M', u'ᅘ'),
    (0x3186, 'M', u'ᅙ'),
    (0x3187, 'M', u'ᆄ'),
    (0x3188, 'M', u'ᆅ'),
    (0x3189, 'M', u'ᆈ'),
    (0x318A, 'M', u'ᆑ'),
    (0x318B, 'M', u'ᆒ'),
    (0x318C, 'M', u'ᆔ'),
    (0x318D, 'M', u'ᆞ'),
    (0x318E, 'M', u'ᆡ'),
    (0x318F, 'X'),
    (0x3190, 'V'),
    (0x3192, 'M', u'一'),
    (0x3193, 'M', u'二'),
    (0x3194, 'M', u'三'),
    (0x3195, 'M', u'四'),
    (0x3196, 'M', u'上'),
    (0x3197, 'M', u'中'),
    (0x3198, 'M', u'下'),
    (0x3199, 'M', u'甲'),
    (0x319A, 'M', u'乙'),
    (0x319B, 'M', u'丙'),
    (0x319C, 'M', u'丁'),
    (0x319D, 'M', u'天'),
    (0x319E, 'M', u'地'),
    (0x319F, 'M', u'人'),
    (0x31A0, 'V'),
    (0x31E4, 'X'),
    (0x31F0, 'V'),
    (0x3200, '3', u'(ᄀ)'),
    (0x3201, '3', u'(ᄂ)'),
    (0x3202, '3', u'(ᄃ)'),
    (0x3203, '3', u'(ᄅ)'),
    (0x3204, '3', u'(ᄆ)'),
    (0x3205, '3', u'(ᄇ)'),
    (0x3206, '3', u'(ᄉ)'),
    (0x3207, '3', u'(ᄋ)'),
    (0x3208, '3', u'(ᄌ)'),
    (0x3209, '3', u'(ᄎ)'),
    (0x320A, '3', u'(ᄏ)'),
    (0x320B, '3', u'(ᄐ)'),
    (0x320C, '3', u'(ᄑ)'),
    (0x320D, '3', u'(ᄒ)'),
    (0x320E, '3', u'(가)'),
    (0x320F, '3', u'(나)'),
    (0x3210, '3', u'(다)'),
    (0x3211, '3', u'(라)'),
    (0x3212, '3', u'(마)'),
    (0x3213, '3', u'(바)'),
    (0x3214, '3', u'(사)'),
    (0x3215, '3', u'(아)'),
    (0x3216, '3', u'(자)'),
    (0x3217, '3', u'(차)'),
    (0x3218, '3', u'(카)'),
    (0x3219, '3', u'(타)'),
    (0x321A, '3', u'(파)'),
    (0x321B, '3', u'(하)'),
    (0x321C, '3', u'(주)'),
    (0x321D, '3', u'(오전)'),
    (0x321E, '3', u'(오후)'),
    (0x321F, 'X'),
    (0x3220, '3', u'(一)'),
    (0x3221, '3', u'(二)'),
    (0x3222, '3', u'(三)'),
    (0x3223, '3', u'(四)'),
    (0x3224, '3', u'(五)'),
    (0x3225, '3', u'(六)'),
    (0x3226, '3', u'(七)'),
    (0x3227, '3', u'(八)'),
    (0x3228, '3', u'(九)'),
    (0x3229, '3', u'(十)'),
    (0x322A, '3', u'(月)'),
    (0x322B, '3', u'(火)'),
    (0x322C, '3', u'(水)'),
    (0x322D, '3', u'(木)'),
    (0x322E, '3', u'(金)'),
    (0x322F, '3', u'(土)'),
    (0x3230, '3', u'(日)'),
    (0x3231, '3', u'(株)'),
    (0x3232, '3', u'(有)'),
    (0x3233, '3', u'(社)'),
    (0x3234, '3', u'(名)'),
    (0x3235, '3', u'(特)'),
    (0x3236, '3', u'(財)'),
    (0x3237, '3', u'(祝)'),
    (0x3238, '3', u'(労)'),
    (0x3239, '3', u'(代)'),
    (0x323A, '3', u'(呼)'),
    (0x323B, '3', u'(学)'),
    (0x323C, '3', u'(監)'),
    (0x323D, '3', u'(企)'),
    (0x323E, '3', u'(資)'),
    (0x323F, '3', u'(協)'),
    (0x3240, '3', u'(祭)'),
    (0x3241, '3', u'(休)'),
    (0x3242, '3', u'(自)'),
    ]

def _seg_31():
    return [
    (0x3243, '3', u'(至)'),
    (0x3244, 'M', u'問'),
    (0x3245, 'M', u'幼'),
    (0x3246, 'M', u'文'),
    (0x3247, 'M', u'箏'),
    (0x3248, 'V'),
    (0x3250, 'M', u'pte'),
    (0x3251, 'M', u'21'),
    (0x3252, 'M', u'22'),
    (0x3253, 'M', u'23'),
    (0x3254, 'M', u'24'),
    (0x3255, 'M', u'25'),
    (0x3256, 'M', u'26'),
    (0x3257, 'M', u'27'),
    (0x3258, 'M', u'28'),
    (0x3259, 'M', u'29'),
    (0x325A, 'M', u'30'),
    (0x325B, 'M', u'31'),
    (0x325C, 'M', u'32'),
    (0x325D, 'M', u'33'),
    (0x325E, 'M', u'34'),
    (0x325F, 'M', u'35'),
    (0x3260, 'M', u'ᄀ'),
    (0x3261, 'M', u'ᄂ'),
    (0x3262, 'M', u'ᄃ'),
    (0x3263, 'M', u'ᄅ'),
    (0x3264, 'M', u'ᄆ'),
    (0x3265, 'M', u'ᄇ'),
    (0x3266, 'M', u'ᄉ'),
    (0x3267, 'M', u'ᄋ'),
    (0x3268, 'M', u'ᄌ'),
    (0x3269, 'M', u'ᄎ'),
    (0x326A, 'M', u'ᄏ'),
    (0x326B, 'M', u'ᄐ'),
    (0x326C, 'M', u'ᄑ'),
    (0x326D, 'M', u'ᄒ'),
    (0x326E, 'M', u'가'),
    (0x326F, 'M', u'나'),
    (0x3270, 'M', u'다'),
    (0x3271, 'M', u'라'),
    (0x3272, 'M', u'마'),
    (0x3273, 'M', u'바'),
    (0x3274, 'M', u'사'),
    (0x3275, 'M', u'아'),
    (0x3276, 'M', u'자'),
    (0x3277, 'M', u'차'),
    (0x3278, 'M', u'카'),
    (0x3279, 'M', u'타'),
    (0x327A, 'M', u'파'),
    (0x327B, 'M', u'하'),
    (0x327C, 'M', u'참고'),
    (0x327D, 'M', u'주의'),
    (0x327E, 'M', u'우'),
    (0x327F, 'V'),
    (0x3280, 'M', u'一'),
    (0x3281, 'M', u'二'),
    (0x3282, 'M', u'三'),
    (0x3283, 'M', u'四'),
    (0x3284, 'M', u'五'),
    (0x3285, 'M', u'六'),
    (0x3286, 'M', u'七'),
    (0x3287, 'M', u'八'),
    (0x3288, 'M', u'九'),
    (0x3289, 'M', u'十'),
    (0x328A, 'M', u'月'),
    (0x328B, 'M', u'火'),
    (0x328C, 'M', u'水'),
    (0x328D, 'M', u'木'),
    (0x328E, 'M', u'金'),
    (0x328F, 'M', u'土'),
    (0x3290, 'M', u'日'),
    (0x3291, 'M', u'株'),
    (0x3292, 'M', u'有'),
    (0x3293, 'M', u'社'),
    (0x3294, 'M', u'名'),
    (0x3295, 'M', u'特'),
    (0x3296, 'M', u'財'),
    (0x3297, 'M', u'祝'),
    (0x3298, 'M', u'労'),
    (0x3299, 'M', u'秘'),
    (0x329A, 'M', u'男'),
    (0x329B, 'M', u'女'),
    (0x329C, 'M', u'適'),
    (0x329D, 'M', u'優'),
    (0x329E, 'M', u'印'),
    (0x329F, 'M', u'注'),
    (0x32A0, 'M', u'項'),
    (0x32A1, 'M', u'休'),
    (0x32A2, 'M', u'写'),
    (0x32A3, 'M', u'正'),
    (0x32A4, 'M', u'上'),
    (0x32A5, 'M', u'中'),
    (0x32A6, 'M', u'下'),
    (0x32A7, 'M', u'左'),
    (0x32A8, 'M', u'右'),
    (0x32A9, 'M', u'医'),
    (0x32AA, 'M', u'宗'),
    (0x32AB, 'M', u'学'),
    (0x32AC, 'M', u'監'),
    (0x32AD, 'M', u'企'),
    ]

def _seg_32():
    return [
    (0x32AE, 'M', u'資'),
    (0x32AF, 'M', u'協'),
    (0x32B0, 'M', u'夜'),
    (0x32B1, 'M', u'36'),
    (0x32B2, 'M', u'37'),
    (0x32B3, 'M', u'38'),
    (0x32B4, 'M', u'39'),
    (0x32B5, 'M', u'40'),
    (0x32B6, 'M', u'41'),
    (0x32B7, 'M', u'42'),
    (0x32B8, 'M', u'43'),
    (0x32B9, 'M', u'44'),
    (0x32BA, 'M', u'45'),
    (0x32BB, 'M', u'46'),
    (0x32BC, 'M', u'47'),
    (0x32BD, 'M', u'48'),
    (0x32BE, 'M', u'49'),
    (0x32BF, 'M', u'50'),
    (0x32C0, 'M', u'1月'),
    (0x32C1, 'M', u'2月'),
    (0x32C2, 'M', u'3月'),
    (0x32C3, 'M', u'4月'),
    (0x32C4, 'M', u'5月'),
    (0x32C5, 'M', u'6月'),
    (0x32C6, 'M', u'7月'),
    (0x32C7, 'M', u'8月'),
    (0x32C8, 'M', u'9月'),
    (0x32C9, 'M', u'10月'),
    (0x32CA, 'M', u'11月'),
    (0x32CB, 'M', u'12月'),
    (0x32CC, 'M', u'hg'),
    (0x32CD, 'M', u'erg'),
    (0x32CE, 'M', u'ev'),
    (0x32CF, 'M', u'ltd'),
    (0x32D0, 'M', u'ア'),
    (0x32D1, 'M', u'イ'),
    (0x32D2, 'M', u'ウ'),
    (0x32D3, 'M', u'エ'),
    (0x32D4, 'M', u'オ'),
    (0x32D5, 'M', u'カ'),
    (0x32D6, 'M', u'キ'),
    (0x32D7, 'M', u'ク'),
    (0x32D8, 'M', u'ケ'),
    (0x32D9, 'M', u'コ'),
    (0x32DA, 'M', u'サ'),
    (0x32DB, 'M', u'シ'),
    (0x32DC, 'M', u'ス'),
    (0x32DD, 'M', u'セ'),
    (0x32DE, 'M', u'ソ'),
    (0x32DF, 'M', u'タ'),
    (0x32E0, 'M', u'チ'),
    (0x32E1, 'M', u'ツ'),
    (0x32E2, 'M', u'テ'),
    (0x32E3, 'M', u'ト'),
    (0x32E4, 'M', u'ナ'),
    (0x32E5, 'M', u'ニ'),
    (0x32E6, 'M', u'ヌ'),
    (0x32E7, 'M', u'ネ'),
    (0x32E8, 'M', u'ノ'),
    (0x32E9, 'M', u'ハ'),
    (0x32EA, 'M', u'ヒ'),
    (0x32EB, 'M', u'フ'),
    (0x32EC, 'M', u'ヘ'),
    (0x32ED, 'M', u'ホ'),
    (0x32EE, 'M', u'マ'),
    (0x32EF, 'M', u'ミ'),
    (0x32F0, 'M', u'ム'),
    (0x32F1, 'M', u'メ'),
    (0x32F2, 'M', u'モ'),
    (0x32F3, 'M', u'ヤ'),
    (0x32F4, 'M', u'ユ'),
    (0x32F5, 'M', u'ヨ'),
    (0x32F6, 'M', u'ラ'),
    (0x32F7, 'M', u'リ'),
    (0x32F8, 'M', u'ル'),
    (0x32F9, 'M', u'レ'),
    (0x32FA, 'M', u'ロ'),
    (0x32FB, 'M', u'ワ'),
    (0x32FC, 'M', u'ヰ'),
    (0x32FD, 'M', u'ヱ'),
    (0x32FE, 'M', u'ヲ'),
    (0x32FF, 'M', u'令和'),
    (0x3300, 'M', u'アパート'),
    (0x3301, 'M', u'アルファ'),
    (0x3302, 'M', u'アンペア'),
    (0x3303, 'M', u'アール'),
    (0x3304, 'M', u'イニング'),
    (0x3305, 'M', u'インチ'),
    (0x3306, 'M', u'ウォン'),
    (0x3307, 'M', u'エスクード'),
    (0x3308, 'M', u'エーカー'),
    (0x3309, 'M', u'オンス'),
    (0x330A, 'M', u'オーム'),
    (0x330B, 'M', u'カイリ'),
    (0x330C, 'M', u'カラット'),
    (0x330D, 'M', u'カロリー'),
    (0x330E, 'M', u'ガロン'),
    (0x330F, 'M', u'ガンマ'),
    (0x3310, 'M', u'ギガ'),
    (0x3311, 'M', u'ギニー'),
    ]

def _seg_33():
    return [
    (0x3312, 'M', u'キュリー'),
    (0x3313, 'M', u'ギルダー'),
    (0x3314, 'M', u'キロ'),
    (0x3315, 'M', u'キログラム'),
    (0x3316, 'M', u'キロメートル'),
    (0x3317, 'M', u'キロワット'),
    (0x3318, 'M', u'グラム'),
    (0x3319, 'M', u'グラムトン'),
    (0x331A, 'M', u'クルゼイロ'),
    (0x331B, 'M', u'クローネ'),
    (0x331C, 'M', u'ケース'),
    (0x331D, 'M', u'コルナ'),
    (0x331E, 'M', u'コーポ'),
    (0x331F, 'M', u'サイクル'),
    (0x3320, 'M', u'サンチーム'),
    (0x3321, 'M', u'シリング'),
    (0x3322, 'M', u'センチ'),
    (0x3323, 'M', u'セント'),
    (0x3324, 'M', u'ダース'),
    (0x3325, 'M', u'デシ'),
    (0x3326, 'M', u'ドル'),
    (0x3327, 'M', u'トン'),
    (0x3328, 'M', u'ナノ'),
    (0x3329, 'M', u'ノット'),
    (0x332A, 'M', u'ハイツ'),
    (0x332B, 'M', u'パーセント'),
    (0x332C, 'M', u'パーツ'),
    (0x332D, 'M', u'バーレル'),
    (0x332E, 'M', u'ピアストル'),
    (0x332F, 'M', u'ピクル'),
    (0x3330, 'M', u'ピコ'),
    (0x3331, 'M', u'ビル'),
    (0x3332, 'M', u'ファラッド'),
    (0x3333, 'M', u'フィート'),
    (0x3334, 'M', u'ブッシェル'),
    (0x3335, 'M', u'フラン'),
    (0x3336, 'M', u'ヘクタール'),
    (0x3337, 'M', u'ペソ'),
    (0x3338, 'M', u'ペニヒ'),
    (0x3339, 'M', u'ヘルツ'),
    (0x333A, 'M', u'ペンス'),
    (0x333B, 'M', u'ページ'),
    (0x333C, 'M', u'ベータ'),
    (0x333D, 'M', u'ポイント'),
    (0x333E, 'M', u'ボルト'),
    (0x333F, 'M', u'ホン'),
    (0x3340, 'M', u'ポンド'),
    (0x3341, 'M', u'ホール'),
    (0x3342, 'M', u'ホーン'),
    (0x3343, 'M', u'マイクロ'),
    (0x3344, 'M', u'マイル'),
    (0x3345, 'M', u'マッハ'),
    (0x3346, 'M', u'マルク'),
    (0x3347, 'M', u'マンション'),
    (0x3348, 'M', u'ミクロン'),
    (0x3349, 'M', u'ミリ'),
    (0x334A, 'M', u'ミリバール'),
    (0x334B, 'M', u'メガ'),
    (0x334C, 'M', u'メガトン'),
    (0x334D, 'M', u'メートル'),
    (0x334E, 'M', u'ヤード'),
    (0x334F, 'M', u'ヤール'),
    (0x3350, 'M', u'ユアン'),
    (0x3351, 'M', u'リットル'),
    (0x3352, 'M', u'リラ'),
    (0x3353, 'M', u'ルピー'),
    (0x3354, 'M', u'ルーブル'),
    (0x3355, 'M', u'レム'),
    (0x3356, 'M', u'レントゲン'),
    (0x3357, 'M', u'ワット'),
    (0x3358, 'M', u'0点'),
    (0x3359, 'M', u'1点'),
    (0x335A, 'M', u'2点'),
    (0x335B, 'M', u'3点'),
    (0x335C, 'M', u'4点'),
    (0x335D, 'M', u'5点'),
    (0x335E, 'M', u'6点'),
    (0x335F, 'M', u'7点'),
    (0x3360, 'M', u'8点'),
    (0x3361, 'M', u'9点'),
    (0x3362, 'M', u'10点'),
    (0x3363, 'M', u'11点'),
    (0x3364, 'M', u'12点'),
    (0x3365, 'M', u'13点'),
    (0x3366, 'M', u'14点'),
    (0x3367, 'M', u'15点'),
    (0x3368, 'M', u'16点'),
    (0x3369, 'M', u'17点'),
    (0x336A, 'M', u'18点'),
    (0x336B, 'M', u'19点'),
    (0x336C, 'M', u'20点'),
    (0x336D, 'M', u'21点'),
    (0x336E, 'M', u'22点'),
    (0x336F, 'M', u'23点'),
    (0x3370, 'M', u'24点'),
    (0x3371, 'M', u'hpa'),
    (0x3372, 'M', u'da'),
    (0x3373, 'M', u'au'),
    (0x3374, 'M', u'bar'),
    (0x3375, 'M', u'ov'),
    ]

def _seg_34():
    return [
    (0x3376, 'M', u'pc'),
    (0x3377, 'M', u'dm'),
    (0x3378, 'M', u'dm2'),
    (0x3379, 'M', u'dm3'),
    (0x337A, 'M', u'iu'),
    (0x337B, 'M', u'平成'),
    (0x337C, 'M', u'昭和'),
    (0x337D, 'M', u'大正'),
    (0x337E, 'M', u'明治'),
    (0x337F, 'M', u'株式会社'),
    (0x3380, 'M', u'pa'),
    (0x3381, 'M', u'na'),
    (0x3382, 'M', u'μa'),
    (0x3383, 'M', u'ma'),
    (0x3384, 'M', u'ka'),
    (0x3385, 'M', u'kb'),
    (0x3386, 'M', u'mb'),
    (0x3387, 'M', u'gb'),
    (0x3388, 'M', u'cal'),
    (0x3389, 'M', u'kcal'),
    (0x338A, 'M', u'pf'),
    (0x338B, 'M', u'nf'),
    (0x338C, 'M', u'μf'),
    (0x338D, 'M', u'μg'),
    (0x338E, 'M', u'mg'),
    (0x338F, 'M', u'kg'),
    (0x3390, 'M', u'hz'),
    (0x3391, 'M', u'khz'),
    (0x3392, 'M', u'mhz'),
    (0x3393, 'M', u'ghz'),
    (0x3394, 'M', u'thz'),
    (0x3395, 'M', u'μl'),
    (0x3396, 'M', u'ml'),
    (0x3397, 'M', u'dl'),
    (0x3398, 'M', u'kl'),
    (0x3399, 'M', u'fm'),
    (0x339A, 'M', u'nm'),
    (0x339B, 'M', u'μm'),
    (0x339C, 'M', u'mm'),
    (0x339D, 'M', u'cm'),
    (0x339E, 'M', u'km'),
    (0x339F, 'M', u'mm2'),
    (0x33A0, 'M', u'cm2'),
    (0x33A1, 'M', u'm2'),
    (0x33A2, 'M', u'km2'),
    (0x33A3, 'M', u'mm3'),
    (0x33A4, 'M', u'cm3'),
    (0x33A5, 'M', u'm3'),
    (0x33A6, 'M', u'km3'),
    (0x33A7, 'M', u'm∕s'),
    (0x33A8, 'M', u'm∕s2'),
    (0x33A9, 'M', u'pa'),
    (0x33AA, 'M', u'kpa'),
    (0x33AB, 'M', u'mpa'),
    (0x33AC, 'M', u'gpa'),
    (0x33AD, 'M', u'rad'),
    (0x33AE, 'M', u'rad∕s'),
    (0x33AF, 'M', u'rad∕s2'),
    (0x33B0, 'M', u'ps'),
    (0x33B1, 'M', u'ns'),
    (0x33B2, 'M', u'μs'),
    (0x33B3, 'M', u'ms'),
    (0x33B4, 'M', u'pv'),
    (0x33B5, 'M', u'nv'),
    (0x33B6, 'M', u'μv'),
    (0x33B7, 'M', u'mv'),
    (0x33B8, 'M', u'kv'),
    (0x33B9, 'M', u'mv'),
    (0x33BA, 'M', u'pw'),
    (0x33BB, 'M', u'nw'),
    (0x33BC, 'M', u'μw'),
    (0x33BD, 'M', u'mw'),
    (0x33BE, 'M', u'kw'),
    (0x33BF, 'M', u'mw'),
    (0x33C0, 'M', u'kω'),
    (0x33C1, 'M', u'mω'),
    (0x33C2, 'X'),
    (0x33C3, 'M', u'bq'),
    (0x33C4, 'M', u'cc'),
    (0x33C5, 'M', u'cd'),
    (0x33C6, 'M', u'c∕kg'),
    (0x33C7, 'X'),
    (0x33C8, 'M', u'db'),
    (0x33C9, 'M', u'gy'),
    (0x33CA, 'M', u'ha'),
    (0x33CB, 'M', u'hp'),
    (0x33CC, 'M', u'in'),
    (0x33CD, 'M', u'kk'),
    (0x33CE, 'M', u'km'),
    (0x33CF, 'M', u'kt'),
    (0x33D0, 'M', u'lm'),
    (0x33D1, 'M', u'ln'),
    (0x33D2, 'M', u'log'),
    (0x33D3, 'M', u'lx'),
    (0x33D4, 'M', u'mb'),
    (0x33D5, 'M', u'mil'),
    (0x33D6, 'M', u'mol'),
    (0x33D7, 'M', u'ph'),
    (0x33D8, 'X'),
    (0x33D9, 'M', u'ppm'),
    ]

def _seg_35():
    return [
    (0x33DA, 'M', u'pr'),
    (0x33DB, 'M', u'sr'),
    (0x33DC, 'M', u'sv'),
    (0x33DD, 'M', u'wb'),
    (0x33DE, 'M', u'v∕m'),
    (0x33DF, 'M', u'a∕m'),
    (0x33E0, 'M', u'1日'),
    (0x33E1, 'M', u'2日'),
    (0x33E2, 'M', u'3日'),
    (0x33E3, 'M', u'4日'),
    (0x33E4, 'M', u'5日'),
    (0x33E5, 'M', u'6日'),
    (0x33E6, 'M', u'7日'),
    (0x33E7, 'M', u'8日'),
    (0x33E8, 'M', u'9日'),
    (0x33E9, 'M', u'10日'),
    (0x33EA, 'M', u'11日'),
    (0x33EB, 'M', u'12日'),
    (0x33EC, 'M', u'13日'),
    (0x33ED, 'M', u'14日'),
    (0x33EE, 'M', u'15日'),
    (0x33EF, 'M', u'16日'),
    (0x33F0, 'M', u'17日'),
    (0x33F1, 'M', u'18日'),
    (0x33F2, 'M', u'19日'),
    (0x33F3, 'M', u'20日'),
    (0x33F4, 'M', u'21日'),
    (0x33F5, 'M', u'22日'),
    (0x33F6, 'M', u'23日'),
    (0x33F7, 'M', u'24日'),
    (0x33F8, 'M', u'25日'),
    (0x33F9, 'M', u'26日'),
    (0x33FA, 'M', u'27日'),
    (0x33FB, 'M', u'28日'),
    (0x33FC, 'M', u'29日'),
    (0x33FD, 'M', u'30日'),
    (0x33FE, 'M', u'31日'),
    (0x33FF, 'M', u'gal'),
    (0x3400, 'V'),
    (0x9FFD, 'X'),
    (0xA000, 'V'),
    (0xA48D, 'X'),
    (0xA490, 'V'),
    (0xA4C7, 'X'),
    (0xA4D0, 'V'),
    (0xA62C, 'X'),
    (0xA640, 'M', u'ꙁ'),
    (0xA641, 'V'),
    (0xA642, 'M', u'ꙃ'),
    (0xA643, 'V'),
    (0xA644, 'M', u'ꙅ'),
    (0xA645, 'V'),
    (0xA646, 'M', u'ꙇ'),
    (0xA647, 'V'),
    (0xA648, 'M', u'ꙉ'),
    (0xA649, 'V'),
    (0xA64A, 'M', u'ꙋ'),
    (0xA64B, 'V'),
    (0xA64C, 'M', u'ꙍ'),
    (0xA64D, 'V'),
    (0xA64E, 'M', u'ꙏ'),
    (0xA64F, 'V'),
    (0xA650, 'M', u'ꙑ'),
    (0xA651, 'V'),
    (0xA652, 'M', u'ꙓ'),
    (0xA653, 'V'),
    (0xA654, 'M', u'ꙕ'),
    (0xA655, 'V'),
    (0xA656, 'M', u'ꙗ'),
    (0xA657, 'V'),
    (0xA658, 'M', u'ꙙ'),
    (0xA659, 'V'),
    (0xA65A, 'M', u'ꙛ'),
    (0xA65B, 'V'),
    (0xA65C, 'M', u'ꙝ'),
    (0xA65D, 'V'),
    (0xA65E, 'M', u'ꙟ'),
    (0xA65F, 'V'),
    (0xA660, 'M', u'ꙡ'),
    (0xA661, 'V'),
    (0xA662, 'M', u'ꙣ'),
    (0xA663, 'V'),
    (0xA664, 'M', u'ꙥ'),
    (0xA665, 'V'),
    (0xA666, 'M', u'ꙧ'),
    (0xA667, 'V'),
    (0xA668, 'M', u'ꙩ'),
    (0xA669, 'V'),
    (0xA66A, 'M', u'ꙫ'),
    (0xA66B, 'V'),
    (0xA66C, 'M', u'ꙭ'),
    (0xA66D, 'V'),
    (0xA680, 'M', u'ꚁ'),
    (0xA681, 'V'),
    (0xA682, 'M', u'ꚃ'),
    (0xA683, 'V'),
    (0xA684, 'M', u'ꚅ'),
    (0xA685, 'V'),
    (0xA686, 'M', u'ꚇ'),
    (0xA687, 'V'),
    ]

def _seg_36():
    return [
    (0xA688, 'M', u'ꚉ'),
    (0xA689, 'V'),
    (0xA68A, 'M', u'ꚋ'),
    (0xA68B, 'V'),
    (0xA68C, 'M', u'ꚍ'),
    (0xA68D, 'V'),
    (0xA68E, 'M', u'ꚏ'),
    (0xA68F, 'V'),
    (0xA690, 'M', u'ꚑ'),
    (0xA691, 'V'),
    (0xA692, 'M', u'ꚓ'),
    (0xA693, 'V'),
    (0xA694, 'M', u'ꚕ'),
    (0xA695, 'V'),
    (0xA696, 'M', u'ꚗ'),
    (0xA697, 'V'),
    (0xA698, 'M', u'ꚙ'),
    (0xA699, 'V'),
    (0xA69A, 'M', u'ꚛ'),
    (0xA69B, 'V'),
    (0xA69C, 'M', u'ъ'),
    (0xA69D, 'M', u'ь'),
    (0xA69E, 'V'),
    (0xA6F8, 'X'),
    (0xA700, 'V'),
    (0xA722, 'M', u'ꜣ'),
    (0xA723, 'V'),
    (0xA724, 'M', u'ꜥ'),
    (0xA725, 'V'),
    (0xA726, 'M', u'ꜧ'),
    (0xA727, 'V'),
    (0xA728, 'M', u'ꜩ'),
    (0xA729, 'V'),
    (0xA72A, 'M', u'ꜫ'),
    (0xA72B, 'V'),
    (0xA72C, 'M', u'ꜭ'),
    (0xA72D, 'V'),
    (0xA72E, 'M', u'ꜯ'),
    (0xA72F, 'V'),
    (0xA732, 'M', u'ꜳ'),
    (0xA733, 'V'),
    (0xA734, 'M', u'ꜵ'),
    (0xA735, 'V'),
    (0xA736, 'M', u'ꜷ'),
    (0xA737, 'V'),
    (0xA738, 'M', u'ꜹ'),
    (0xA739, 'V'),
    (0xA73A, 'M', u'ꜻ'),
    (0xA73B, 'V'),
    (0xA73C, 'M', u'ꜽ'),
    (0xA73D, 'V'),
    (0xA73E, 'M', u'ꜿ'),
    (0xA73F, 'V'),
    (0xA740, 'M', u'ꝁ'),
    (0xA741, 'V'),
    (0xA742, 'M', u'ꝃ'),
    (0xA743, 'V'),
    (0xA744, 'M', u'ꝅ'),
    (0xA745, 'V'),
    (0xA746, 'M', u'ꝇ'),
    (0xA747, 'V'),
    (0xA748, 'M', u'ꝉ'),
    (0xA749, 'V'),
    (0xA74A, 'M', u'ꝋ'),
    (0xA74B, 'V'),
    (0xA74C, 'M', u'ꝍ'),
    (0xA74D, 'V'),
    (0xA74E, 'M', u'ꝏ'),
    (0xA74F, 'V'),
    (0xA750, 'M', u'ꝑ'),
    (0xA751, 'V'),
    (0xA752, 'M', u'ꝓ'),
    (0xA753, 'V'),
    (0xA754, 'M', u'ꝕ'),
    (0xA755, 'V'),
    (0xA756, 'M', u'ꝗ'),
    (0xA757, 'V'),
    (0xA758, 'M', u'ꝙ'),
    (0xA759, 'V'),
    (0xA75A, 'M', u'ꝛ'),
    (0xA75B, 'V'),
    (0xA75C, 'M', u'ꝝ'),
    (0xA75D, 'V'),
    (0xA75E, 'M', u'ꝟ'),
    (0xA75F, 'V'),
    (0xA760, 'M', u'ꝡ'),
    (0xA761, 'V'),
    (0xA762, 'M', u'ꝣ'),
    (0xA763, 'V'),
    (0xA764, 'M', u'ꝥ'),
    (0xA765, 'V'),
    (0xA766, 'M', u'ꝧ'),
    (0xA767, 'V'),
    (0xA768, 'M', u'ꝩ'),
    (0xA769, 'V'),
    (0xA76A, 'M', u'ꝫ'),
    (0xA76B, 'V'),
    (0xA76C, 'M', u'ꝭ'),
    (0xA76D, 'V'),
    (0xA76E, 'M', u'ꝯ'),
    ]

def _seg_37():
    return [
    (0xA76F, 'V'),
    (0xA770, 'M', u'ꝯ'),
    (0xA771, 'V'),
    (0xA779, 'M', u'ꝺ'),
    (0xA77A, 'V'),
    (0xA77B, 'M', u'ꝼ'),
    (0xA77C, 'V'),
    (0xA77D, 'M', u'ᵹ'),
    (0xA77E, 'M', u'ꝿ'),
    (0xA77F, 'V'),
    (0xA780, 'M', u'ꞁ'),
    (0xA781, 'V'),
    (0xA782, 'M', u'ꞃ'),
    (0xA783, 'V'),
    (0xA784, 'M', u'ꞅ'),
    (0xA785, 'V'),
    (0xA786, 'M', u'ꞇ'),
    (0xA787, 'V'),
    (0xA78B, 'M', u'ꞌ'),
    (0xA78C, 'V'),
    (0xA78D, 'M', u'ɥ'),
    (0xA78E, 'V'),
    (0xA790, 'M', u'ꞑ'),
    (0xA791, 'V'),
    (0xA792, 'M', u'ꞓ'),
    (0xA793, 'V'),
    (0xA796, 'M', u'ꞗ'),
    (0xA797, 'V'),
    (0xA798, 'M', u'ꞙ'),
    (0xA799, 'V'),
    (0xA79A, 'M', u'ꞛ'),
    (0xA79B, 'V'),
    (0xA79C, 'M', u'ꞝ'),
    (0xA79D, 'V'),
    (0xA79E, 'M', u'ꞟ'),
    (0xA79F, 'V'),
    (0xA7A0, 'M', u'ꞡ'),
    (0xA7A1, 'V'),
    (0xA7A2, 'M', u'ꞣ'),
    (0xA7A3, 'V'),
    (0xA7A4, 'M', u'ꞥ'),
    (0xA7A5, 'V'),
    (0xA7A6, 'M', u'ꞧ'),
    (0xA7A7, 'V'),
    (0xA7A8, 'M', u'ꞩ'),
    (0xA7A9, 'V'),
    (0xA7AA, 'M', u'ɦ'),
    (0xA7AB, 'M', u'ɜ'),
    (0xA7AC, 'M', u'ɡ'),
    (0xA7AD, 'M', u'ɬ'),
    (0xA7AE, 'M', u'ɪ'),
    (0xA7AF, 'V'),
    (0xA7B0, 'M', u'ʞ'),
    (0xA7B1, 'M', u'ʇ'),
    (0xA7B2, 'M', u'ʝ'),
    (0xA7B3, 'M', u'ꭓ'),
    (0xA7B4, 'M', u'ꞵ'),
    (0xA7B5, 'V'),
    (0xA7B6, 'M', u'ꞷ'),
    (0xA7B7, 'V'),
    (0xA7B8, 'M', u'ꞹ'),
    (0xA7B9, 'V'),
    (0xA7BA, 'M', u'ꞻ'),
    (0xA7BB, 'V'),
    (0xA7BC, 'M', u'ꞽ'),
    (0xA7BD, 'V'),
    (0xA7BE, 'M', u'ꞿ'),
    (0xA7BF, 'V'),
    (0xA7C0, 'X'),
    (0xA7C2, 'M', u'ꟃ'),
    (0xA7C3, 'V'),
    (0xA7C4, 'M', u'ꞔ'),
    (0xA7C5, 'M', u'ʂ'),
    (0xA7C6, 'M', u'ᶎ'),
    (0xA7C7, 'M', u'ꟈ'),
    (0xA7C8, 'V'),
    (0xA7C9, 'M', u'ꟊ'),
    (0xA7CA, 'V'),
    (0xA7CB, 'X'),
    (0xA7F5, 'M', u'ꟶ'),
    (0xA7F6, 'V'),
    (0xA7F8, 'M', u'ħ'),
    (0xA7F9, 'M', u'œ'),
    (0xA7FA, 'V'),
    (0xA82D, 'X'),
    (0xA830, 'V'),
    (0xA83A, 'X'),
    (0xA840, 'V'),
    (0xA878, 'X'),
    (0xA880, 'V'),
    (0xA8C6, 'X'),
    (0xA8CE, 'V'),
    (0xA8DA, 'X'),
    (0xA8E0, 'V'),
    (0xA954, 'X'),
    (0xA95F, 'V'),
    (0xA97D, 'X'),
    (0xA980, 'V'),
    (0xA9CE, 'X'),
    (0xA9CF, 'V'),
    ]

def _seg_38():
    return [
    (0xA9DA, 'X'),
    (0xA9DE, 'V'),
    (0xA9FF, 'X'),
    (0xAA00, 'V'),
    (0xAA37, 'X'),
    (0xAA40, 'V'),
    (0xAA4E, 'X'),
    (0xAA50, 'V'),
    (0xAA5A, 'X'),
    (0xAA5C, 'V'),
    (0xAAC3, 'X'),
    (0xAADB, 'V'),
    (0xAAF7, 'X'),
    (0xAB01, 'V'),
    (0xAB07, 'X'),
    (0xAB09, 'V'),
    (0xAB0F, 'X'),
    (0xAB11, 'V'),
    (0xAB17, 'X'),
    (0xAB20, 'V'),
    (0xAB27, 'X'),
    (0xAB28, 'V'),
    (0xAB2F, 'X'),
    (0xAB30, 'V'),
    (0xAB5C, 'M', u'ꜧ'),
    (0xAB5D, 'M', u'ꬷ'),
    (0xAB5E, 'M', u'ɫ'),
    (0xAB5F, 'M', u'ꭒ'),
    (0xAB60, 'V'),
    (0xAB69, 'M', u'ʍ'),
    (0xAB6A, 'V'),
    (0xAB6C, 'X'),
    (0xAB70, 'M', u'Ꭰ'),
    (0xAB71, 'M', u'Ꭱ'),
    (0xAB72, 'M', u'Ꭲ'),
    (0xAB73, 'M', u'Ꭳ'),
    (0xAB74, 'M', u'Ꭴ'),
    (0xAB75, 'M', u'Ꭵ'),
    (0xAB76, 'M', u'Ꭶ'),
    (0xAB77, 'M', u'Ꭷ'),
    (0xAB78, 'M', u'Ꭸ'),
    (0xAB79, 'M', u'Ꭹ'),
    (0xAB7A, 'M', u'Ꭺ'),
    (0xAB7B, 'M', u'Ꭻ'),
    (0xAB7C, 'M', u'Ꭼ'),
    (0xAB7D, 'M', u'Ꭽ'),
    (0xAB7E, 'M', u'Ꭾ'),
    (0xAB7F, 'M', u'Ꭿ'),
    (0xAB80, 'M', u'Ꮀ'),
    (0xAB81, 'M', u'Ꮁ'),
    (0xAB82, 'M', u'Ꮂ'),
    (0xAB83, 'M', u'Ꮃ'),
    (0xAB84, 'M', u'Ꮄ'),
    (0xAB85, 'M', u'Ꮅ'),
    (0xAB86, 'M', u'Ꮆ'),
    (0xAB87, 'M', u'Ꮇ'),
    (0xAB88, 'M', u'Ꮈ'),
    (0xAB89, 'M', u'Ꮉ'),
    (0xAB8A, 'M', u'Ꮊ'),
    (0xAB8B, 'M', u'Ꮋ'),
    (0xAB8C, 'M', u'Ꮌ'),
    (0xAB8D, 'M', u'Ꮍ'),
    (0xAB8E, 'M', u'Ꮎ'),
    (0xAB8F, 'M', u'Ꮏ'),
    (0xAB90, 'M', u'Ꮐ'),
    (0xAB91, 'M', u'Ꮑ'),
    (0xAB92, 'M', u'Ꮒ'),
    (0xAB93, 'M', u'Ꮓ'),
    (0xAB94, 'M', u'Ꮔ'),
    (0xAB95, 'M', u'Ꮕ'),
    (0xAB96, 'M', u'Ꮖ'),
    (0xAB97, 'M', u'Ꮗ'),
    (0xAB98, 'M', u'Ꮘ'),
    (0xAB99, 'M', u'Ꮙ'),
    (0xAB9A, 'M', u'Ꮚ'),
    (0xAB9B, 'M', u'Ꮛ'),
    (0xAB9C, 'M', u'Ꮜ'),
    (0xAB9D, 'M', u'Ꮝ'),
    (0xAB9E, 'M', u'Ꮞ'),
    (0xAB9F, 'M', u'Ꮟ'),
    (0xABA0, 'M', u'Ꮠ'),
    (0xABA1, 'M', u'Ꮡ'),
    (0xABA2, 'M', u'Ꮢ'),
    (0xABA3, 'M', u'Ꮣ'),
    (0xABA4, 'M', u'Ꮤ'),
    (0xABA5, 'M', u'Ꮥ'),
    (0xABA6, 'M', u'Ꮦ'),
    (0xABA7, 'M', u'Ꮧ'),
    (0xABA8, 'M', u'Ꮨ'),
    (0xABA9, 'M', u'Ꮩ'),
    (0xABAA, 'M', u'Ꮪ'),
    (0xABAB, 'M', u'Ꮫ'),
    (0xABAC, 'M', u'Ꮬ'),
    (0xABAD, 'M', u'Ꮭ'),
    (0xABAE, 'M', u'Ꮮ'),
    (0xABAF, 'M', u'Ꮯ'),
    (0xABB0, 'M', u'Ꮰ'),
    (0xABB1, 'M', u'Ꮱ'),
    (0xABB2, 'M', u'Ꮲ'),
    (0xABB3, 'M', u'Ꮳ'),
    ]

def _seg_39():
    return [
    (0xABB4, 'M', u'Ꮴ'),
    (0xABB5, 'M', u'Ꮵ'),
    (0xABB6, 'M', u'Ꮶ'),
    (0xABB7, 'M', u'Ꮷ'),
    (0xABB8, 'M', u'Ꮸ'),
    (0xABB9, 'M', u'Ꮹ'),
    (0xABBA, 'M', u'Ꮺ'),
    (0xABBB, 'M', u'Ꮻ'),
    (0xABBC, 'M', u'Ꮼ'),
    (0xABBD, 'M', u'Ꮽ'),
    (0xABBE, 'M', u'Ꮾ'),
    (0xABBF, 'M', u'Ꮿ'),
    (0xABC0, 'V'),
    (0xABEE, 'X'),
    (0xABF0, 'V'),
    (0xABFA, 'X'),
    (0xAC00, 'V'),
    (0xD7A4, 'X'),
    (0xD7B0, 'V'),
    (0xD7C7, 'X'),
    (0xD7CB, 'V'),
    (0xD7FC, 'X'),
    (0xF900, 'M', u'豈'),
    (0xF901, 'M', u'更'),
    (0xF902, 'M', u'車'),
    (0xF903, 'M', u'賈'),
    (0xF904, 'M', u'滑'),
    (0xF905, 'M', u'串'),
    (0xF906, 'M', u'句'),
    (0xF907, 'M', u'龜'),
    (0xF909, 'M', u'契'),
    (0xF90A, 'M', u'金'),
    (0xF90B, 'M', u'喇'),
    (0xF90C, 'M', u'奈'),
    (0xF90D, 'M', u'懶'),
    (0xF90E, 'M', u'癩'),
    (0xF90F, 'M', u'羅'),
    (0xF910, 'M', u'蘿'),
    (0xF911, 'M', u'螺'),
    (0xF912, 'M', u'裸'),
    (0xF913, 'M', u'邏'),
    (0xF914, 'M', u'樂'),
    (0xF915, 'M', u'洛'),
    (0xF916, 'M', u'烙'),
    (0xF917, 'M', u'珞'),
    (0xF918, 'M', u'落'),
    (0xF919, 'M', u'酪'),
    (0xF91A, 'M', u'駱'),
    (0xF91B, 'M', u'亂'),
    (0xF91C, 'M', u'卵'),
    (0xF91D, 'M', u'欄'),
    (0xF91E, 'M', u'爛'),
    (0xF91F, 'M', u'蘭'),
    (0xF920, 'M', u'鸞'),
    (0xF921, 'M', u'嵐'),
    (0xF922, 'M', u'濫'),
    (0xF923, 'M', u'藍'),
    (0xF924, 'M', u'襤'),
    (0xF925, 'M', u'拉'),
    (0xF926, 'M', u'臘'),
    (0xF927, 'M', u'蠟'),
    (0xF928, 'M', u'廊'),
    (0xF929, 'M', u'朗'),
    (0xF92A, 'M', u'浪'),
    (0xF92B, 'M', u'狼'),
    (0xF92C, 'M', u'郎'),
    (0xF92D, 'M', u'來'),
    (0xF92E, 'M', u'冷'),
    (0xF92F, 'M', u'勞'),
    (0xF930, 'M', u'擄'),
    (0xF931, 'M', u'櫓'),
    (0xF932, 'M', u'爐'),
    (0xF933, 'M', u'盧'),
    (0xF934, 'M', u'老'),
    (0xF935, 'M', u'蘆'),
    (0xF936, 'M', u'虜'),
    (0xF937, 'M', u'路'),
    (0xF938, 'M', u'露'),
    (0xF939, 'M', u'魯'),
    (0xF93A, 'M', u'鷺'),
    (0xF93B, 'M', u'碌'),
    (0xF93C, 'M', u'祿'),
    (0xF93D, 'M', u'綠'),
    (0xF93E, 'M', u'菉'),
    (0xF93F, 'M', u'錄'),
    (0xF940, 'M', u'鹿'),
    (0xF941, 'M', u'論'),
    (0xF942, 'M', u'壟'),
    (0xF943, 'M', u'弄'),
    (0xF944, 'M', u'籠'),
    (0xF945, 'M', u'聾'),
    (0xF946, 'M', u'牢'),
    (0xF947, 'M', u'磊'),
    (0xF948, 'M', u'賂'),
    (0xF949, 'M', u'雷'),
    (0xF94A, 'M', u'壘'),
    (0xF94B, 'M', u'屢'),
    (0xF94C, 'M', u'樓'),
    (0xF94D, 'M', u'淚'),
    (0xF94E, 'M', u'漏'),
    ]

def _seg_40():
    return [
    (0xF94F, 'M', u'累'),
    (0xF950, 'M', u'縷'),
    (0xF951, 'M', u'陋'),
    (0xF952, 'M', u'勒'),
    (0xF953, 'M', u'肋'),
    (0xF954, 'M', u'凜'),
    (0xF955, 'M', u'凌'),
    (0xF956, 'M', u'稜'),
    (0xF957, 'M', u'綾'),
    (0xF958, 'M', u'菱'),
    (0xF959, 'M', u'陵'),
    (0xF95A, 'M', u'讀'),
    (0xF95B, 'M', u'拏'),
    (0xF95C, 'M', u'樂'),
    (0xF95D, 'M', u'諾'),
    (0xF95E, 'M', u'丹'),
    (0xF95F, 'M', u'寧'),
    (0xF960, 'M', u'怒'),
    (0xF961, 'M', u'率'),
    (0xF962, 'M', u'異'),
    (0xF963, 'M', u'北'),
    (0xF964, 'M', u'磻'),
    (0xF965, 'M', u'便'),
    (0xF966, 'M', u'復'),
    (0xF967, 'M', u'不'),
    (0xF968, 'M', u'泌'),
    (0xF969, 'M', u'數'),
    (0xF96A, 'M', u'索'),
    (0xF96B, 'M', u'參'),
    (0xF96C, 'M', u'塞'),
    (0xF96D, 'M', u'省'),
    (0xF96E, 'M', u'葉'),
    (0xF96F, 'M', u'說'),
    (0xF970, 'M', u'殺'),
    (0xF971, 'M', u'辰'),
    (0xF972, 'M', u'沈'),
    (0xF973, 'M', u'拾'),
    (0xF974, 'M', u'若'),
    (0xF975, 'M', u'掠'),
    (0xF976, 'M', u'略'),
    (0xF977, 'M', u'亮'),
    (0xF978, 'M', u'兩'),
    (0xF979, 'M', u'凉'),
    (0xF97A, 'M', u'梁'),
    (0xF97B, 'M', u'糧'),
    (0xF97C, 'M', u'良'),
    (0xF97D, 'M', u'諒'),
    (0xF97E, 'M', u'量'),
    (0xF97F, 'M', u'勵'),
    (0xF980, 'M', u'呂'),
    (0xF981, 'M', u'女'),
    (0xF982, 'M', u'廬'),
    (0xF983, 'M', u'旅'),
    (0xF984, 'M', u'濾'),
    (0xF985, 'M', u'礪'),
    (0xF986, 'M', u'閭'),
    (0xF987, 'M', u'驪'),
    (0xF988, 'M', u'麗'),
    (0xF989, 'M', u'黎'),
    (0xF98A, 'M', u'力'),
    (0xF98B, 'M', u'曆'),
    (0xF98C, 'M', u'歷'),
    (0xF98D, 'M', u'轢'),
    (0xF98E, 'M', u'年'),
    (0xF98F, 'M', u'憐'),
    (0xF990, 'M', u'戀'),
    (0xF991, 'M', u'撚'),
    (0xF992, 'M', u'漣'),
    (0xF993, 'M', u'煉'),
    (0xF994, 'M', u'璉'),
    (0xF995, 'M', u'秊'),
    (0xF996, 'M', u'練'),
    (0xF997, 'M', u'聯'),
    (0xF998, 'M', u'輦'),
    (0xF999, 'M', u'蓮'),
    (0xF99A, 'M', u'連'),
    (0xF99B, 'M', u'鍊'),
    (0xF99C, 'M', u'列'),
    (0xF99D, 'M', u'劣'),
    (0xF99E, 'M', u'咽'),
    (0xF99F, 'M', u'烈'),
    (0xF9A0, 'M', u'裂'),
    (0xF9A1, 'M', u'說'),
    (0xF9A2, 'M', u'廉'),
    (0xF9A3, 'M', u'念'),
    (0xF9A4, 'M', u'捻'),
    (0xF9A5, 'M', u'殮'),
    (0xF9A6, 'M', u'簾'),
    (0xF9A7, 'M', u'獵'),
    (0xF9A8, 'M', u'令'),
    (0xF9A9, 'M', u'囹'),
    (0xF9AA, 'M', u'寧'),
    (0xF9AB, 'M', u'嶺'),
    (0xF9AC, 'M', u'怜'),
    (0xF9AD, 'M', u'玲'),
    (0xF9AE, 'M', u'瑩'),
    (0xF9AF, 'M', u'羚'),
    (0xF9B0, 'M', u'聆'),
    (0xF9B1, 'M', u'鈴'),
    (0xF9B2, 'M', u'零'),
    ]

def _seg_41():
    return [
    (0xF9B3, 'M', u'靈'),
    (0xF9B4, 'M', u'領'),
    (0xF9B5, 'M', u'例'),
    (0xF9B6, 'M', u'禮'),
    (0xF9B7, 'M', u'醴'),
    (0xF9B8, 'M', u'隸'),
    (0xF9B9, 'M', u'惡'),
    (0xF9BA, 'M', u'了'),
    (0xF9BB, 'M', u'僚'),
    (0xF9BC, 'M', u'寮'),
    (0xF9BD, 'M', u'尿'),
    (0xF9BE, 'M', u'料'),
    (0xF9BF, 'M', u'樂'),
    (0xF9C0, 'M', u'燎'),
    (0xF9C1, 'M', u'療'),
    (0xF9C2, 'M', u'蓼'),
    (0xF9C3, 'M', u'遼'),
    (0xF9C4, 'M', u'龍'),
    (0xF9C5, 'M', u'暈'),
    (0xF9C6, 'M', u'阮'),
    (0xF9C7, 'M', u'劉'),
    (0xF9C8, 'M', u'杻'),
    (0xF9C9, 'M', u'柳'),
    (0xF9CA, 'M', u'流'),
    (0xF9CB, 'M', u'溜'),
    (0xF9CC, 'M', u'琉'),
    (0xF9CD, 'M', u'留'),
    (0xF9CE, 'M', u'硫'),
    (0xF9CF, 'M', u'紐'),
    (0xF9D0, 'M', u'類'),
    (0xF9D1, 'M', u'六'),
    (0xF9D2, 'M', u'戮'),
    (0xF9D3, 'M', u'陸'),
    (0xF9D4, 'M', u'倫'),
    (0xF9D5, 'M', u'崙'),
    (0xF9D6, 'M', u'淪'),
    (0xF9D7, 'M', u'輪'),
    (0xF9D8, 'M', u'律'),
    (0xF9D9, 'M', u'慄'),
    (0xF9DA, 'M', u'栗'),
    (0xF9DB, 'M', u'率'),
    (0xF9DC, 'M', u'隆'),
    (0xF9DD, 'M', u'利'),
    (0xF9DE, 'M', u'吏'),
    (0xF9DF, 'M', u'履'),
    (0xF9E0, 'M', u'易'),
    (0xF9E1, 'M', u'李'),
    (0xF9E2, 'M', u'梨'),
    (0xF9E3, 'M', u'泥'),
    (0xF9E4, 'M', u'理'),
    (0xF9E5, 'M', u'痢'),
    (0xF9E6, 'M', u'罹'),
    (0xF9E7, 'M', u'裏'),
    (0xF9E8, 'M', u'裡'),
    (0xF9E9, 'M', u'里'),
    (0xF9EA, 'M', u'離'),
    (0xF9EB, 'M', u'匿'),
    (0xF9EC, 'M', u'溺'),
    (0xF9ED, 'M', u'吝'),
    (0xF9EE, 'M', u'燐'),
    (0xF9EF, 'M', u'璘'),
    (0xF9F0, 'M', u'藺'),
    (0xF9F1, 'M', u'隣'),
    (0xF9F2, 'M', u'鱗'),
    (0xF9F3, 'M', u'麟'),
    (0xF9F4, 'M', u'林'),
    (0xF9F5, 'M', u'淋'),
    (0xF9F6, 'M', u'臨'),
    (0xF9F7, 'M', u'立'),
    (0xF9F8, 'M', u'笠'),
    (0xF9F9, 'M', u'粒'),
    (0xF9FA, 'M', u'狀'),
    (0xF9FB, 'M', u'炙'),
    (0xF9FC, 'M', u'識'),
    (0xF9FD, 'M', u'什'),
    (0xF9FE, 'M', u'茶'),
    (0xF9FF, 'M', u'刺'),
    (0xFA00, 'M', u'切'),
    (0xFA01, 'M', u'度'),
    (0xFA02, 'M', u'拓'),
    (0xFA03, 'M', u'糖'),
    (0xFA04, 'M', u'宅'),
    (0xFA05, 'M', u'洞'),
    (0xFA06, 'M', u'暴'),
    (0xFA07, 'M', u'輻'),
    (0xFA08, 'M', u'行'),
    (0xFA09, 'M', u'降'),
    (0xFA0A, 'M', u'見'),
    (0xFA0B, 'M', u'廓'),
    (0xFA0C, 'M', u'兀'),
    (0xFA0D, 'M', u'嗀'),
    (0xFA0E, 'V'),
    (0xFA10, 'M', u'塚'),
    (0xFA11, 'V'),
    (0xFA12, 'M', u'晴'),
    (0xFA13, 'V'),
    (0xFA15, 'M', u'凞'),
    (0xFA16, 'M', u'猪'),
    (0xFA17, 'M', u'益'),
    (0xFA18, 'M', u'礼'),
    ]

def _seg_42():
    return [
    (0xFA19, 'M', u'神'),
    (0xFA1A, 'M', u'祥'),
    (0xFA1B, 'M', u'福'),
    (0xFA1C, 'M', u'靖'),
    (0xFA1D, 'M', u'精'),
    (0xFA1E, 'M', u'羽'),
    (0xFA1F, 'V'),
    (0xFA20, 'M', u'蘒'),
    (0xFA21, 'V'),
    (0xFA22, 'M', u'諸'),
    (0xFA23, 'V'),
    (0xFA25, 'M', u'逸'),
    (0xFA26, 'M', u'都'),
    (0xFA27, 'V'),
    (0xFA2A, 'M', u'飯'),
    (0xFA2B, 'M', u'飼'),
    (0xFA2C, 'M', u'館'),
    (0xFA2D, 'M', u'鶴'),
    (0xFA2E, 'M', u'郞'),
    (0xFA2F, 'M', u'隷'),
    (0xFA30, 'M', u'侮'),
    (0xFA31, 'M', u'僧'),
    (0xFA32, 'M', u'免'),
    (0xFA33, 'M', u'勉'),
    (0xFA34, 'M', u'勤'),
    (0xFA35, 'M', u'卑'),
    (0xFA36, 'M', u'喝'),
    (0xFA37, 'M', u'嘆'),
    (0xFA38, 'M', u'器'),
    (0xFA39, 'M', u'塀'),
    (0xFA3A, 'M', u'墨'),
    (0xFA3B, 'M', u'層'),
    (0xFA3C, 'M', u'屮'),
    (0xFA3D, 'M', u'悔'),
    (0xFA3E, 'M', u'慨'),
    (0xFA3F, 'M', u'憎'),
    (0xFA40, 'M', u'懲'),
    (0xFA41, 'M', u'敏'),
    (0xFA42, 'M', u'既'),
    (0xFA43, 'M', u'暑'),
    (0xFA44, 'M', u'梅'),
    (0xFA45, 'M', u'海'),
    (0xFA46, 'M', u'渚'),
    (0xFA47, 'M', u'漢'),
    (0xFA48, 'M', u'煮'),
    (0xFA49, 'M', u'爫'),
    (0xFA4A, 'M', u'琢'),
    (0xFA4B, 'M', u'碑'),
    (0xFA4C, 'M', u'社'),
    (0xFA4D, 'M', u'祉'),
    (0xFA4E, 'M', u'祈'),
    (0xFA4F, 'M', u'祐'),
    (0xFA50, 'M', u'祖'),
    (0xFA51, 'M', u'祝'),
    (0xFA52, 'M', u'禍'),
    (0xFA53, 'M', u'禎'),
    (0xFA54, 'M', u'穀'),
    (0xFA55, 'M', u'突'),
    (0xFA56, 'M', u'節'),
    (0xFA57, 'M', u'練'),
    (0xFA58, 'M', u'縉'),
    (0xFA59, 'M', u'繁'),
    (0xFA5A, 'M', u'署'),
    (0xFA5B, 'M', u'者'),
    (0xFA5C, 'M', u'臭'),
    (0xFA5D, 'M', u'艹'),
    (0xFA5F, 'M', u'著'),
    (0xFA60, 'M', u'褐'),
    (0xFA61, 'M', u'視'),
    (0xFA62, 'M', u'謁'),
    (0xFA63, 'M', u'謹'),
    (0xFA64, 'M', u'賓'),
    (0xFA65, 'M', u'贈'),
    (0xFA66, 'M', u'辶'),
    (0xFA67, 'M', u'逸'),
    (0xFA68, 'M', u'難'),
    (0xFA69, 'M', u'響'),
    (0xFA6A, 'M', u'頻'),
    (0xFA6B, 'M', u'恵'),
    (0xFA6C, 'M', u'𤋮'),
    (0xFA6D, 'M', u'舘'),
    (0xFA6E, 'X'),
    (0xFA70, 'M', u'並'),
    (0xFA71, 'M', u'况'),
    (0xFA72, 'M', u'全'),
    (0xFA73, 'M', u'侀'),
    (0xFA74, 'M', u'充'),
    (0xFA75, 'M', u'冀'),
    (0xFA76, 'M', u'勇'),
    (0xFA77, 'M', u'勺'),
    (0xFA78, 'M', u'喝'),
    (0xFA79, 'M', u'啕'),
    (0xFA7A, 'M', u'喙'),
    (0xFA7B, 'M', u'嗢'),
    (0xFA7C, 'M', u'塚'),
    (0xFA7D, 'M', u'墳'),
    (0xFA7E, 'M', u'奄'),
    (0xFA7F, 'M', u'奔'),
    (0xFA80, 'M', u'婢'),
    (0xFA81, 'M', u'嬨'),
    ]

def _seg_43():
    return [
    (0xFA82, 'M', u'廒'),
    (0xFA83, 'M', u'廙'),
    (0xFA84, 'M', u'彩'),
    (0xFA85, 'M', u'徭'),
    (0xFA86, 'M', u'惘'),
    (0xFA87, 'M', u'慎'),
    (0xFA88, 'M', u'愈'),
    (0xFA89, 'M', u'憎'),
    (0xFA8A, 'M', u'慠'),
    (0xFA8B, 'M', u'懲'),
    (0xFA8C, 'M', u'戴'),
    (0xFA8D, 'M', u'揄'),
    (0xFA8E, 'M', u'搜'),
    (0xFA8F, 'M', u'摒'),
    (0xFA90, 'M', u'敖'),
    (0xFA91, 'M', u'晴'),
    (0xFA92, 'M', u'朗'),
    (0xFA93, 'M', u'望'),
    (0xFA94, 'M', u'杖'),
    (0xFA95, 'M', u'歹'),
    (0xFA96, 'M', u'殺'),
    (0xFA97, 'M', u'流'),
    (0xFA98, 'M', u'滛'),
    (0xFA99, 'M', u'滋'),
    (0xFA9A, 'M', u'漢'),
    (0xFA9B, 'M', u'瀞'),
    (0xFA9C, 'M', u'煮'),
    (0xFA9D, 'M', u'瞧'),
    (0xFA9E, 'M', u'爵'),
    (0xFA9F, 'M', u'犯'),
    (0xFAA0, 'M', u'猪'),
    (0xFAA1, 'M', u'瑱'),
    (0xFAA2, 'M', u'甆'),
    (0xFAA3, 'M', u'画'),
    (0xFAA4, 'M', u'瘝'),
    (0xFAA5, 'M', u'瘟'),
    (0xFAA6, 'M', u'益'),
    (0xFAA7, 'M', u'盛'),
    (0xFAA8, 'M', u'直'),
    (0xFAA9, 'M', u'睊'),
    (0xFAAA, 'M', u'着'),
    (0xFAAB, 'M', u'磌'),
    (0xFAAC, 'M', u'窱'),
    (0xFAAD, 'M', u'節'),
    (0xFAAE, 'M', u'类'),
    (0xFAAF, 'M', u'絛'),
    (0xFAB0, 'M', u'練'),
    (0xFAB1, 'M', u'缾'),
    (0xFAB2, 'M', u'者'),
    (0xFAB3, 'M', u'荒'),
    (0xFAB4, 'M', u'華'),
    (0xFAB5, 'M', u'蝹'),
    (0xFAB6, 'M', u'襁'),
    (0xFAB7, 'M', u'覆'),
    (0xFAB8, 'M', u'視'),
    (0xFAB9, 'M', u'調'),
    (0xFABA, 'M', u'諸'),
    (0xFABB, 'M', u'請'),
    (0xFABC, 'M', u'謁'),
    (0xFABD, 'M', u'諾'),
    (0xFABE, 'M', u'諭'),
    (0xFABF, 'M', u'謹'),
    (0xFAC0, 'M', u'變'),
    (0xFAC1, 'M', u'贈'),
    (0xFAC2, 'M', u'輸'),
    (0xFAC3, 'M', u'遲'),
    (0xFAC4, 'M', u'醙'),
    (0xFAC5, 'M', u'鉶'),
    (0xFAC6, 'M', u'陼'),
    (0xFAC7, 'M', u'難'),
    (0xFAC8, 'M', u'靖'),
    (0xFAC9, 'M', u'韛'),
    (0xFACA, 'M', u'響'),
    (0xFACB, 'M', u'頋'),
    (0xFACC, 'M', u'頻'),
    (0xFACD, 'M', u'鬒'),
    (0xFACE, 'M', u'龜'),
    (0xFACF, 'M', u'𢡊'),
    (0xFAD0, 'M', u'𢡄'),
    (0xFAD1, 'M', u'𣏕'),
    (0xFAD2, 'M', u'㮝'),
    (0xFAD3, 'M', u'䀘'),
    (0xFAD4, 'M', u'䀹'),
    (0xFAD5, 'M', u'𥉉'),
    (0xFAD6, 'M', u'𥳐'),
    (0xFAD7, 'M', u'𧻓'),
    (0xFAD8, 'M', u'齃'),
    (0xFAD9, 'M', u'龎'),
    (0xFADA, 'X'),
    (0xFB00, 'M', u'ff'),
    (0xFB01, 'M', u'fi'),
    (0xFB02, 'M', u'fl'),
    (0xFB03, 'M', u'ffi'),
    (0xFB04, 'M', u'ffl'),
    (0xFB05, 'M', u'st'),
    (0xFB07, 'X'),
    (0xFB13, 'M', u'մն'),
    (0xFB14, 'M', u'մե'),
    (0xFB15, 'M', u'մի'),
    (0xFB16, 'M', u'վն'),
    ]

def _seg_44():
    return [
    (0xFB17, 'M', u'մխ'),
    (0xFB18, 'X'),
    (0xFB1D, 'M', u'יִ'),
    (0xFB1E, 'V'),
    (0xFB1F, 'M', u'ײַ'),
    (0xFB20, 'M', u'ע'),
    (0xFB21, 'M', u'א'),
    (0xFB22, 'M', u'ד'),
    (0xFB23, 'M', u'ה'),
    (0xFB24, 'M', u'כ'),
    (0xFB25, 'M', u'ל'),
    (0xFB26, 'M', u'ם'),
    (0xFB27, 'M', u'ר'),
    (0xFB28, 'M', u'ת'),
    (0xFB29, '3', u'+'),
    (0xFB2A, 'M', u'שׁ'),
    (0xFB2B, 'M', u'שׂ'),
    (0xFB2C, 'M', u'שּׁ'),
    (0xFB2D, 'M', u'שּׂ'),
    (0xFB2E, 'M', u'אַ'),
    (0xFB2F, 'M', u'אָ'),
    (0xFB30, 'M', u'אּ'),
    (0xFB31, 'M', u'בּ'),
    (0xFB32, 'M', u'גּ'),
    (0xFB33, 'M', u'דּ'),
    (0xFB34, 'M', u'הּ'),
    (0xFB35, 'M', u'וּ'),
    (0xFB36, 'M', u'זּ'),
    (0xFB37, 'X'),
    (0xFB38, 'M', u'טּ'),
    (0xFB39, 'M', u'יּ'),
    (0xFB3A, 'M', u'ךּ'),
    (0xFB3B, 'M', u'כּ'),
    (0xFB3C, 'M', u'לּ'),
    (0xFB3D, 'X'),
    (0xFB3E, 'M', u'מּ'),
    (0xFB3F, 'X'),
    (0xFB40, 'M', u'נּ'),
    (0xFB41, 'M', u'סּ'),
    (0xFB42, 'X'),
    (0xFB43, 'M', u'ףּ'),
    (0xFB44, 'M', u'פּ'),
    (0xFB45, 'X'),
    (0xFB46, 'M', u'צּ'),
    (0xFB47, 'M', u'קּ'),
    (0xFB48, 'M', u'רּ'),
    (0xFB49, 'M', u'שּ'),
    (0xFB4A, 'M', u'תּ'),
    (0xFB4B, 'M', u'וֹ'),
    (0xFB4C, 'M', u'בֿ'),
    (0xFB4D, 'M', u'כֿ'),
    (0xFB4E, 'M', u'פֿ'),
    (0xFB4F, 'M', u'אל'),
    (0xFB50, 'M', u'ٱ'),
    (0xFB52, 'M', u'ٻ'),
    (0xFB56, 'M', u'پ'),
    (0xFB5A, 'M', u'ڀ'),
    (0xFB5E, 'M', u'ٺ'),
    (0xFB62, 'M', u'ٿ'),
    (0xFB66, 'M', u'ٹ'),
    (0xFB6A, 'M', u'ڤ'),
    (0xFB6E, 'M', u'ڦ'),
    (0xFB72, 'M', u'ڄ'),
    (0xFB76, 'M', u'ڃ'),
    (0xFB7A, 'M', u'چ'),
    (0xFB7E, 'M', u'ڇ'),
    (0xFB82, 'M', u'ڍ'),
    (0xFB84, 'M', u'ڌ'),
    (0xFB86, 'M', u'ڎ'),
    (0xFB88, 'M', u'ڈ'),
    (0xFB8A, 'M', u'ژ'),
    (0xFB8C, 'M', u'ڑ'),
    (0xFB8E, 'M', u'ک'),
    (0xFB92, 'M', u'گ'),
    (0xFB96, 'M', u'ڳ'),
    (0xFB9A, 'M', u'ڱ'),
    (0xFB9E, 'M', u'ں'),
    (0xFBA0, 'M', u'ڻ'),
    (0xFBA4, 'M', u'ۀ'),
    (0xFBA6, 'M', u'ہ'),
    (0xFBAA, 'M', u'ھ'),
    (0xFBAE, 'M', u'ے'),
    (0xFBB0, 'M', u'ۓ'),
    (0xFBB2, 'V'),
    (0xFBC2, 'X'),
    (0xFBD3, 'M', u'ڭ'),
    (0xFBD7, 'M', u'ۇ'),
    (0xFBD9, 'M', u'ۆ'),
    (0xFBDB, 'M', u'ۈ'),
    (0xFBDD, 'M', u'ۇٴ'),
    (0xFBDE, 'M', u'ۋ'),
    (0xFBE0, 'M', u'ۅ'),
    (0xFBE2, 'M', u'ۉ'),
    (0xFBE4, 'M', u'ې'),
    (0xFBE8, 'M', u'ى'),
    (0xFBEA, 'M', u'ئا'),
    (0xFBEC, 'M', u'ئە'),
    (0xFBEE, 'M', u'ئو'),
    (0xFBF0, 'M', u'ئۇ'),
    (0xFBF2, 'M', u'ئۆ'),
    ]

def _seg_45():
    return [
    (0xFBF4, 'M', u'ئۈ'),
    (0xFBF6, 'M', u'ئې'),
    (0xFBF9, 'M', u'ئى'),
    (0xFBFC, 'M', u'ی'),
    (0xFC00, 'M', u'ئج'),
    (0xFC01, 'M', u'ئح'),
    (0xFC02, 'M', u'ئم'),
    (0xFC03, 'M', u'ئى'),
    (0xFC04, 'M', u'ئي'),
    (0xFC05, 'M', u'بج'),
    (0xFC06, 'M', u'بح'),
    (0xFC07, 'M', u'بخ'),
    (0xFC08, 'M', u'بم'),
    (0xFC09, 'M', u'بى'),
    (0xFC0A, 'M', u'بي'),
    (0xFC0B, 'M', u'تج'),
    (0xFC0C, 'M', u'تح'),
    (0xFC0D, 'M', u'تخ'),
    (0xFC0E, 'M', u'تم'),
    (0xFC0F, 'M', u'تى'),
    (0xFC10, 'M', u'تي'),
    (0xFC11, 'M', u'ثج'),
    (0xFC12, 'M', u'ثم'),
    (0xFC13, 'M', u'ثى'),
    (0xFC14, 'M', u'ثي'),
    (0xFC15, 'M', u'جح'),
    (0xFC16, 'M', u'جم'),
    (0xFC17, 'M', u'حج'),
    (0xFC18, 'M', u'حم'),
    (0xFC19, 'M', u'خج'),
    (0xFC1A, 'M', u'خح'),
    (0xFC1B, 'M', u'خم'),
    (0xFC1C, 'M', u'سج'),
    (0xFC1D, 'M', u'سح'),
    (0xFC1E, 'M', u'سخ'),
    (0xFC1F, 'M', u'سم'),
    (0xFC20, 'M', u'صح'),
    (0xFC21, 'M', u'صم'),
    (0xFC22, 'M', u'ضج'),
    (0xFC23, 'M', u'ضح'),
    (0xFC24, 'M', u'ضخ'),
    (0xFC25, 'M', u'ضم'),
    (0xFC26, 'M', u'طح'),
    (0xFC27, 'M', u'طم'),
    (0xFC28, 'M', u'ظم'),
    (0xFC29, 'M', u'عج'),
    (0xFC2A, 'M', u'عم'),
    (0xFC2B, 'M', u'غج'),
    (0xFC2C, 'M', u'غم'),
    (0xFC2D, 'M', u'فج'),
    (0xFC2E, 'M', u'فح'),
    (0xFC2F, 'M', u'فخ'),
    (0xFC30, 'M', u'فم'),
    (0xFC31, 'M', u'فى'),
    (0xFC32, 'M', u'في'),
    (0xFC33, 'M', u'قح'),
    (0xFC34, 'M', u'قم'),
    (0xFC35, 'M', u'قى'),
    (0xFC36, 'M', u'قي'),
    (0xFC37, 'M', u'كا'),
    (0xFC38, 'M', u'كج'),
    (0xFC39, 'M', u'كح'),
    (0xFC3A, 'M', u'كخ'),
    (0xFC3B, 'M', u'كل'),
    (0xFC3C, 'M', u'كم'),
    (0xFC3D, 'M', u'كى'),
    (0xFC3E, 'M', u'كي'),
    (0xFC3F, 'M', u'لج'),
    (0xFC40, 'M', u'لح'),
    (0xFC41, 'M', u'لخ'),
    (0xFC42, 'M', u'لم'),
    (0xFC43, 'M', u'لى'),
    (0xFC44, 'M', u'لي'),
    (0xFC45, 'M', u'مج'),
    (0xFC46, 'M', u'مح'),
    (0xFC47, 'M', u'مخ'),
    (0xFC48, 'M', u'مم'),
    (0xFC49, 'M', u'مى'),
    (0xFC4A, 'M', u'مي'),
    (0xFC4B, 'M', u'نج'),
    (0xFC4C, 'M', u'نح'),
    (0xFC4D, 'M', u'نخ'),
    (0xFC4E, 'M', u'نم'),
    (0xFC4F, 'M', u'نى'),
    (0xFC50, 'M', u'ني'),
    (0xFC51, 'M', u'هج'),
    (0xFC52, 'M', u'هم'),
    (0xFC53, 'M', u'هى'),
    (0xFC54, 'M', u'هي'),
    (0xFC55, 'M', u'يج'),
    (0xFC56, 'M', u'يح'),
    (0xFC57, 'M', u'يخ'),
    (0xFC58, 'M', u'يم'),
    (0xFC59, 'M', u'يى'),
    (0xFC5A, 'M', u'يي'),
    (0xFC5B, 'M', u'ذٰ'),
    (0xFC5C, 'M', u'رٰ'),
    (0xFC5D, 'M', u'ىٰ'),
    (0xFC5E, '3', u' ٌّ'),
    (0xFC5F, '3', u' ٍّ'),
    ]

def _seg_46():
    return [
    (0xFC60, '3', u' َّ'),
    (0xFC61, '3', u' ُّ'),
    (0xFC62, '3', u' ِّ'),
    (0xFC63, '3', u' ّٰ'),
    (0xFC64, 'M', u'ئر'),
    (0xFC65, 'M', u'ئز'),
    (0xFC66, 'M', u'ئم'),
    (0xFC67, 'M', u'ئن'),
    (0xFC68, 'M', u'ئى'),
    (0xFC69, 'M', u'ئي'),
    (0xFC6A, 'M', u'بر'),
    (0xFC6B, 'M', u'بز'),
    (0xFC6C, 'M', u'بم'),
    (0xFC6D, 'M', u'بن'),
    (0xFC6E, 'M', u'بى'),
    (0xFC6F, 'M', u'بي'),
    (0xFC70, 'M', u'تر'),
    (0xFC71, 'M', u'تز'),
    (0xFC72, 'M', u'تم'),
    (0xFC73, 'M', u'تن'),
    (0xFC74, 'M', u'تى'),
    (0xFC75, 'M', u'تي'),
    (0xFC76, 'M', u'ثر'),
    (0xFC77, 'M', u'ثز'),
    (0xFC78, 'M', u'ثم'),
    (0xFC79, 'M', u'ثن'),
    (0xFC7A, 'M', u'ثى'),
    (0xFC7B, 'M', u'ثي'),
    (0xFC7C, 'M', u'فى'),
    (0xFC7D, 'M', u'في'),
    (0xFC7E, 'M', u'قى'),
    (0xFC7F, 'M', u'قي'),
    (0xFC80, 'M', u'كا'),
    (0xFC81, 'M', u'كل'),
    (0xFC82, 'M', u'كم'),
    (0xFC83, 'M', u'كى'),
    (0xFC84, 'M', u'كي'),
    (0xFC85, 'M', u'لم'),
    (0xFC86, 'M', u'لى'),
    (0xFC87, 'M', u'لي'),
    (0xFC88, 'M', u'ما'),
    (0xFC89, 'M', u'مم'),
    (0xFC8A, 'M', u'نر'),
    (0xFC8B, 'M', u'نز'),
    (0xFC8C, 'M', u'نم'),
    (0xFC8D, 'M', u'نن'),
    (0xFC8E, 'M', u'نى'),
    (0xFC8F, 'M', u'ني'),
    (0xFC90, 'M', u'ىٰ'),
    (0xFC91, 'M', u'ير'),
    (0xFC92, 'M', u'يز'),
    (0xFC93, 'M', u'يم'),
    (0xFC94, 'M', u'ين'),
    (0xFC95, 'M', u'يى'),
    (0xFC96, 'M', u'يي'),
    (0xFC97, 'M', u'ئج'),
    (0xFC98, 'M', u'ئح'),
    (0xFC99, 'M', u'ئخ'),
    (0xFC9A, 'M', u'ئم'),
    (0xFC9B, 'M', u'ئه'),
    (0xFC9C, 'M', u'بج'),
    (0xFC9D, 'M', u'بح'),
    (0xFC9E, 'M', u'بخ'),
    (0xFC9F, 'M', u'بم'),
    (0xFCA0, 'M', u'به'),
    (0xFCA1, 'M', u'تج'),
    (0xFCA2, 'M', u'تح'),
    (0xFCA3, 'M', u'تخ'),
    (0xFCA4, 'M', u'تم'),
    (0xFCA5, 'M', u'ته'),
    (0xFCA6, 'M', u'ثم'),
    (0xFCA7, 'M', u'جح'),
    (0xFCA8, 'M', u'جم'),
    (0xFCA9, 'M', u'حج'),
    (0xFCAA, 'M', u'حم'),
    (0xFCAB, 'M', u'خج'),
    (0xFCAC, 'M', u'خم'),
    (0xFCAD, 'M', u'سج'),
    (0xFCAE, 'M', u'سح'),
    (0xFCAF, 'M', u'سخ'),
    (0xFCB0, 'M', u'سم'),
    (0xFCB1, 'M', u'صح'),
    (0xFCB2, 'M', u'صخ'),
    (0xFCB3, 'M', u'صم'),
    (0xFCB4, 'M', u'ضج'),
    (0xFCB5, 'M', u'ضح'),
    (0xFCB6, 'M', u'ضخ'),
    (0xFCB7, 'M', u'ضم'),
    (0xFCB8, 'M', u'طح'),
    (0xFCB9, 'M', u'ظم'),
    (0xFCBA, 'M', u'عج'),
    (0xFCBB, 'M', u'عم'),
    (0xFCBC, 'M', u'غج'),
    (0xFCBD, 'M', u'غم'),
    (0xFCBE, 'M', u'فج'),
    (0xFCBF, 'M', u'فح'),
    (0xFCC0, 'M', u'فخ'),
    (0xFCC1, 'M', u'فم'),
    (0xFCC2, 'M', u'قح'),
    (0xFCC3, 'M', u'قم'),
    ]

def _seg_47():
    return [
    (0xFCC4, 'M', u'كج'),
    (0xFCC5, 'M', u'كح'),
    (0xFCC6, 'M', u'كخ'),
    (0xFCC7, 'M', u'كل'),
    (0xFCC8, 'M', u'كم'),
    (0xFCC9, 'M', u'لج'),
    (0xFCCA, 'M', u'لح'),
    (0xFCCB, 'M', u'لخ'),
    (0xFCCC, 'M', u'لم'),
    (0xFCCD, 'M', u'له'),
    (0xFCCE, 'M', u'مج'),
    (0xFCCF, 'M', u'مح'),
    (0xFCD0, 'M', u'مخ'),
    (0xFCD1, 'M', u'مم'),
    (0xFCD2, 'M', u'نج'),
    (0xFCD3, 'M', u'نح'),
    (0xFCD4, 'M', u'نخ'),
    (0xFCD5, 'M', u'نم'),
    (0xFCD6, 'M', u'نه'),
    (0xFCD7, 'M', u'هج'),
    (0xFCD8, 'M', u'هم'),
    (0xFCD9, 'M', u'هٰ'),
    (0xFCDA, 'M', u'يج'),
    (0xFCDB, 'M', u'يح'),
    (0xFCDC, 'M', u'يخ'),
    (0xFCDD, 'M', u'يم'),
    (0xFCDE, 'M', u'يه'),
    (0xFCDF, 'M', u'ئم'),
    (0xFCE0, 'M', u'ئه'),
    (0xFCE1, 'M', u'بم'),
    (0xFCE2, 'M', u'به'),
    (0xFCE3, 'M', u'تم'),
    (0xFCE4, 'M', u'ته'),
    (0xFCE5, 'M', u'ثم'),
    (0xFCE6, 'M', u'ثه'),
    (0xFCE7, 'M', u'سم'),
    (0xFCE8, 'M', u'سه'),
    (0xFCE9, 'M', u'شم'),
    (0xFCEA, 'M', u'شه'),
    (0xFCEB, 'M', u'كل'),
    (0xFCEC, 'M', u'كم'),
    (0xFCED, 'M', u'لم'),
    (0xFCEE, 'M', u'نم'),
    (0xFCEF, 'M', u'نه'),
    (0xFCF0, 'M', u'يم'),
    (0xFCF1, 'M', u'يه'),
    (0xFCF2, 'M', u'ـَّ'),
    (0xFCF3, 'M', u'ـُّ'),
    (0xFCF4, 'M', u'ـِّ'),
    (0xFCF5, 'M', u'طى'),
    (0xFCF6, 'M', u'طي'),
    (0xFCF7, 'M', u'عى'),
    (0xFCF8, 'M', u'عي'),
    (0xFCF9, 'M', u'غى'),
    (0xFCFA, 'M', u'غي'),
    (0xFCFB, 'M', u'سى'),
    (0xFCFC, 'M', u'سي'),
    (0xFCFD, 'M', u'شى'),
    (0xFCFE, 'M', u'شي'),
    (0xFCFF, 'M', u'حى'),
    (0xFD00, 'M', u'حي'),
    (0xFD01, 'M', u'جى'),
    (0xFD02, 'M', u'جي'),
    (0xFD03, 'M', u'خى'),
    (0xFD04, 'M', u'خي'),
    (0xFD05, 'M', u'صى'),
    (0xFD06, 'M', u'صي'),
    (0xFD07, 'M', u'ضى'),
    (0xFD08, 'M', u'ضي'),
    (0xFD09, 'M', u'شج'),
    (0xFD0A, 'M', u'شح'),
    (0xFD0B, 'M', u'شخ'),
    (0xFD0C, 'M', u'شم'),
    (0xFD0D, 'M', u'شر'),
    (0xFD0E, 'M', u'سر'),
    (0xFD0F, 'M', u'صر'),
    (0xFD10, 'M', u'ضر'),
    (0xFD11, 'M', u'طى'),
    (0xFD12, 'M', u'طي'),
    (0xFD13, 'M', u'عى'),
    (0xFD14, 'M', u'عي'),
    (0xFD15, 'M', u'غى'),
    (0xFD16, 'M', u'غي'),
    (0xFD17, 'M', u'سى'),
    (0xFD18, 'M', u'سي'),
    (0xFD19, 'M', u'شى'),
    (0xFD1A, 'M', u'شي'),
    (0xFD1B, 'M', u'حى'),
    (0xFD1C, 'M', u'حي'),
    (0xFD1D, 'M', u'جى'),
    (0xFD1E, 'M', u'جي'),
    (0xFD1F, 'M', u'خى'),
    (0xFD20, 'M', u'خي'),
    (0xFD21, 'M', u'صى'),
    (0xFD22, 'M', u'صي'),
    (0xFD23, 'M', u'ضى'),
    (0xFD24, 'M', u'ضي'),
    (0xFD25, 'M', u'شج'),
    (0xFD26, 'M', u'شح'),
    (0xFD27, 'M', u'شخ'),
    ]

def _seg_48():
    return [
    (0xFD28, 'M', u'شم'),
    (0xFD29, 'M', u'شر'),
    (0xFD2A, 'M', u'سر'),
    (0xFD2B, 'M', u'صر'),
    (0xFD2C, 'M', u'ضر'),
    (0xFD2D, 'M', u'شج'),
    (0xFD2E, 'M', u'شح'),
    (0xFD2F, 'M', u'شخ'),
    (0xFD30, 'M', u'شم'),
    (0xFD31, 'M', u'سه'),
    (0xFD32, 'M', u'شه'),
    (0xFD33, 'M', u'طم'),
    (0xFD34, 'M', u'سج'),
    (0xFD35, 'M', u'سح'),
    (0xFD36, 'M', u'سخ'),
    (0xFD37, 'M', u'شج'),
    (0xFD38, 'M', u'شح'),
    (0xFD39, 'M', u'شخ'),
    (0xFD3A, 'M', u'طم'),
    (0xFD3B, 'M', u'ظم'),
    (0xFD3C, 'M', u'اً'),
    (0xFD3E, 'V'),
    (0xFD40, 'X'),
    (0xFD50, 'M', u'تجم'),
    (0xFD51, 'M', u'تحج'),
    (0xFD53, 'M', u'تحم'),
    (0xFD54, 'M', u'تخم'),
    (0xFD55, 'M', u'تمج'),
    (0xFD56, 'M', u'تمح'),
    (0xFD57, 'M', u'تمخ'),
    (0xFD58, 'M', u'جمح'),
    (0xFD5A, 'M', u'حمي'),
    (0xFD5B, 'M', u'حمى'),
    (0xFD5C, 'M', u'سحج'),
    (0xFD5D, 'M', u'سجح'),
    (0xFD5E, 'M', u'سجى'),
    (0xFD5F, 'M', u'سمح'),
    (0xFD61, 'M', u'سمج'),
    (0xFD62, 'M', u'سمم'),
    (0xFD64, 'M', u'صحح'),
    (0xFD66, 'M', u'صمم'),
    (0xFD67, 'M', u'شحم'),
    (0xFD69, 'M', u'شجي'),
    (0xFD6A, 'M', u'شمخ'),
    (0xFD6C, 'M', u'شمم'),
    (0xFD6E, 'M', u'ضحى'),
    (0xFD6F, 'M', u'ضخم'),
    (0xFD71, 'M', u'طمح'),
    (0xFD73, 'M', u'طمم'),
    (0xFD74, 'M', u'طمي'),
    (0xFD75, 'M', u'عجم'),
    (0xFD76, 'M', u'عمم'),
    (0xFD78, 'M', u'عمى'),
    (0xFD79, 'M', u'غمم'),
    (0xFD7A, 'M', u'غمي'),
    (0xFD7B, 'M', u'غمى'),
    (0xFD7C, 'M', u'فخم'),
    (0xFD7E, 'M', u'قمح'),
    (0xFD7F, 'M', u'قمم'),
    (0xFD80, 'M', u'لحم'),
    (0xFD81, 'M', u'لحي'),
    (0xFD82, 'M', u'لحى'),
    (0xFD83, 'M', u'لجج'),
    (0xFD85, 'M', u'لخم'),
    (0xFD87, 'M', u'لمح'),
    (0xFD89, 'M', u'محج'),
    (0xFD8A, 'M', u'محم'),
    (0xFD8B, 'M', u'محي'),
    (0xFD8C, 'M', u'مجح'),
    (0xFD8D, 'M', u'مجم'),
    (0xFD8E, 'M', u'مخج'),
    (0xFD8F, 'M', u'مخم'),
    (0xFD90, 'X'),
    (0xFD92, 'M', u'مجخ'),
    (0xFD93, 'M', u'همج'),
    (0xFD94, 'M', u'همم'),
    (0xFD95, 'M', u'نحم'),
    (0xFD96, 'M', u'نحى'),
    (0xFD97, 'M', u'نجم'),
    (0xFD99, 'M', u'نجى'),
    (0xFD9A, 'M', u'نمي'),
    (0xFD9B, 'M', u'نمى'),
    (0xFD9C, 'M', u'يمم'),
    (0xFD9E, 'M', u'بخي'),
    (0xFD9F, 'M', u'تجي'),
    (0xFDA0, 'M', u'تجى'),
    (0xFDA1, 'M', u'تخي'),
    (0xFDA2, 'M', u'تخى'),
    (0xFDA3, 'M', u'تمي'),
    (0xFDA4, 'M', u'تمى'),
    (0xFDA5, 'M', u'جمي'),
    (0xFDA6, 'M', u'جحى'),
    (0xFDA7, 'M', u'جمى'),
    (0xFDA8, 'M', u'سخى'),
    (0xFDA9, 'M', u'صحي'),
    (0xFDAA, 'M', u'شحي'),
    (0xFDAB, 'M', u'ضحي'),
    (0xFDAC, 'M', u'لجي'),
    (0xFDAD, 'M', u'لمي'),
    (0xFDAE, 'M', u'يحي'),
    ]

def _seg_49():
    return [
    (0xFDAF, 'M', u'يجي'),
    (0xFDB0, 'M', u'يمي'),
    (0xFDB1, 'M', u'ممي'),
    (0xFDB2, 'M', u'قمي'),
    (0xFDB3, 'M', u'نحي'),
    (0xFDB4, 'M', u'قمح'),
    (0xFDB5, 'M', u'لحم'),
    (0xFDB6, 'M', u'عمي'),
    (0xFDB7, 'M', u'كمي'),
    (0xFDB8, 'M', u'نجح'),
    (0xFDB9, 'M', u'مخي'),
    (0xFDBA, 'M', u'لجم'),
    (0xFDBB, 'M', u'كمم'),
    (0xFDBC, 'M', u'لجم'),
    (0xFDBD, 'M', u'نجح'),
    (0xFDBE, 'M', u'جحي'),
    (0xFDBF, 'M', u'حجي'),
    (0xFDC0, 'M', u'مجي'),
    (0xFDC1, 'M', u'فمي'),
    (0xFDC2, 'M', u'بحي'),
    (0xFDC3, 'M', u'كمم'),
    (0xFDC4, 'M', u'عجم'),
    (0xFDC5, 'M', u'صمم'),
    (0xFDC6, 'M', u'سخي'),
    (0xFDC7, 'M', u'نجي'),
    (0xFDC8, 'X'),
    (0xFDF0, 'M', u'صلے'),
    (0xFDF1, 'M', u'قلے'),
    (0xFDF2, 'M', u'الله'),
    (0xFDF3, 'M', u'اكبر'),
    (0xFDF4, 'M', u'محمد'),
    (0xFDF5, 'M', u'صلعم'),
    (0xFDF6, 'M', u'رسول'),
    (0xFDF7, 'M', u'عليه'),
    (0xFDF8, 'M', u'وسلم'),
    (0xFDF9, 'M', u'صلى'),
    (0xFDFA, '3', u'صلى الله عليه وسلم'),
    (0xFDFB, '3', u'جل جلاله'),
    (0xFDFC, 'M', u'ریال'),
    (0xFDFD, 'V'),
    (0xFDFE, 'X'),
    (0xFE00, 'I'),
    (0xFE10, '3', u','),
    (0xFE11, 'M', u'、'),
    (0xFE12, 'X'),
    (0xFE13, '3', u':'),
    (0xFE14, '3', u';'),
    (0xFE15, '3', u'!'),
    (0xFE16, '3', u'?'),
    (0xFE17, 'M', u'〖'),
    (0xFE18, 'M', u'〗'),
    (0xFE19, 'X'),
    (0xFE20, 'V'),
    (0xFE30, 'X'),
    (0xFE31, 'M', u'—'),
    (0xFE32, 'M', u'–'),
    (0xFE33, '3', u'_'),
    (0xFE35, '3', u'('),
    (0xFE36, '3', u')'),
    (0xFE37, '3', u'{'),
    (0xFE38, '3', u'}'),
    (0xFE39, 'M', u'〔'),
    (0xFE3A, 'M', u'〕'),
    (0xFE3B, 'M', u'【'),
    (0xFE3C, 'M', u'】'),
    (0xFE3D, 'M', u'《'),
    (0xFE3E, 'M', u'》'),
    (0xFE3F, 'M', u'〈'),
    (0xFE40, 'M', u'〉'),
    (0xFE41, 'M', u'「'),
    (0xFE42, 'M', u'」'),
    (0xFE43, 'M', u'『'),
    (0xFE44, 'M', u'』'),
    (0xFE45, 'V'),
    (0xFE47, '3', u'['),
    (0xFE48, '3', u']'),
    (0xFE49, '3', u' ̅'),
    (0xFE4D, '3', u'_'),
    (0xFE50, '3', u','),
    (0xFE51, 'M', u'、'),
    (0xFE52, 'X'),
    (0xFE54, '3', u';'),
    (0xFE55, '3', u':'),
    (0xFE56, '3', u'?'),
    (0xFE57, '3', u'!'),
    (0xFE58, 'M', u'—'),
    (0xFE59, '3', u'('),
    (0xFE5A, '3', u')'),
    (0xFE5B, '3', u'{'),
    (0xFE5C, '3', u'}'),
    (0xFE5D, 'M', u'〔'),
    (0xFE5E, 'M', u'〕'),
    (0xFE5F, '3', u'#'),
    (0xFE60, '3', u'&'),
    (0xFE61, '3', u'*'),
    (0xFE62, '3', u'+'),
    (0xFE63, 'M', u'-'),
    (0xFE64, '3', u'<'),
    (0xFE65, '3', u'>'),
    (0xFE66, '3', u'='),
    ]

def _seg_50():
    return [
    (0xFE67, 'X'),
    (0xFE68, '3', u'\\'),
    (0xFE69, '3', u'$'),
    (0xFE6A, '3', u'%'),
    (0xFE6B, '3', u'@'),
    (0xFE6C, 'X'),
    (0xFE70, '3', u' ً'),
    (0xFE71, 'M', u'ـً'),
    (0xFE72, '3', u' ٌ'),
    (0xFE73, 'V'),
    (0xFE74, '3', u' ٍ'),
    (0xFE75, 'X'),
    (0xFE76, '3', u' َ'),
    (0xFE77, 'M', u'ـَ'),
    (0xFE78, '3', u' ُ'),
    (0xFE79, 'M', u'ـُ'),
    (0xFE7A, '3', u' ِ'),
    (0xFE7B, 'M', u'ـِ'),
    (0xFE7C, '3', u' ّ'),
    (0xFE7D, 'M', u'ـّ'),
    (0xFE7E, '3', u' ْ'),
    (0xFE7F, 'M', u'ـْ'),
    (0xFE80, 'M', u'ء'),
    (0xFE81, 'M', u'آ'),
    (0xFE83, 'M', u'أ'),
    (0xFE85, 'M', u'ؤ'),
    (0xFE87, 'M', u'إ'),
    (0xFE89, 'M', u'ئ'),
    (0xFE8D, 'M', u'ا'),
    (0xFE8F, 'M', u'ب'),
    (0xFE93, 'M', u'ة'),
    (0xFE95, 'M', u'ت'),
    (0xFE99, 'M', u'ث'),
    (0xFE9D, 'M', u'ج'),
    (0xFEA1, 'M', u'ح'),
    (0xFEA5, 'M', u'خ'),
    (0xFEA9, 'M', u'د'),
    (0xFEAB, 'M', u'ذ'),
    (0xFEAD, 'M', u'ر'),
    (0xFEAF, 'M', u'ز'),
    (0xFEB1, 'M', u'س'),
    (0xFEB5, 'M', u'ش'),
    (0xFEB9, 'M', u'ص'),
    (0xFEBD, 'M', u'ض'),
    (0xFEC1, 'M', u'ط'),
    (0xFEC5, 'M', u'ظ'),
    (0xFEC9, 'M', u'ع'),
    (0xFECD, 'M', u'غ'),
    (0xFED1, 'M', u'ف'),
    (0xFED5, 'M', u'ق'),
    (0xFED9, 'M', u'ك'),
    (0xFEDD, 'M', u'ل'),
    (0xFEE1, 'M', u'م'),
    (0xFEE5, 'M', u'ن'),
    (0xFEE9, 'M', u'ه'),
    (0xFEED, 'M', u'و'),
    (0xFEEF, 'M', u'ى'),
    (0xFEF1, 'M', u'ي'),
    (0xFEF5, 'M', u'لآ'),
    (0xFEF7, 'M', u'لأ'),
    (0xFEF9, 'M', u'لإ'),
    (0xFEFB, 'M', u'لا'),
    (0xFEFD, 'X'),
    (0xFEFF, 'I'),
    (0xFF00, 'X'),
    (0xFF01, '3', u'!'),
    (0xFF02, '3', u'"'),
    (0xFF03, '3', u'#'),
    (0xFF04, '3', u'$'),
    (0xFF05, '3', u'%'),
    (0xFF06, '3', u'&'),
    (0xFF07, '3', u'\''),
    (0xFF08, '3', u'('),
    (0xFF09, '3', u')'),
    (0xFF0A, '3', u'*'),
    (0xFF0B, '3', u'+'),
    (0xFF0C, '3', u','),
    (0xFF0D, 'M', u'-'),
    (0xFF0E, 'M', u'.'),
    (0xFF0F, '3', u'/'),
    (0xFF10, 'M', u'0'),
    (0xFF11, 'M', u'1'),
    (0xFF12, 'M', u'2'),
    (0xFF13, 'M', u'3'),
    (0xFF14, 'M', u'4'),
    (0xFF15, 'M', u'5'),
    (0xFF16, 'M', u'6'),
    (0xFF17, 'M', u'7'),
    (0xFF18, 'M', u'8'),
    (0xFF19, 'M', u'9'),
    (0xFF1A, '3', u':'),
    (0xFF1B, '3', u';'),
    (0xFF1C, '3', u'<'),
    (0xFF1D, '3', u'='),
    (0xFF1E, '3', u'>'),
    (0xFF1F, '3', u'?'),
    (0xFF20, '3', u'@'),
    (0xFF21, 'M', u'a'),
    (0xFF22, 'M', u'b'),
    (0xFF23, 'M', u'c'),
    ]

def _seg_51():
    return [
    (0xFF24, 'M', u'd'),
    (0xFF25, 'M', u'e'),
    (0xFF26, 'M', u'f'),
    (0xFF27, 'M', u'g'),
    (0xFF28, 'M', u'h'),
    (0xFF29, 'M', u'i'),
    (0xFF2A, 'M', u'j'),
    (0xFF2B, 'M', u'k'),
    (0xFF2C, 'M', u'l'),
    (0xFF2D, 'M', u'm'),
    (0xFF2E, 'M', u'n'),
    (0xFF2F, 'M', u'o'),
    (0xFF30, 'M', u'p'),
    (0xFF31, 'M', u'q'),
    (0xFF32, 'M', u'r'),
    (0xFF33, 'M', u's'),
    (0xFF34, 'M', u't'),
    (0xFF35, 'M', u'u'),
    (0xFF36, 'M', u'v'),
    (0xFF37, 'M', u'w'),
    (0xFF38, 'M', u'x'),
    (0xFF39, 'M', u'y'),
    (0xFF3A, 'M', u'z'),
    (0xFF3B, '3', u'['),
    (0xFF3C, '3', u'\\'),
    (0xFF3D, '3', u']'),
    (0xFF3E, '3', u'^'),
    (0xFF3F, '3', u'_'),
    (0xFF40, '3', u'`'),
    (0xFF41, 'M', u'a'),
    (0xFF42, 'M', u'b'),
    (0xFF43, 'M', u'c'),
    (0xFF44, 'M', u'd'),
    (0xFF45, 'M', u'e'),
    (0xFF46, 'M', u'f'),
    (0xFF47, 'M', u'g'),
    (0xFF48, 'M', u'h'),
    (0xFF49, 'M', u'i'),
    (0xFF4A, 'M', u'j'),
    (0xFF4B, 'M', u'k'),
    (0xFF4C, 'M', u'l'),
    (0xFF4D, 'M', u'm'),
    (0xFF4E, 'M', u'n'),
    (0xFF4F, 'M', u'o'),
    (0xFF50, 'M', u'p'),
    (0xFF51, 'M', u'q'),
    (0xFF52, 'M', u'r'),
    (0xFF53, 'M', u's'),
    (0xFF54, 'M', u't'),
    (0xFF55, 'M', u'u'),
    (0xFF56, 'M', u'v'),
    (0xFF57, 'M', u'w'),
    (0xFF58, 'M', u'x'),
    (0xFF59, 'M', u'y'),
    (0xFF5A, 'M', u'z'),
    (0xFF5B, '3', u'{'),
    (0xFF5C, '3', u'|'),
    (0xFF5D, '3', u'}'),
    (0xFF5E, '3', u'~'),
    (0xFF5F, 'M', u'⦅'),
    (0xFF60, 'M', u'⦆'),
    (0xFF61, 'M', u'.'),
    (0xFF62, 'M', u'「'),
    (0xFF63, 'M', u'」'),
    (0xFF64, 'M', u'、'),
    (0xFF65, 'M', u'・'),
    (0xFF66, 'M', u'ヲ'),
    (0xFF67, 'M', u'ァ'),
    (0xFF68, 'M', u'ィ'),
    (0xFF69, 'M', u'ゥ'),
    (0xFF6A, 'M', u'ェ'),
    (0xFF6B, 'M', u'ォ'),
    (0xFF6C, 'M', u'ャ'),
    (0xFF6D, 'M', u'ュ'),
    (0xFF6E, 'M', u'ョ'),
    (0xFF6F, 'M', u'ッ'),
    (0xFF70, 'M', u'ー'),
    (0xFF71, 'M', u'ア'),
    (0xFF72, 'M', u'イ'),
    (0xFF73, 'M', u'ウ'),
    (0xFF74, 'M', u'エ'),
    (0xFF75, 'M', u'オ'),
    (0xFF76, 'M', u'カ'),
    (0xFF77, 'M', u'キ'),
    (0xFF78, 'M', u'ク'),
    (0xFF79, 'M', u'ケ'),
    (0xFF7A, 'M', u'コ'),
    (0xFF7B, 'M', u'サ'),
    (0xFF7C, 'M', u'シ'),
    (0xFF7D, 'M', u'ス'),
    (0xFF7E, 'M', u'セ'),
    (0xFF7F, 'M', u'ソ'),
    (0xFF80, 'M', u'タ'),
    (0xFF81, 'M', u'チ'),
    (0xFF82, 'M', u'ツ'),
    (0xFF83, 'M', u'テ'),
    (0xFF84, 'M', u'ト'),
    (0xFF85, 'M', u'ナ'),
    (0xFF86, 'M', u'ニ'),
    (0xFF87, 'M', u'ヌ'),
    ]

def _seg_52():
    return [
    (0xFF88, 'M', u'ネ'),
    (0xFF89, 'M', u'ノ'),
    (0xFF8A, 'M', u'ハ'),
    (0xFF8B, 'M', u'ヒ'),
    (0xFF8C, 'M', u'フ'),
    (0xFF8D, 'M', u'ヘ'),
    (0xFF8E, 'M', u'ホ'),
    (0xFF8F, 'M', u'マ'),
    (0xFF90, 'M', u'ミ'),
    (0xFF91, 'M', u'ム'),
    (0xFF92, 'M', u'メ'),
    (0xFF93, 'M', u'モ'),
    (0xFF94, 'M', u'ヤ'),
    (0xFF95, 'M', u'ユ'),
    (0xFF96, 'M', u'ヨ'),
    (0xFF97, 'M', u'ラ'),
    (0xFF98, 'M', u'リ'),
    (0xFF99, 'M', u'ル'),
    (0xFF9A, 'M', u'レ'),
    (0xFF9B, 'M', u'ロ'),
    (0xFF9C, 'M', u'ワ'),
    (0xFF9D, 'M', u'ン'),
    (0xFF9E, 'M', u'゙'),
    (0xFF9F, 'M', u'゚'),
    (0xFFA0, 'X'),
    (0xFFA1, 'M', u'ᄀ'),
    (0xFFA2, 'M', u'ᄁ'),
    (0xFFA3, 'M', u'ᆪ'),
    (0xFFA4, 'M', u'ᄂ'),
    (0xFFA5, 'M', u'ᆬ'),
    (0xFFA6, 'M', u'ᆭ'),
    (0xFFA7, 'M', u'ᄃ'),
    (0xFFA8, 'M', u'ᄄ'),
    (0xFFA9, 'M', u'ᄅ'),
    (0xFFAA, 'M', u'ᆰ'),
    (0xFFAB, 'M', u'ᆱ'),
    (0xFFAC, 'M', u'ᆲ'),
    (0xFFAD, 'M', u'ᆳ'),
    (0xFFAE, 'M', u'ᆴ'),
    (0xFFAF, 'M', u'ᆵ'),
    (0xFFB0, 'M', u'ᄚ'),
    (0xFFB1, 'M', u'ᄆ'),
    (0xFFB2, 'M', u'ᄇ'),
    (0xFFB3, 'M', u'ᄈ'),
    (0xFFB4, 'M', u'ᄡ'),
    (0xFFB5, 'M', u'ᄉ'),
    (0xFFB6, 'M', u'ᄊ'),
    (0xFFB7, 'M', u'ᄋ'),
    (0xFFB8, 'M', u'ᄌ'),
    (0xFFB9, 'M', u'ᄍ'),
    (0xFFBA, 'M', u'ᄎ'),
    (0xFFBB, 'M', u'ᄏ'),
    (0xFFBC, 'M', u'ᄐ'),
    (0xFFBD, 'M', u'ᄑ'),
    (0xFFBE, 'M', u'ᄒ'),
    (0xFFBF, 'X'),
    (0xFFC2, 'M', u'ᅡ'),
    (0xFFC3, 'M', u'ᅢ'),
    (0xFFC4, 'M', u'ᅣ'),
    (0xFFC5, 'M', u'ᅤ'),
    (0xFFC6, 'M', u'ᅥ'),
    (0xFFC7, 'M', u'ᅦ'),
    (0xFFC8, 'X'),
    (0xFFCA, 'M', u'ᅧ'),
    (0xFFCB, 'M', u'ᅨ'),
    (0xFFCC, 'M', u'ᅩ'),
    (0xFFCD, 'M', u'ᅪ'),
    (0xFFCE, 'M', u'ᅫ'),
    (0xFFCF, 'M', u'ᅬ'),
    (0xFFD0, 'X'),
    (0xFFD2, 'M', u'ᅭ'),
    (0xFFD3, 'M', u'ᅮ'),
    (0xFFD4, 'M', u'ᅯ'),
    (0xFFD5, 'M', u'ᅰ'),
    (0xFFD6, 'M', u'ᅱ'),
    (0xFFD7, 'M', u'ᅲ'),
    (0xFFD8, 'X'),
    (0xFFDA, 'M', u'ᅳ'),
    (0xFFDB, 'M', u'ᅴ'),
    (0xFFDC, 'M', u'ᅵ'),
    (0xFFDD, 'X'),
    (0xFFE0, 'M', u'¢'),
    (0xFFE1, 'M', u'£'),
    (0xFFE2, 'M', u'¬'),
    (0xFFE3, '3', u' ̄'),
    (0xFFE4, 'M', u'¦'),
    (0xFFE5, 'M', u'¥'),
    (0xFFE6, 'M', u'₩'),
    (0xFFE7, 'X'),
    (0xFFE8, 'M', u'│'),
    (0xFFE9, 'M', u'←'),
    (0xFFEA, 'M', u'↑'),
    (0xFFEB, 'M', u'→'),
    (0xFFEC, 'M', u'↓'),
    (0xFFED, 'M', u'■'),
    (0xFFEE, 'M', u'○'),
    (0xFFEF, 'X'),
    (0x10000, 'V'),
    (0x1000C, 'X'),
    (0x1000D, 'V'),
    ]

def _seg_53():
    return [
    (0x10027, 'X'),
    (0x10028, 'V'),
    (0x1003B, 'X'),
    (0x1003C, 'V'),
    (0x1003E, 'X'),
    (0x1003F, 'V'),
    (0x1004E, 'X'),
    (0x10050, 'V'),
    (0x1005E, 'X'),
    (0x10080, 'V'),
    (0x100FB, 'X'),
    (0x10100, 'V'),
    (0x10103, 'X'),
    (0x10107, 'V'),
    (0x10134, 'X'),
    (0x10137, 'V'),
    (0x1018F, 'X'),
    (0x10190, 'V'),
    (0x1019D, 'X'),
    (0x101A0, 'V'),
    (0x101A1, 'X'),
    (0x101D0, 'V'),
    (0x101FE, 'X'),
    (0x10280, 'V'),
    (0x1029D, 'X'),
    (0x102A0, 'V'),
    (0x102D1, 'X'),
    (0x102E0, 'V'),
    (0x102FC, 'X'),
    (0x10300, 'V'),
    (0x10324, 'X'),
    (0x1032D, 'V'),
    (0x1034B, 'X'),
    (0x10350, 'V'),
    (0x1037B, 'X'),
    (0x10380, 'V'),
    (0x1039E, 'X'),
    (0x1039F, 'V'),
    (0x103C4, 'X'),
    (0x103C8, 'V'),
    (0x103D6, 'X'),
    (0x10400, 'M', u'𐐨'),
    (0x10401, 'M', u'𐐩'),
    (0x10402, 'M', u'𐐪'),
    (0x10403, 'M', u'𐐫'),
    (0x10404, 'M', u'𐐬'),
    (0x10405, 'M', u'𐐭'),
    (0x10406, 'M', u'𐐮'),
    (0x10407, 'M', u'𐐯'),
    (0x10408, 'M', u'𐐰'),
    (0x10409, 'M', u'𐐱'),
    (0x1040A, 'M', u'𐐲'),
    (0x1040B, 'M', u'𐐳'),
    (0x1040C, 'M', u'𐐴'),
    (0x1040D, 'M', u'𐐵'),
    (0x1040E, 'M', u'𐐶'),
    (0x1040F, 'M', u'𐐷'),
    (0x10410, 'M', u'𐐸'),
    (0x10411, 'M', u'𐐹'),
    (0x10412, 'M', u'𐐺'),
    (0x10413, 'M', u'𐐻'),
    (0x10414, 'M', u'𐐼'),
    (0x10415, 'M', u'𐐽'),
    (0x10416, 'M', u'𐐾'),
    (0x10417, 'M', u'𐐿'),
    (0x10418, 'M', u'𐑀'),
    (0x10419, 'M', u'𐑁'),
    (0x1041A, 'M', u'𐑂'),
    (0x1041B, 'M', u'𐑃'),
    (0x1041C, 'M', u'𐑄'),
    (0x1041D, 'M', u'𐑅'),
    (0x1041E, 'M', u'𐑆'),
    (0x1041F, 'M', u'𐑇'),
    (0x10420, 'M', u'𐑈'),
    (0x10421, 'M', u'𐑉'),
    (0x10422, 'M', u'𐑊'),
    (0x10423, 'M', u'𐑋'),
    (0x10424, 'M', u'𐑌'),
    (0x10425, 'M', u'𐑍'),
    (0x10426, 'M', u'𐑎'),
    (0x10427, 'M', u'𐑏'),
    (0x10428, 'V'),
    (0x1049E, 'X'),
    (0x104A0, 'V'),
    (0x104AA, 'X'),
    (0x104B0, 'M', u'𐓘'),
    (0x104B1, 'M', u'𐓙'),
    (0x104B2, 'M', u'𐓚'),
    (0x104B3, 'M', u'𐓛'),
    (0x104B4, 'M', u'𐓜'),
    (0x104B5, 'M', u'𐓝'),
    (0x104B6, 'M', u'𐓞'),
    (0x104B7, 'M', u'𐓟'),
    (0x104B8, 'M', u'𐓠'),
    (0x104B9, 'M', u'𐓡'),
    (0x104BA, 'M', u'𐓢'),
    (0x104BB, 'M', u'𐓣'),
    (0x104BC, 'M', u'𐓤'),
    (0x104BD, 'M', u'𐓥'),
    (0x104BE, 'M', u'𐓦'),
    ]

def _seg_54():
    return [
    (0x104BF, 'M', u'𐓧'),
    (0x104C0, 'M', u'𐓨'),
    (0x104C1, 'M', u'𐓩'),
    (0x104C2, 'M', u'𐓪'),
    (0x104C3, 'M', u'𐓫'),
    (0x104C4, 'M', u'𐓬'),
    (0x104C5, 'M', u'𐓭'),
    (0x104C6, 'M', u'𐓮'),
    (0x104C7, 'M', u'𐓯'),
    (0x104C8, 'M', u'𐓰'),
    (0x104C9, 'M', u'𐓱'),
    (0x104CA, 'M', u'𐓲'),
    (0x104CB, 'M', u'𐓳'),
    (0x104CC, 'M', u'𐓴'),
    (0x104CD, 'M', u'𐓵'),
    (0x104CE, 'M', u'𐓶'),
    (0x104CF, 'M', u'𐓷'),
    (0x104D0, 'M', u'𐓸'),
    (0x104D1, 'M', u'𐓹'),
    (0x104D2, 'M', u'𐓺'),
    (0x104D3, 'M', u'𐓻'),
    (0x104D4, 'X'),
    (0x104D8, 'V'),
    (0x104FC, 'X'),
    (0x10500, 'V'),
    (0x10528, 'X'),
    (0x10530, 'V'),
    (0x10564, 'X'),
    (0x1056F, 'V'),
    (0x10570, 'X'),
    (0x10600, 'V'),
    (0x10737, 'X'),
    (0x10740, 'V'),
    (0x10756, 'X'),
    (0x10760, 'V'),
    (0x10768, 'X'),
    (0x10800, 'V'),
    (0x10806, 'X'),
    (0x10808, 'V'),
    (0x10809, 'X'),
    (0x1080A, 'V'),
    (0x10836, 'X'),
    (0x10837, 'V'),
    (0x10839, 'X'),
    (0x1083C, 'V'),
    (0x1083D, 'X'),
    (0x1083F, 'V'),
    (0x10856, 'X'),
    (0x10857, 'V'),
    (0x1089F, 'X'),
    (0x108A7, 'V'),
    (0x108B0, 'X'),
    (0x108E0, 'V'),
    (0x108F3, 'X'),
    (0x108F4, 'V'),
    (0x108F6, 'X'),
    (0x108FB, 'V'),
    (0x1091C, 'X'),
    (0x1091F, 'V'),
    (0x1093A, 'X'),
    (0x1093F, 'V'),
    (0x10940, 'X'),
    (0x10980, 'V'),
    (0x109B8, 'X'),
    (0x109BC, 'V'),
    (0x109D0, 'X'),
    (0x109D2, 'V'),
    (0x10A04, 'X'),
    (0x10A05, 'V'),
    (0x10A07, 'X'),
    (0x10A0C, 'V'),
    (0x10A14, 'X'),
    (0x10A15, 'V'),
    (0x10A18, 'X'),
    (0x10A19, 'V'),
    (0x10A36, 'X'),
    (0x10A38, 'V'),
    (0x10A3B, 'X'),
    (0x10A3F, 'V'),
    (0x10A49, 'X'),
    (0x10A50, 'V'),
    (0x10A59, 'X'),
    (0x10A60, 'V'),
    (0x10AA0, 'X'),
    (0x10AC0, 'V'),
    (0x10AE7, 'X'),
    (0x10AEB, 'V'),
    (0x10AF7, 'X'),
    (0x10B00, 'V'),
    (0x10B36, 'X'),
    (0x10B39, 'V'),
    (0x10B56, 'X'),
    (0x10B58, 'V'),
    (0x10B73, 'X'),
    (0x10B78, 'V'),
    (0x10B92, 'X'),
    (0x10B99, 'V'),
    (0x10B9D, 'X'),
    (0x10BA9, 'V'),
    (0x10BB0, 'X'),
    ]

def _seg_55():
    return [
    (0x10C00, 'V'),
    (0x10C49, 'X'),
    (0x10C80, 'M', u'𐳀'),
    (0x10C81, 'M', u'𐳁'),
    (0x10C82, 'M', u'𐳂'),
    (0x10C83, 'M', u'𐳃'),
    (0x10C84, 'M', u'𐳄'),
    (0x10C85, 'M', u'𐳅'),
    (0x10C86, 'M', u'𐳆'),
    (0x10C87, 'M', u'𐳇'),
    (0x10C88, 'M', u'𐳈'),
    (0x10C89, 'M', u'𐳉'),
    (0x10C8A, 'M', u'𐳊'),
    (0x10C8B, 'M', u'𐳋'),
    (0x10C8C, 'M', u'𐳌'),
    (0x10C8D, 'M', u'𐳍'),
    (0x10C8E, 'M', u'𐳎'),
    (0x10C8F, 'M', u'𐳏'),
    (0x10C90, 'M', u'𐳐'),
    (0x10C91, 'M', u'𐳑'),
    (0x10C92, 'M', u'𐳒'),
    (0x10C93, 'M', u'𐳓'),
    (0x10C94, 'M', u'𐳔'),
    (0x10C95, 'M', u'𐳕'),
    (0x10C96, 'M', u'𐳖'),
    (0x10C97, 'M', u'𐳗'),
    (0x10C98, 'M', u'𐳘'),
    (0x10C99, 'M', u'𐳙'),
    (0x10C9A, 'M', u'𐳚'),
    (0x10C9B, 'M', u'𐳛'),
    (0x10C9C, 'M', u'𐳜'),
    (0x10C9D, 'M', u'𐳝'),
    (0x10C9E, 'M', u'𐳞'),
    (0x10C9F, 'M', u'𐳟'),
    (0x10CA0, 'M', u'𐳠'),
    (0x10CA1, 'M', u'𐳡'),
    (0x10CA2, 'M', u'𐳢'),
    (0x10CA3, 'M', u'𐳣'),
    (0x10CA4, 'M', u'𐳤'),
    (0x10CA5, 'M', u'𐳥'),
    (0x10CA6, 'M', u'𐳦'),
    (0x10CA7, 'M', u'𐳧'),
    (0x10CA8, 'M', u'𐳨'),
    (0x10CA9, 'M', u'𐳩'),
    (0x10CAA, 'M', u'𐳪'),
    (0x10CAB, 'M', u'𐳫'),
    (0x10CAC, 'M', u'𐳬'),
    (0x10CAD, 'M', u'𐳭'),
    (0x10CAE, 'M', u'𐳮'),
    (0x10CAF, 'M', u'𐳯'),
    (0x10CB0, 'M', u'𐳰'),
    (0x10CB1, 'M', u'𐳱'),
    (0x10CB2, 'M', u'𐳲'),
    (0x10CB3, 'X'),
    (0x10CC0, 'V'),
    (0x10CF3, 'X'),
    (0x10CFA, 'V'),
    (0x10D28, 'X'),
    (0x10D30, 'V'),
    (0x10D3A, 'X'),
    (0x10E60, 'V'),
    (0x10E7F, 'X'),
    (0x10E80, 'V'),
    (0x10EAA, 'X'),
    (0x10EAB, 'V'),
    (0x10EAE, 'X'),
    (0x10EB0, 'V'),
    (0x10EB2, 'X'),
    (0x10F00, 'V'),
    (0x10F28, 'X'),
    (0x10F30, 'V'),
    (0x10F5A, 'X'),
    (0x10FB0, 'V'),
    (0x10FCC, 'X'),
    (0x10FE0, 'V'),
    (0x10FF7, 'X'),
    (0x11000, 'V'),
    (0x1104E, 'X'),
    (0x11052, 'V'),
    (0x11070, 'X'),
    (0x1107F, 'V'),
    (0x110BD, 'X'),
    (0x110BE, 'V'),
    (0x110C2, 'X'),
    (0x110D0, 'V'),
    (0x110E9, 'X'),
    (0x110F0, 'V'),
    (0x110FA, 'X'),
    (0x11100, 'V'),
    (0x11135, 'X'),
    (0x11136, 'V'),
    (0x11148, 'X'),
    (0x11150, 'V'),
    (0x11177, 'X'),
    (0x11180, 'V'),
    (0x111E0, 'X'),
    (0x111E1, 'V'),
    (0x111F5, 'X'),
    (0x11200, 'V'),
    (0x11212, 'X'),
    ]

def _seg_56():
    return [
    (0x11213, 'V'),
    (0x1123F, 'X'),
    (0x11280, 'V'),
    (0x11287, 'X'),
    (0x11288, 'V'),
    (0x11289, 'X'),
    (0x1128A, 'V'),
    (0x1128E, 'X'),
    (0x1128F, 'V'),
    (0x1129E, 'X'),
    (0x1129F, 'V'),
    (0x112AA, 'X'),
    (0x112B0, 'V'),
    (0x112EB, 'X'),
    (0x112F0, 'V'),
    (0x112FA, 'X'),
    (0x11300, 'V'),
    (0x11304, 'X'),
    (0x11305, 'V'),
    (0x1130D, 'X'),
    (0x1130F, 'V'),
    (0x11311, 'X'),
    (0x11313, 'V'),
    (0x11329, 'X'),
    (0x1132A, 'V'),
    (0x11331, 'X'),
    (0x11332, 'V'),
    (0x11334, 'X'),
    (0x11335, 'V'),
    (0x1133A, 'X'),
    (0x1133B, 'V'),
    (0x11345, 'X'),
    (0x11347, 'V'),
    (0x11349, 'X'),
    (0x1134B, 'V'),
    (0x1134E, 'X'),
    (0x11350, 'V'),
    (0x11351, 'X'),
    (0x11357, 'V'),
    (0x11358, 'X'),
    (0x1135D, 'V'),
    (0x11364, 'X'),
    (0x11366, 'V'),
    (0x1136D, 'X'),
    (0x11370, 'V'),
    (0x11375, 'X'),
    (0x11400, 'V'),
    (0x1145C, 'X'),
    (0x1145D, 'V'),
    (0x11462, 'X'),
    (0x11480, 'V'),
    (0x114C8, 'X'),
    (0x114D0, 'V'),
    (0x114DA, 'X'),
    (0x11580, 'V'),
    (0x115B6, 'X'),
    (0x115B8, 'V'),
    (0x115DE, 'X'),
    (0x11600, 'V'),
    (0x11645, 'X'),
    (0x11650, 'V'),
    (0x1165A, 'X'),
    (0x11660, 'V'),
    (0x1166D, 'X'),
    (0x11680, 'V'),
    (0x116B9, 'X'),
    (0x116C0, 'V'),
    (0x116CA, 'X'),
    (0x11700, 'V'),
    (0x1171B, 'X'),
    (0x1171D, 'V'),
    (0x1172C, 'X'),
    (0x11730, 'V'),
    (0x11740, 'X'),
    (0x11800, 'V'),
    (0x1183C, 'X'),
    (0x118A0, 'M', u'𑣀'),
    (0x118A1, 'M', u'𑣁'),
    (0x118A2, 'M', u'𑣂'),
    (0x118A3, 'M', u'𑣃'),
    (0x118A4, 'M', u'𑣄'),
    (0x118A5, 'M', u'𑣅'),
    (0x118A6, 'M', u'𑣆'),
    (0x118A7, 'M', u'𑣇'),
    (0x118A8, 'M', u'𑣈'),
    (0x118A9, 'M', u'𑣉'),
    (0x118AA, 'M', u'𑣊'),
    (0x118AB, 'M', u'𑣋'),
    (0x118AC, 'M', u'𑣌'),
    (0x118AD, 'M', u'𑣍'),
    (0x118AE, 'M', u'𑣎'),
    (0x118AF, 'M', u'𑣏'),
    (0x118B0, 'M', u'𑣐'),
    (0x118B1, 'M', u'𑣑'),
    (0x118B2, 'M', u'𑣒'),
    (0x118B3, 'M', u'𑣓'),
    (0x118B4, 'M', u'𑣔'),
    (0x118B5, 'M', u'𑣕'),
    (0x118B6, 'M', u'𑣖'),
    (0x118B7, 'M', u'𑣗'),
    ]

def _seg_57():
    return [
    (0x118B8, 'M', u'𑣘'),
    (0x118B9, 'M', u'𑣙'),
    (0x118BA, 'M', u'𑣚'),
    (0x118BB, 'M', u'𑣛'),
    (0x118BC, 'M', u'𑣜'),
    (0x118BD, 'M', u'𑣝'),
    (0x118BE, 'M', u'𑣞'),
    (0x118BF, 'M', u'𑣟'),
    (0x118C0, 'V'),
    (0x118F3, 'X'),
    (0x118FF, 'V'),
    (0x11907, 'X'),
    (0x11909, 'V'),
    (0x1190A, 'X'),
    (0x1190C, 'V'),
    (0x11914, 'X'),
    (0x11915, 'V'),
    (0x11917, 'X'),
    (0x11918, 'V'),
    (0x11936, 'X'),
    (0x11937, 'V'),
    (0x11939, 'X'),
    (0x1193B, 'V'),
    (0x11947, 'X'),
    (0x11950, 'V'),
    (0x1195A, 'X'),
    (0x119A0, 'V'),
    (0x119A8, 'X'),
    (0x119AA, 'V'),
    (0x119D8, 'X'),
    (0x119DA, 'V'),
    (0x119E5, 'X'),
    (0x11A00, 'V'),
    (0x11A48, 'X'),
    (0x11A50, 'V'),
    (0x11AA3, 'X'),
    (0x11AC0, 'V'),
    (0x11AF9, 'X'),
    (0x11C00, 'V'),
    (0x11C09, 'X'),
    (0x11C0A, 'V'),
    (0x11C37, 'X'),
    (0x11C38, 'V'),
    (0x11C46, 'X'),
    (0x11C50, 'V'),
    (0x11C6D, 'X'),
    (0x11C70, 'V'),
    (0x11C90, 'X'),
    (0x11C92, 'V'),
    (0x11CA8, 'X'),
    (0x11CA9, 'V'),
    (0x11CB7, 'X'),
    (0x11D00, 'V'),
    (0x11D07, 'X'),
    (0x11D08, 'V'),
    (0x11D0A, 'X'),
    (0x11D0B, 'V'),
    (0x11D37, 'X'),
    (0x11D3A, 'V'),
    (0x11D3B, 'X'),
    (0x11D3C, 'V'),
    (0x11D3E, 'X'),
    (0x11D3F, 'V'),
    (0x11D48, 'X'),
    (0x11D50, 'V'),
    (0x11D5A, 'X'),
    (0x11D60, 'V'),
    (0x11D66, 'X'),
    (0x11D67, 'V'),
    (0x11D69, 'X'),
    (0x11D6A, 'V'),
    (0x11D8F, 'X'),
    (0x11D90, 'V'),
    (0x11D92, 'X'),
    (0x11D93, 'V'),
    (0x11D99, 'X'),
    (0x11DA0, 'V'),
    (0x11DAA, 'X'),
    (0x11EE0, 'V'),
    (0x11EF9, 'X'),
    (0x11FB0, 'V'),
    (0x11FB1, 'X'),
    (0x11FC0, 'V'),
    (0x11FF2, 'X'),
    (0x11FFF, 'V'),
    (0x1239A, 'X'),
    (0x12400, 'V'),
    (0x1246F, 'X'),
    (0x12470, 'V'),
    (0x12475, 'X'),
    (0x12480, 'V'),
    (0x12544, 'X'),
    (0x13000, 'V'),
    (0x1342F, 'X'),
    (0x14400, 'V'),
    (0x14647, 'X'),
    (0x16800, 'V'),
    (0x16A39, 'X'),
    (0x16A40, 'V'),
    (0x16A5F, 'X'),
    ]

def _seg_58():
    return [
    (0x16A60, 'V'),
    (0x16A6A, 'X'),
    (0x16A6E, 'V'),
    (0x16A70, 'X'),
    (0x16AD0, 'V'),
    (0x16AEE, 'X'),
    (0x16AF0, 'V'),
    (0x16AF6, 'X'),
    (0x16B00, 'V'),
    (0x16B46, 'X'),
    (0x16B50, 'V'),
    (0x16B5A, 'X'),
    (0x16B5B, 'V'),
    (0x16B62, 'X'),
    (0x16B63, 'V'),
    (0x16B78, 'X'),
    (0x16B7D, 'V'),
    (0x16B90, 'X'),
    (0x16E40, 'M', u'𖹠'),
    (0x16E41, 'M', u'𖹡'),
    (0x16E42, 'M', u'𖹢'),
    (0x16E43, 'M', u'𖹣'),
    (0x16E44, 'M', u'𖹤'),
    (0x16E45, 'M', u'𖹥'),
    (0x16E46, 'M', u'𖹦'),
    (0x16E47, 'M', u'𖹧'),
    (0x16E48, 'M', u'𖹨'),
    (0x16E49, 'M', u'𖹩'),
    (0x16E4A, 'M', u'𖹪'),
    (0x16E4B, 'M', u'𖹫'),
    (0x16E4C, 'M', u'𖹬'),
    (0x16E4D, 'M', u'𖹭'),
    (0x16E4E, 'M', u'𖹮'),
    (0x16E4F, 'M', u'𖹯'),
    (0x16E50, 'M', u'𖹰'),
    (0x16E51, 'M', u'𖹱'),
    (0x16E52, 'M', u'𖹲'),
    (0x16E53, 'M', u'𖹳'),
    (0x16E54, 'M', u'𖹴'),
    (0x16E55, 'M', u'𖹵'),
    (0x16E56, 'M', u'𖹶'),
    (0x16E57, 'M', u'𖹷'),
    (0x16E58, 'M', u'𖹸'),
    (0x16E59, 'M', u'𖹹'),
    (0x16E5A, 'M', u'𖹺'),
    (0x16E5B, 'M', u'𖹻'),
    (0x16E5C, 'M', u'𖹼'),
    (0x16E5D, 'M', u'𖹽'),
    (0x16E5E, 'M', u'𖹾'),
    (0x16E5F, 'M', u'𖹿'),
    (0x16E60, 'V'),
    (0x16E9B, 'X'),
    (0x16F00, 'V'),
    (0x16F4B, 'X'),
    (0x16F4F, 'V'),
    (0x16F88, 'X'),
    (0x16F8F, 'V'),
    (0x16FA0, 'X'),
    (0x16FE0, 'V'),
    (0x16FE5, 'X'),
    (0x16FF0, 'V'),
    (0x16FF2, 'X'),
    (0x17000, 'V'),
    (0x187F8, 'X'),
    (0x18800, 'V'),
    (0x18CD6, 'X'),
    (0x18D00, 'V'),
    (0x18D09, 'X'),
    (0x1B000, 'V'),
    (0x1B11F, 'X'),
    (0x1B150, 'V'),
    (0x1B153, 'X'),
    (0x1B164, 'V'),
    (0x1B168, 'X'),
    (0x1B170, 'V'),
    (0x1B2FC, 'X'),
    (0x1BC00, 'V'),
    (0x1BC6B, 'X'),
    (0x1BC70, 'V'),
    (0x1BC7D, 'X'),
    (0x1BC80, 'V'),
    (0x1BC89, 'X'),
    (0x1BC90, 'V'),
    (0x1BC9A, 'X'),
    (0x1BC9C, 'V'),
    (0x1BCA0, 'I'),
    (0x1BCA4, 'X'),
    (0x1D000, 'V'),
    (0x1D0F6, 'X'),
    (0x1D100, 'V'),
    (0x1D127, 'X'),
    (0x1D129, 'V'),
    (0x1D15E, 'M', u'𝅗𝅥'),
    (0x1D15F, 'M', u'𝅘𝅥'),
    (0x1D160, 'M', u'𝅘𝅥𝅮'),
    (0x1D161, 'M', u'𝅘𝅥𝅯'),
    (0x1D162, 'M', u'𝅘𝅥𝅰'),
    (0x1D163, 'M', u'𝅘𝅥𝅱'),
    (0x1D164, 'M', u'𝅘𝅥𝅲'),
    (0x1D165, 'V'),
    ]

def _seg_59():
    return [
    (0x1D173, 'X'),
    (0x1D17B, 'V'),
    (0x1D1BB, 'M', u'𝆹𝅥'),
    (0x1D1BC, 'M', u'𝆺𝅥'),
    (0x1D1BD, 'M', u'𝆹𝅥𝅮'),
    (0x1D1BE, 'M', u'𝆺𝅥𝅮'),
    (0x1D1BF, 'M', u'𝆹𝅥𝅯'),
    (0x1D1C0, 'M', u'𝆺𝅥𝅯'),
    (0x1D1C1, 'V'),
    (0x1D1E9, 'X'),
    (0x1D200, 'V'),
    (0x1D246, 'X'),
    (0x1D2E0, 'V'),
    (0x1D2F4, 'X'),
    (0x1D300, 'V'),
    (0x1D357, 'X'),
    (0x1D360, 'V'),
    (0x1D379, 'X'),
    (0x1D400, 'M', u'a'),
    (0x1D401, 'M', u'b'),
    (0x1D402, 'M', u'c'),
    (0x1D403, 'M', u'd'),
    (0x1D404, 'M', u'e'),
    (0x1D405, 'M', u'f'),
    (0x1D406, 'M', u'g'),
    (0x1D407, 'M', u'h'),
    (0x1D408, 'M', u'i'),
    (0x1D409, 'M', u'j'),
    (0x1D40A, 'M', u'k'),
    (0x1D40B, 'M', u'l'),
    (0x1D40C, 'M', u'm'),
    (0x1D40D, 'M', u'n'),
    (0x1D40E, 'M', u'o'),
    (0x1D40F, 'M', u'p'),
    (0x1D410, 'M', u'q'),
    (0x1D411, 'M', u'r'),
    (0x1D412, 'M', u's'),
    (0x1D413, 'M', u't'),
    (0x1D414, 'M', u'u'),
    (0x1D415, 'M', u'v'),
    (0x1D416, 'M', u'w'),
    (0x1D417, 'M', u'x'),
    (0x1D418, 'M', u'y'),
    (0x1D419, 'M', u'z'),
    (0x1D41A, 'M', u'a'),
    (0x1D41B, 'M', u'b'),
    (0x1D41C, 'M', u'c'),
    (0x1D41D, 'M', u'd'),
    (0x1D41E, 'M', u'e'),
    (0x1D41F, 'M', u'f'),
    (0x1D420, 'M', u'g'),
    (0x1D421, 'M', u'h'),
    (0x1D422, 'M', u'i'),
    (0x1D423, 'M', u'j'),
    (0x1D424, 'M', u'k'),
    (0x1D425, 'M', u'l'),
    (0x1D426, 'M', u'm'),
    (0x1D427, 'M', u'n'),
    (0x1D428, 'M', u'o'),
    (0x1D429, 'M', u'p'),
    (0x1D42A, 'M', u'q'),
    (0x1D42B, 'M', u'r'),
    (0x1D42C, 'M', u's'),
    (0x1D42D, 'M', u't'),
    (0x1D42E, 'M', u'u'),
    (0x1D42F, 'M', u'v'),
    (0x1D430, 'M', u'w'),
    (0x1D431, 'M', u'x'),
    (0x1D432, 'M', u'y'),
    (0x1D433, 'M', u'z'),
    (0x1D434, 'M', u'a'),
    (0x1D435, 'M', u'b'),
    (0x1D436, 'M', u'c'),
    (0x1D437, 'M', u'd'),
    (0x1D438, 'M', u'e'),
    (0x1D439, 'M', u'f'),
    (0x1D43A, 'M', u'g'),
    (0x1D43B, 'M', u'h'),
    (0x1D43C, 'M', u'i'),
    (0x1D43D, 'M', u'j'),
    (0x1D43E, 'M', u'k'),
    (0x1D43F, 'M', u'l'),
    (0x1D440, 'M', u'm'),
    (0x1D441, 'M', u'n'),
    (0x1D442, 'M', u'o'),
    (0x1D443, 'M', u'p'),
    (0x1D444, 'M', u'q'),
    (0x1D445, 'M', u'r'),
    (0x1D446, 'M', u's'),
    (0x1D447, 'M', u't'),
    (0x1D448, 'M', u'u'),
    (0x1D449, 'M', u'v'),
    (0x1D44A, 'M', u'w'),
    (0x1D44B, 'M', u'x'),
    (0x1D44C, 'M', u'y'),
    (0x1D44D, 'M', u'z'),
    (0x1D44E, 'M', u'a'),
    (0x1D44F, 'M', u'b'),
    (0x1D450, 'M', u'c'),
    (0x1D451, 'M', u'd'),
    ]

def _seg_60():
    return [
    (0x1D452, 'M', u'e'),
    (0x1D453, 'M', u'f'),
    (0x1D454, 'M', u'g'),
    (0x1D455, 'X'),
    (0x1D456, 'M', u'i'),
    (0x1D457, 'M', u'j'),
    (0x1D458, 'M', u'k'),
    (0x1D459, 'M', u'l'),
    (0x1D45A, 'M', u'm'),
    (0x1D45B, 'M', u'n'),
    (0x1D45C, 'M', u'o'),
    (0x1D45D, 'M', u'p'),
    (0x1D45E, 'M', u'q'),
    (0x1D45F, 'M', u'r'),
    (0x1D460, 'M', u's'),
    (0x1D461, 'M', u't'),
    (0x1D462, 'M', u'u'),
    (0x1D463, 'M', u'v'),
    (0x1D464, 'M', u'w'),
    (0x1D465, 'M', u'x'),
    (0x1D466, 'M', u'y'),
    (0x1D467, 'M', u'z'),
    (0x1D468, 'M', u'a'),
    (0x1D469, 'M', u'b'),
    (0x1D46A, 'M', u'c'),
    (0x1D46B, 'M', u'd'),
    (0x1D46C, 'M', u'e'),
    (0x1D46D, 'M', u'f'),
    (0x1D46E, 'M', u'g'),
    (0x1D46F, 'M', u'h'),
    (0x1D470, 'M', u'i'),
    (0x1D471, 'M', u'j'),
    (0x1D472, 'M', u'k'),
    (0x1D473, 'M', u'l'),
    (0x1D474, 'M', u'm'),
    (0x1D475, 'M', u'n'),
    (0x1D476, 'M', u'o'),
    (0x1D477, 'M', u'p'),
    (0x1D478, 'M', u'q'),
    (0x1D479, 'M', u'r'),
    (0x1D47A, 'M', u's'),
    (0x1D47B, 'M', u't'),
    (0x1D47C, 'M', u'u'),
    (0x1D47D, 'M', u'v'),
    (0x1D47E, 'M', u'w'),
    (0x1D47F, 'M', u'x'),
    (0x1D480, 'M', u'y'),
    (0x1D481, 'M', u'z'),
    (0x1D482, 'M', u'a'),
    (0x1D483, 'M', u'b'),
    (0x1D484, 'M', u'c'),
    (0x1D485, 'M', u'd'),
    (0x1D486, 'M', u'e'),
    (0x1D487, 'M', u'f'),
    (0x1D488, 'M', u'g'),
    (0x1D489, 'M', u'h'),
    (0x1D48A, 'M', u'i'),
    (0x1D48B, 'M', u'j'),
    (0x1D48C, 'M', u'k'),
    (0x1D48D, 'M', u'l'),
    (0x1D48E, 'M', u'm'),
    (0x1D48F, 'M', u'n'),
    (0x1D490, 'M', u'o'),
    (0x1D491, 'M', u'p'),
    (0x1D492, 'M', u'q'),
    (0x1D493, 'M', u'r'),
    (0x1D494, 'M', u's'),
    (0x1D495, 'M', u't'),
    (0x1D496, 'M', u'u'),
    (0x1D497, 'M', u'v'),
    (0x1D498, 'M', u'w'),
    (0x1D499, 'M', u'x'),
    (0x1D49A, 'M', u'y'),
    (0x1D49B, 'M', u'z'),
    (0x1D49C, 'M', u'a'),
    (0x1D49D, 'X'),
    (0x1D49E, 'M', u'c'),
    (0x1D49F, 'M', u'd'),
    (0x1D4A0, 'X'),
    (0x1D4A2, 'M', u'g'),
    (0x1D4A3, 'X'),
    (0x1D4A5, 'M', u'j'),
    (0x1D4A6, 'M', u'k'),
    (0x1D4A7, 'X'),
    (0x1D4A9, 'M', u'n'),
    (0x1D4AA, 'M', u'o'),
    (0x1D4AB, 'M', u'p'),
    (0x1D4AC, 'M', u'q'),
    (0x1D4AD, 'X'),
    (0x1D4AE, 'M', u's'),
    (0x1D4AF, 'M', u't'),
    (0x1D4B0, 'M', u'u'),
    (0x1D4B1, 'M', u'v'),
    (0x1D4B2, 'M', u'w'),
    (0x1D4B3, 'M', u'x'),
    (0x1D4B4, 'M', u'y'),
    (0x1D4B5, 'M', u'z'),
    (0x1D4B6, 'M', u'a'),
    (0x1D4B7, 'M', u'b'),
    (0x1D4B8, 'M', u'c'),
    ]

def _seg_61():
    return [
    (0x1D4B9, 'M', u'd'),
    (0x1D4BA, 'X'),
    (0x1D4BB, 'M', u'f'),
    (0x1D4BC, 'X'),
    (0x1D4BD, 'M', u'h'),
    (0x1D4BE, 'M', u'i'),
    (0x1D4BF, 'M', u'j'),
    (0x1D4C0, 'M', u'k'),
    (0x1D4C1, 'M', u'l'),
    (0x1D4C2, 'M', u'm'),
    (0x1D4C3, 'M', u'n'),
    (0x1D4C4, 'X'),
    (0x1D4C5, 'M', u'p'),
    (0x1D4C6, 'M', u'q'),
    (0x1D4C7, 'M', u'r'),
    (0x1D4C8, 'M', u's'),
    (0x1D4C9, 'M', u't'),
    (0x1D4CA, 'M', u'u'),
    (0x1D4CB, 'M', u'v'),
    (0x1D4CC, 'M', u'w'),
    (0x1D4CD, 'M', u'x'),
    (0x1D4CE, 'M', u'y'),
    (0x1D4CF, 'M', u'z'),
    (0x1D4D0, 'M', u'a'),
    (0x1D4D1, 'M', u'b'),
    (0x1D4D2, 'M', u'c'),
    (0x1D4D3, 'M', u'd'),
    (0x1D4D4, 'M', u'e'),
    (0x1D4D5, 'M', u'f'),
    (0x1D4D6, 'M', u'g'),
    (0x1D4D7, 'M', u'h'),
    (0x1D4D8, 'M', u'i'),
    (0x1D4D9, 'M', u'j'),
    (0x1D4DA, 'M', u'k'),
    (0x1D4DB, 'M', u'l'),
    (0x1D4DC, 'M', u'm'),
    (0x1D4DD, 'M', u'n'),
    (0x1D4DE, 'M', u'o'),
    (0x1D4DF, 'M', u'p'),
    (0x1D4E0, 'M', u'q'),
    (0x1D4E1, 'M', u'r'),
    (0x1D4E2, 'M', u's'),
    (0x1D4E3, 'M', u't'),
    (0x1D4E4, 'M', u'u'),
    (0x1D4E5, 'M', u'v'),
    (0x1D4E6, 'M', u'w'),
    (0x1D4E7, 'M', u'x'),
    (0x1D4E8, 'M', u'y'),
    (0x1D4E9, 'M', u'z'),
    (0x1D4EA, 'M', u'a'),
    (0x1D4EB, 'M', u'b'),
    (0x1D4EC, 'M', u'c'),
    (0x1D4ED, 'M', u'd'),
    (0x1D4EE, 'M', u'e'),
    (0x1D4EF, 'M', u'f'),
    (0x1D4F0, 'M', u'g'),
    (0x1D4F1, 'M', u'h'),
    (0x1D4F2, 'M', u'i'),
    (0x1D4F3, 'M', u'j'),
    (0x1D4F4, 'M', u'k'),
    (0x1D4F5, 'M', u'l'),
    (0x1D4F6, 'M', u'm'),
    (0x1D4F7, 'M', u'n'),
    (0x1D4F8, 'M', u'o'),
    (0x1D4F9, 'M', u'p'),
    (0x1D4FA, 'M', u'q'),
    (0x1D4FB, 'M', u'r'),
    (0x1D4FC, 'M', u's'),
    (0x1D4FD, 'M', u't'),
    (0x1D4FE, 'M', u'u'),
    (0x1D4FF, 'M', u'v'),
    (0x1D500, 'M', u'w'),
    (0x1D501, 'M', u'x'),
    (0x1D502, 'M', u'y'),
    (0x1D503, 'M', u'z'),
    (0x1D504, 'M', u'a'),
    (0x1D505, 'M', u'b'),
    (0x1D506, 'X'),
    (0x1D507, 'M', u'd'),
    (0x1D508, 'M', u'e'),
    (0x1D509, 'M', u'f'),
    (0x1D50A, 'M', u'g'),
    (0x1D50B, 'X'),
    (0x1D50D, 'M', u'j'),
    (0x1D50E, 'M', u'k'),
    (0x1D50F, 'M', u'l'),
    (0x1D510, 'M', u'm'),
    (0x1D511, 'M', u'n'),
    (0x1D512, 'M', u'o'),
    (0x1D513, 'M', u'p'),
    (0x1D514, 'M', u'q'),
    (0x1D515, 'X'),
    (0x1D516, 'M', u's'),
    (0x1D517, 'M', u't'),
    (0x1D518, 'M', u'u'),
    (0x1D519, 'M', u'v'),
    (0x1D51A, 'M', u'w'),
    (0x1D51B, 'M', u'x'),
    (0x1D51C, 'M', u'y'),
    (0x1D51D, 'X'),
    ]

def _seg_62():
    return [
    (0x1D51E, 'M', u'a'),
    (0x1D51F, 'M', u'b'),
    (0x1D520, 'M', u'c'),
    (0x1D521, 'M', u'd'),
    (0x1D522, 'M', u'e'),
    (0x1D523, 'M', u'f'),
    (0x1D524, 'M', u'g'),
    (0x1D525, 'M', u'h'),
    (0x1D526, 'M', u'i'),
    (0x1D527, 'M', u'j'),
    (0x1D528, 'M', u'k'),
    (0x1D529, 'M', u'l'),
    (0x1D52A, 'M', u'm'),
    (0x1D52B, 'M', u'n'),
    (0x1D52C, 'M', u'o'),
    (0x1D52D, 'M', u'p'),
    (0x1D52E, 'M', u'q'),
    (0x1D52F, 'M', u'r'),
    (0x1D530, 'M', u's'),
    (0x1D531, 'M', u't'),
    (0x1D532, 'M', u'u'),
    (0x1D533, 'M', u'v'),
    (0x1D534, 'M', u'w'),
    (0x1D535, 'M', u'x'),
    (0x1D536, 'M', u'y'),
    (0x1D537, 'M', u'z'),
    (0x1D538, 'M', u'a'),
    (0x1D539, 'M', u'b'),
    (0x1D53A, 'X'),
    (0x1D53B, 'M', u'd'),
    (0x1D53C, 'M', u'e'),
    (0x1D53D, 'M', u'f'),
    (0x1D53E, 'M', u'g'),
    (0x1D53F, 'X'),
    (0x1D540, 'M', u'i'),
    (0x1D541, 'M', u'j'),
    (0x1D542, 'M', u'k'),
    (0x1D543, 'M', u'l'),
    (0x1D544, 'M', u'm'),
    (0x1D545, 'X'),
    (0x1D546, 'M', u'o'),
    (0x1D547, 'X'),
    (0x1D54A, 'M', u's'),
    (0x1D54B, 'M', u't'),
    (0x1D54C, 'M', u'u'),
    (0x1D54D, 'M', u'v'),
    (0x1D54E, 'M', u'w'),
    (0x1D54F, 'M', u'x'),
    (0x1D550, 'M', u'y'),
    (0x1D551, 'X'),
    (0x1D552, 'M', u'a'),
    (0x1D553, 'M', u'b'),
    (0x1D554, 'M', u'c'),
    (0x1D555, 'M', u'd'),
    (0x1D556, 'M', u'e'),
    (0x1D557, 'M', u'f'),
    (0x1D558, 'M', u'g'),
    (0x1D559, 'M', u'h'),
    (0x1D55A, 'M', u'i'),
    (0x1D55B, 'M', u'j'),
    (0x1D55C, 'M', u'k'),
    (0x1D55D, 'M', u'l'),
    (0x1D55E, 'M', u'm'),
    (0x1D55F, 'M', u'n'),
    (0x1D560, 'M', u'o'),
    (0x1D561, 'M', u'p'),
    (0x1D562, 'M', u'q'),
    (0x1D563, 'M', u'r'),
    (0x1D564, 'M', u's'),
    (0x1D565, 'M', u't'),
    (0x1D566, 'M', u'u'),
    (0x1D567, 'M', u'v'),
    (0x1D568, 'M', u'w'),
    (0x1D569, 'M', u'x'),
    (0x1D56A, 'M', u'y'),
    (0x1D56B, 'M', u'z'),
    (0x1D56C, 'M', u'a'),
    (0x1D56D, 'M', u'b'),
    (0x1D56E, 'M', u'c'),
    (0x1D56F, 'M', u'd'),
    (0x1D570, 'M', u'e'),
    (0x1D571, 'M', u'f'),
    (0x1D572, 'M', u'g'),
    (0x1D573, 'M', u'h'),
    (0x1D574, 'M', u'i'),
    (0x1D575, 'M', u'j'),
    (0x1D576, 'M', u'k'),
    (0x1D577, 'M', u'l'),
    (0x1D578, 'M', u'm'),
    (0x1D579, 'M', u'n'),
    (0x1D57A, 'M', u'o'),
    (0x1D57B, 'M', u'p'),
    (0x1D57C, 'M', u'q'),
    (0x1D57D, 'M', u'r'),
    (0x1D57E, 'M', u's'),
    (0x1D57F, 'M', u't'),
    (0x1D580, 'M', u'u'),
    (0x1D581, 'M', u'v'),
    (0x1D582, 'M', u'w'),
    (0x1D583, 'M', u'x'),
    ]

def _seg_63():
    return [
    (0x1D584, 'M', u'y'),
    (0x1D585, 'M', u'z'),
    (0x1D586, 'M', u'a'),
    (0x1D587, 'M', u'b'),
    (0x1D588, 'M', u'c'),
    (0x1D589, 'M', u'd'),
    (0x1D58A, 'M', u'e'),
    (0x1D58B, 'M', u'f'),
    (0x1D58C, 'M', u'g'),
    (0x1D58D, 'M', u'h'),
    (0x1D58E, 'M', u'i'),
    (0x1D58F, 'M', u'j'),
    (0x1D590, 'M', u'k'),
    (0x1D591, 'M', u'l'),
    (0x1D592, 'M', u'm'),
    (0x1D593, 'M', u'n'),
    (0x1D594, 'M', u'o'),
    (0x1D595, 'M', u'p'),
    (0x1D596, 'M', u'q'),
    (0x1D597, 'M', u'r'),
    (0x1D598, 'M', u's'),
    (0x1D599, 'M', u't'),
    (0x1D59A, 'M', u'u'),
    (0x1D59B, 'M', u'v'),
    (0x1D59C, 'M', u'w'),
    (0x1D59D, 'M', u'x'),
    (0x1D59E, 'M', u'y'),
    (0x1D59F, 'M', u'z'),
    (0x1D5A0, 'M', u'a'),
    (0x1D5A1, 'M', u'b'),
    (0x1D5A2, 'M', u'c'),
    (0x1D5A3, 'M', u'd'),
    (0x1D5A4, 'M', u'e'),
    (0x1D5A5, 'M', u'f'),
    (0x1D5A6, 'M', u'g'),
    (0x1D5A7, 'M', u'h'),
    (0x1D5A8, 'M', u'i'),
    (0x1D5A9, 'M', u'j'),
    (0x1D5AA, 'M', u'k'),
    (0x1D5AB, 'M', u'l'),
    (0x1D5AC, 'M', u'm'),
    (0x1D5AD, 'M', u'n'),
    (0x1D5AE, 'M', u'o'),
    (0x1D5AF, 'M', u'p'),
    (0x1D5B0, 'M', u'q'),
    (0x1D5B1, 'M', u'r'),
    (0x1D5B2, 'M', u's'),
    (0x1D5B3, 'M', u't'),
    (0x1D5B4, 'M', u'u'),
    (0x1D5B5, 'M', u'v'),
    (0x1D5B6, 'M', u'w'),
    (0x1D5B7, 'M', u'x'),
    (0x1D5B8, 'M', u'y'),
    (0x1D5B9, 'M', u'z'),
    (0x1D5BA, 'M', u'a'),
    (0x1D5BB, 'M', u'b'),
    (0x1D5BC, 'M', u'c'),
    (0x1D5BD, 'M', u'd'),
    (0x1D5BE, 'M', u'e'),
    (0x1D5BF, 'M', u'f'),
    (0x1D5C0, 'M', u'g'),
    (0x1D5C1, 'M', u'h'),
    (0x1D5C2, 'M', u'i'),
    (0x1D5C3, 'M', u'j'),
    (0x1D5C4, 'M', u'k'),
    (0x1D5C5, 'M', u'l'),
    (0x1D5C6, 'M', u'm'),
    (0x1D5C7, 'M', u'n'),
    (0x1D5C8, 'M', u'o'),
    (0x1D5C9, 'M', u'p'),
    (0x1D5CA, 'M', u'q'),
    (0x1D5CB, 'M', u'r'),
    (0x1D5CC, 'M', u's'),
    (0x1D5CD, 'M', u't'),
    (0x1D5CE, 'M', u'u'),
    (0x1D5CF, 'M', u'v'),
    (0x1D5D0, 'M', u'w'),
    (0x1D5D1, 'M', u'x'),
    (0x1D5D2, 'M', u'y'),
    (0x1D5D3, 'M', u'z'),
    (0x1D5D4, 'M', u'a'),
    (0x1D5D5, 'M', u'b'),
    (0x1D5D6, 'M', u'c'),
    (0x1D5D7, 'M', u'd'),
    (0x1D5D8, 'M', u'e'),
    (0x1D5D9, 'M', u'f'),
    (0x1D5DA, 'M', u'g'),
    (0x1D5DB, 'M', u'h'),
    (0x1D5DC, 'M', u'i'),
    (0x1D5DD, 'M', u'j'),
    (0x1D5DE, 'M', u'k'),
    (0x1D5DF, 'M', u'l'),
    (0x1D5E0, 'M', u'm'),
    (0x1D5E1, 'M', u'n'),
    (0x1D5E2, 'M', u'o'),
    (0x1D5E3, 'M', u'p'),
    (0x1D5E4, 'M', u'q'),
    (0x1D5E5, 'M', u'r'),
    (0x1D5E6, 'M', u's'),
    (0x1D5E7, 'M', u't'),
    ]

def _seg_64():
    return [
    (0x1D5E8, 'M', u'u'),
    (0x1D5E9, 'M', u'v'),
    (0x1D5EA, 'M', u'w'),
    (0x1D5EB, 'M', u'x'),
    (0x1D5EC, 'M', u'y'),
    (0x1D5ED, 'M', u'z'),
    (0x1D5EE, 'M', u'a'),
    (0x1D5EF, 'M', u'b'),
    (0x1D5F0, 'M', u'c'),
    (0x1D5F1, 'M', u'd'),
    (0x1D5F2, 'M', u'e'),
    (0x1D5F3, 'M', u'f'),
    (0x1D5F4, 'M', u'g'),
    (0x1D5F5, 'M', u'h'),
    (0x1D5F6, 'M', u'i'),
    (0x1D5F7, 'M', u'j'),
    (0x1D5F8, 'M', u'k'),
    (0x1D5F9, 'M', u'l'),
    (0x1D5FA, 'M', u'm'),
    (0x1D5FB, 'M', u'n'),
    (0x1D5FC, 'M', u'o'),
    (0x1D5FD, 'M', u'p'),
    (0x1D5FE, 'M', u'q'),
    (0x1D5FF, 'M', u'r'),
    (0x1D600, 'M', u's'),
    (0x1D601, 'M', u't'),
    (0x1D602, 'M', u'u'),
    (0x1D603, 'M', u'v'),
    (0x1D604, 'M', u'w'),
    (0x1D605, 'M', u'x'),
    (0x1D606, 'M', u'y'),
    (0x1D607, 'M', u'z'),
    (0x1D608, 'M', u'a'),
    (0x1D609, 'M', u'b'),
    (0x1D60A, 'M', u'c'),
    (0x1D60B, 'M', u'd'),
    (0x1D60C, 'M', u'e'),
    (0x1D60D, 'M', u'f'),
    (0x1D60E, 'M', u'g'),
    (0x1D60F, 'M', u'h'),
    (0x1D610, 'M', u'i'),
    (0x1D611, 'M', u'j'),
    (0x1D612, 'M', u'k'),
    (0x1D613, 'M', u'l'),
    (0x1D614, 'M', u'm'),
    (0x1D615, 'M', u'n'),
    (0x1D616, 'M', u'o'),
    (0x1D617, 'M', u'p'),
    (0x1D618, 'M', u'q'),
    (0x1D619, 'M', u'r'),
    (0x1D61A, 'M', u's'),
    (0x1D61B, 'M', u't'),
    (0x1D61C, 'M', u'u'),
    (0x1D61D, 'M', u'v'),
    (0x1D61E, 'M', u'w'),
    (0x1D61F, 'M', u'x'),
    (0x1D620, 'M', u'y'),
    (0x1D621, 'M', u'z'),
    (0x1D622, 'M', u'a'),
    (0x1D623, 'M', u'b'),
    (0x1D624, 'M', u'c'),
    (0x1D625, 'M', u'd'),
    (0x1D626, 'M', u'e'),
    (0x1D627, 'M', u'f'),
    (0x1D628, 'M', u'g'),
    (0x1D629, 'M', u'h'),
    (0x1D62A, 'M', u'i'),
    (0x1D62B, 'M', u'j'),
    (0x1D62C, 'M', u'k'),
    (0x1D62D, 'M', u'l'),
    (0x1D62E, 'M', u'm'),
    (0x1D62F, 'M', u'n'),
    (0x1D630, 'M', u'o'),
    (0x1D631, 'M', u'p'),
    (0x1D632, 'M', u'q'),
    (0x1D633, 'M', u'r'),
    (0x1D634, 'M', u's'),
    (0x1D635, 'M', u't'),
    (0x1D636, 'M', u'u'),
    (0x1D637, 'M', u'v'),
    (0x1D638, 'M', u'w'),
    (0x1D639, 'M', u'x'),
    (0x1D63A, 'M', u'y'),
    (0x1D63B, 'M', u'z'),
    (0x1D63C, 'M', u'a'),
    (0x1D63D, 'M', u'b'),
    (0x1D63E, 'M', u'c'),
    (0x1D63F, 'M', u'd'),
    (0x1D640, 'M', u'e'),
    (0x1D641, 'M', u'f'),
    (0x1D642, 'M', u'g'),
    (0x1D643, 'M', u'h'),
    (0x1D644, 'M', u'i'),
    (0x1D645, 'M', u'j'),
    (0x1D646, 'M', u'k'),
    (0x1D647, 'M', u'l'),
    (0x1D648, 'M', u'm'),
    (0x1D649, 'M', u'n'),
    (0x1D64A, 'M', u'o'),
    (0x1D64B, 'M', u'p'),
    ]

def _seg_65():
    return [
    (0x1D64C, 'M', u'q'),
    (0x1D64D, 'M', u'r'),
    (0x1D64E, 'M', u's'),
    (0x1D64F, 'M', u't'),
    (0x1D650, 'M', u'u'),
    (0x1D651, 'M', u'v'),
    (0x1D652, 'M', u'w'),
    (0x1D653, 'M', u'x'),
    (0x1D654, 'M', u'y'),
    (0x1D655, 'M', u'z'),
    (0x1D656, 'M', u'a'),
    (0x1D657, 'M', u'b'),
    (0x1D658, 'M', u'c'),
    (0x1D659, 'M', u'd'),
    (0x1D65A, 'M', u'e'),
    (0x1D65B, 'M', u'f'),
    (0x1D65C, 'M', u'g'),
    (0x1D65D, 'M', u'h'),
    (0x1D65E, 'M', u'i'),
    (0x1D65F, 'M', u'j'),
    (0x1D660, 'M', u'k'),
    (0x1D661, 'M', u'l'),
    (0x1D662, 'M', u'm'),
    (0x1D663, 'M', u'n'),
    (0x1D664, 'M', u'o'),
    (0x1D665, 'M', u'p'),
    (0x1D666, 'M', u'q'),
    (0x1D667, 'M', u'r'),
    (0x1D668, 'M', u's'),
    (0x1D669, 'M', u't'),
    (0x1D66A, 'M', u'u'),
    (0x1D66B, 'M', u'v'),
    (0x1D66C, 'M', u'w'),
    (0x1D66D, 'M', u'x'),
    (0x1D66E, 'M', u'y'),
    (0x1D66F, 'M', u'z'),
    (0x1D670, 'M', u'a'),
    (0x1D671, 'M', u'b'),
    (0x1D672, 'M', u'c'),
    (0x1D673, 'M', u'd'),
    (0x1D674, 'M', u'e'),
    (0x1D675, 'M', u'f'),
    (0x1D676, 'M', u'g'),
    (0x1D677, 'M', u'h'),
    (0x1D678, 'M', u'i'),
    (0x1D679, 'M', u'j'),
    (0x1D67A, 'M', u'k'),
    (0x1D67B, 'M', u'l'),
    (0x1D67C, 'M', u'm'),
    (0x1D67D, 'M', u'n'),
    (0x1D67E, 'M', u'o'),
    (0x1D67F, 'M', u'p'),
    (0x1D680, 'M', u'q'),
    (0x1D681, 'M', u'r'),
    (0x1D682, 'M', u's'),
    (0x1D683, 'M', u't'),
    (0x1D684, 'M', u'u'),
    (0x1D685, 'M', u'v'),
    (0x1D686, 'M', u'w'),
    (0x1D687, 'M', u'x'),
    (0x1D688, 'M', u'y'),
    (0x1D689, 'M', u'z'),
    (0x1D68A, 'M', u'a'),
    (0x1D68B, 'M', u'b'),
    (0x1D68C, 'M', u'c'),
    (0x1D68D, 'M', u'd'),
    (0x1D68E, 'M', u'e'),
    (0x1D68F, 'M', u'f'),
    (0x1D690, 'M', u'g'),
    (0x1D691, 'M', u'h'),
    (0x1D692, 'M', u'i'),
    (0x1D693, 'M', u'j'),
    (0x1D694, 'M', u'k'),
    (0x1D695, 'M', u'l'),
    (0x1D696, 'M', u'm'),
    (0x1D697, 'M', u'n'),
    (0x1D698, 'M', u'o'),
    (0x1D699, 'M', u'p'),
    (0x1D69A, 'M', u'q'),
    (0x1D69B, 'M', u'r'),
    (0x1D69C, 'M', u's'),
    (0x1D69D, 'M', u't'),
    (0x1D69E, 'M', u'u'),
    (0x1D69F, 'M', u'v'),
    (0x1D6A0, 'M', u'w'),
    (0x1D6A1, 'M', u'x'),
    (0x1D6A2, 'M', u'y'),
    (0x1D6A3, 'M', u'z'),
    (0x1D6A4, 'M', u'ı'),
    (0x1D6A5, 'M', u'ȷ'),
    (0x1D6A6, 'X'),
    (0x1D6A8, 'M', u'α'),
    (0x1D6A9, 'M', u'β'),
    (0x1D6AA, 'M', u'γ'),
    (0x1D6AB, 'M', u'δ'),
    (0x1D6AC, 'M', u'ε'),
    (0x1D6AD, 'M', u'ζ'),
    (0x1D6AE, 'M', u'η'),
    (0x1D6AF, 'M', u'θ'),
    (0x1D6B0, 'M', u'ι'),
    ]

def _seg_66():
    return [
    (0x1D6B1, 'M', u'κ'),
    (0x1D6B2, 'M', u'λ'),
    (0x1D6B3, 'M', u'μ'),
    (0x1D6B4, 'M', u'ν'),
    (0x1D6B5, 'M', u'ξ'),
    (0x1D6B6, 'M', u'ο'),
    (0x1D6B7, 'M', u'π'),
    (0x1D6B8, 'M', u'ρ'),
    (0x1D6B9, 'M', u'θ'),
    (0x1D6BA, 'M', u'σ'),
    (0x1D6BB, 'M', u'τ'),
    (0x1D6BC, 'M', u'υ'),
    (0x1D6BD, 'M', u'φ'),
    (0x1D6BE, 'M', u'χ'),
    (0x1D6BF, 'M', u'ψ'),
    (0x1D6C0, 'M', u'ω'),
    (0x1D6C1, 'M', u'∇'),
    (0x1D6C2, 'M', u'α'),
    (0x1D6C3, 'M', u'β'),
    (0x1D6C4, 'M', u'γ'),
    (0x1D6C5, 'M', u'δ'),
    (0x1D6C6, 'M', u'ε'),
    (0x1D6C7, 'M', u'ζ'),
    (0x1D6C8, 'M', u'η'),
    (0x1D6C9, 'M', u'θ'),
    (0x1D6CA, 'M', u'ι'),
    (0x1D6CB, 'M', u'κ'),
    (0x1D6CC, 'M', u'λ'),
    (0x1D6CD, 'M', u'μ'),
    (0x1D6CE, 'M', u'ν'),
    (0x1D6CF, 'M', u'ξ'),
    (0x1D6D0, 'M', u'ο'),
    (0x1D6D1, 'M', u'π'),
    (0x1D6D2, 'M', u'ρ'),
    (0x1D6D3, 'M', u'σ'),
    (0x1D6D5, 'M', u'τ'),
    (0x1D6D6, 'M', u'υ'),
    (0x1D6D7, 'M', u'φ'),
    (0x1D6D8, 'M', u'χ'),
    (0x1D6D9, 'M', u'ψ'),
    (0x1D6DA, 'M', u'ω'),
    (0x1D6DB, 'M', u'∂'),
    (0x1D6DC, 'M', u'ε'),
    (0x1D6DD, 'M', u'θ'),
    (0x1D6DE, 'M', u'κ'),
    (0x1D6DF, 'M', u'φ'),
    (0x1D6E0, 'M', u'ρ'),
    (0x1D6E1, 'M', u'π'),
    (0x1D6E2, 'M', u'α'),
    (0x1D6E3, 'M', u'β'),
    (0x1D6E4, 'M', u'γ'),
    (0x1D6E5, 'M', u'δ'),
    (0x1D6E6, 'M', u'ε'),
    (0x1D6E7, 'M', u'ζ'),
    (0x1D6E8, 'M', u'η'),
    (0x1D6E9, 'M', u'θ'),
    (0x1D6EA, 'M', u'ι'),
    (0x1D6EB, 'M', u'κ'),
    (0x1D6EC, 'M', u'λ'),
    (0x1D6ED, 'M', u'μ'),
    (0x1D6EE, 'M', u'ν'),
    (0x1D6EF, 'M', u'ξ'),
    (0x1D6F0, 'M', u'ο'),
    (0x1D6F1, 'M', u'π'),
    (0x1D6F2, 'M', u'ρ'),
    (0x1D6F3, 'M', u'θ'),
    (0x1D6F4, 'M', u'σ'),
    (0x1D6F5, 'M', u'τ'),
    (0x1D6F6, 'M', u'υ'),
    (0x1D6F7, 'M', u'φ'),
    (0x1D6F8, 'M', u'χ'),
    (0x1D6F9, 'M', u'ψ'),
    (0x1D6FA, 'M', u'ω'),
    (0x1D6FB, 'M', u'∇'),
    (0x1D6FC, 'M', u'α'),
    (0x1D6FD, 'M', u'β'),
    (0x1D6FE, 'M', u'γ'),
    (0x1D6FF, 'M', u'δ'),
    (0x1D700, 'M', u'ε'),
    (0x1D701, 'M', u'ζ'),
    (0x1D702, 'M', u'η'),
    (0x1D703, 'M', u'θ'),
    (0x1D704, 'M', u'ι'),
    (0x1D705, 'M', u'κ'),
    (0x1D706, 'M', u'λ'),
    (0x1D707, 'M', u'μ'),
    (0x1D708, 'M', u'ν'),
    (0x1D709, 'M', u'ξ'),
    (0x1D70A, 'M', u'ο'),
    (0x1D70B, 'M', u'π'),
    (0x1D70C, 'M', u'ρ'),
    (0x1D70D, 'M', u'σ'),
    (0x1D70F, 'M', u'τ'),
    (0x1D710, 'M', u'υ'),
    (0x1D711, 'M', u'φ'),
    (0x1D712, 'M', u'χ'),
    (0x1D713, 'M', u'ψ'),
    (0x1D714, 'M', u'ω'),
    (0x1D715, 'M', u'∂'),
    (0x1D716, 'M', u'ε'),
    ]

def _seg_67():
    return [
    (0x1D717, 'M', u'θ'),
    (0x1D718, 'M', u'κ'),
    (0x1D719, 'M', u'φ'),
    (0x1D71A, 'M', u'ρ'),
    (0x1D71B, 'M', u'π'),
    (0x1D71C, 'M', u'α'),
    (0x1D71D, 'M', u'β'),
    (0x1D71E, 'M', u'γ'),
    (0x1D71F, 'M', u'δ'),
    (0x1D720, 'M', u'ε'),
    (0x1D721, 'M', u'ζ'),
    (0x1D722, 'M', u'η'),
    (0x1D723, 'M', u'θ'),
    (0x1D724, 'M', u'ι'),
    (0x1D725, 'M', u'κ'),
    (0x1D726, 'M', u'λ'),
    (0x1D727, 'M', u'μ'),
    (0x1D728, 'M', u'ν'),
    (0x1D729, 'M', u'ξ'),
    (0x1D72A, 'M', u'ο'),
    (0x1D72B, 'M', u'π'),
    (0x1D72C, 'M', u'ρ'),
    (0x1D72D, 'M', u'θ'),
    (0x1D72E, 'M', u'σ'),
    (0x1D72F, 'M', u'τ'),
    (0x1D730, 'M', u'υ'),
    (0x1D731, 'M', u'φ'),
    (0x1D732, 'M', u'χ'),
    (0x1D733, 'M', u'ψ'),
    (0x1D734, 'M', u'ω'),
    (0x1D735, 'M', u'∇'),
    (0x1D736, 'M', u'α'),
    (0x1D737, 'M', u'β'),
    (0x1D738, 'M', u'γ'),
    (0x1D739, 'M', u'δ'),
    (0x1D73A, 'M', u'ε'),
    (0x1D73B, 'M', u'ζ'),
    (0x1D73C, 'M', u'η'),
    (0x1D73D, 'M', u'θ'),
    (0x1D73E, 'M', u'ι'),
    (0x1D73F, 'M', u'κ'),
    (0x1D740, 'M', u'λ'),
    (0x1D741, 'M', u'μ'),
    (0x1D742, 'M', u'ν'),
    (0x1D743, 'M', u'ξ'),
    (0x1D744, 'M', u'ο'),
    (0x1D745, 'M', u'π'),
    (0x1D746, 'M', u'ρ'),
    (0x1D747, 'M', u'σ'),
    (0x1D749, 'M', u'τ'),
    (0x1D74A, 'M', u'υ'),
    (0x1D74B, 'M', u'φ'),
    (0x1D74C, 'M', u'χ'),
    (0x1D74D, 'M', u'ψ'),
    (0x1D74E, 'M', u'ω'),
    (0x1D74F, 'M', u'∂'),
    (0x1D750, 'M', u'ε'),
    (0x1D751, 'M', u'θ'),
    (0x1D752, 'M', u'κ'),
    (0x1D753, 'M', u'φ'),
    (0x1D754, 'M', u'ρ'),
    (0x1D755, 'M', u'π'),
    (0x1D756, 'M', u'α'),
    (0x1D757, 'M', u'β'),
    (0x1D758, 'M', u'γ'),
    (0x1D759, 'M', u'δ'),
    (0x1D75A, 'M', u'ε'),
    (0x1D75B, 'M', u'ζ'),
    (0x1D75C, 'M', u'η'),
    (0x1D75D, 'M', u'θ'),
    (0x1D75E, 'M', u'ι'),
    (0x1D75F, 'M', u'κ'),
    (0x1D760, 'M', u'λ'),
    (0x1D761, 'M', u'μ'),
    (0x1D762, 'M', u'ν'),
    (0x1D763, 'M', u'ξ'),
    (0x1D764, 'M', u'ο'),
    (0x1D765, 'M', u'π'),
    (0x1D766, 'M', u'ρ'),
    (0x1D767, 'M', u'θ'),
    (0x1D768, 'M', u'σ'),
    (0x1D769, 'M', u'τ'),
    (0x1D76A, 'M', u'υ'),
    (0x1D76B, 'M', u'φ'),
    (0x1D76C, 'M', u'χ'),
    (0x1D76D, 'M', u'ψ'),
    (0x1D76E, 'M', u'ω'),
    (0x1D76F, 'M', u'∇'),
    (0x1D770, 'M', u'α'),
    (0x1D771, 'M', u'β'),
    (0x1D772, 'M', u'γ'),
    (0x1D773, 'M', u'δ'),
    (0x1D774, 'M', u'ε'),
    (0x1D775, 'M', u'ζ'),
    (0x1D776, 'M', u'η'),
    (0x1D777, 'M', u'θ'),
    (0x1D778, 'M', u'ι'),
    (0x1D779, 'M', u'κ'),
    (0x1D77A, 'M', u'λ'),
    (0x1D77B, 'M', u'μ'),
    ]

def _seg_68():
    return [
    (0x1D77C, 'M', u'ν'),
    (0x1D77D, 'M', u'ξ'),
    (0x1D77E, 'M', u'ο'),
    (0x1D77F, 'M', u'π'),
    (0x1D780, 'M', u'ρ'),
    (0x1D781, 'M', u'σ'),
    (0x1D783, 'M', u'τ'),
    (0x1D784, 'M', u'υ'),
    (0x1D785, 'M', u'φ'),
    (0x1D786, 'M', u'χ'),
    (0x1D787, 'M', u'ψ'),
    (0x1D788, 'M', u'ω'),
    (0x1D789, 'M', u'∂'),
    (0x1D78A, 'M', u'ε'),
    (0x1D78B, 'M', u'θ'),
    (0x1D78C, 'M', u'κ'),
    (0x1D78D, 'M', u'φ'),
    (0x1D78E, 'M', u'ρ'),
    (0x1D78F, 'M', u'π'),
    (0x1D790, 'M', u'α'),
    (0x1D791, 'M', u'β'),
    (0x1D792, 'M', u'γ'),
    (0x1D793, 'M', u'δ'),
    (0x1D794, 'M', u'ε'),
    (0x1D795, 'M', u'ζ'),
    (0x1D796, 'M', u'η'),
    (0x1D797, 'M', u'θ'),
    (0x1D798, 'M', u'ι'),
    (0x1D799, 'M', u'κ'),
    (0x1D79A, 'M', u'λ'),
    (0x1D79B, 'M', u'μ'),
    (0x1D79C, 'M', u'ν'),
    (0x1D79D, 'M', u'ξ'),
    (0x1D79E, 'M', u'ο'),
    (0x1D79F, 'M', u'π'),
    (0x1D7A0, 'M', u'ρ'),
    (0x1D7A1, 'M', u'θ'),
    (0x1D7A2, 'M', u'σ'),
    (0x1D7A3, 'M', u'τ'),
    (0x1D7A4, 'M', u'υ'),
    (0x1D7A5, 'M', u'φ'),
    (0x1D7A6, 'M', u'χ'),
    (0x1D7A7, 'M', u'ψ'),
    (0x1D7A8, 'M', u'ω'),
    (0x1D7A9, 'M', u'∇'),
    (0x1D7AA, 'M', u'α'),
    (0x1D7AB, 'M', u'β'),
    (0x1D7AC, 'M', u'γ'),
    (0x1D7AD, 'M', u'δ'),
    (0x1D7AE, 'M', u'ε'),
    (0x1D7AF, 'M', u'ζ'),
    (0x1D7B0, 'M', u'η'),
    (0x1D7B1, 'M', u'θ'),
    (0x1D7B2, 'M', u'ι'),
    (0x1D7B3, 'M', u'κ'),
    (0x1D7B4, 'M', u'λ'),
    (0x1D7B5, 'M', u'μ'),
    (0x1D7B6, 'M', u'ν'),
    (0x1D7B7, 'M', u'ξ'),
    (0x1D7B8, 'M', u'ο'),
    (0x1D7B9, 'M', u'π'),
    (0x1D7BA, 'M', u'ρ'),
    (0x1D7BB, 'M', u'σ'),
    (0x1D7BD, 'M', u'τ'),
    (0x1D7BE, 'M', u'υ'),
    (0x1D7BF, 'M', u'φ'),
    (0x1D7C0, 'M', u'χ'),
    (0x1D7C1, 'M', u'ψ'),
    (0x1D7C2, 'M', u'ω'),
    (0x1D7C3, 'M', u'∂'),
    (0x1D7C4, 'M', u'ε'),
    (0x1D7C5, 'M', u'θ'),
    (0x1D7C6, 'M', u'κ'),
    (0x1D7C7, 'M', u'φ'),
    (0x1D7C8, 'M', u'ρ'),
    (0x1D7C9, 'M', u'π'),
    (0x1D7CA, 'M', u'ϝ'),
    (0x1D7CC, 'X'),
    (0x1D7CE, 'M', u'0'),
    (0x1D7CF, 'M', u'1'),
    (0x1D7D0, 'M', u'2'),
    (0x1D7D1, 'M', u'3'),
    (0x1D7D2, 'M', u'4'),
    (0x1D7D3, 'M', u'5'),
    (0x1D7D4, 'M', u'6'),
    (0x1D7D5, 'M', u'7'),
    (0x1D7D6, 'M', u'8'),
    (0x1D7D7, 'M', u'9'),
    (0x1D7D8, 'M', u'0'),
    (0x1D7D9, 'M', u'1'),
    (0x1D7DA, 'M', u'2'),
    (0x1D7DB, 'M', u'3'),
    (0x1D7DC, 'M', u'4'),
    (0x1D7DD, 'M', u'5'),
    (0x1D7DE, 'M', u'6'),
    (0x1D7DF, 'M', u'7'),
    (0x1D7E0, 'M', u'8'),
    (0x1D7E1, 'M', u'9'),
    (0x1D7E2, 'M', u'0'),
    (0x1D7E3, 'M', u'1'),
    ]

def _seg_69():
    return [
    (0x1D7E4, 'M', u'2'),
    (0x1D7E5, 'M', u'3'),
    (0x1D7E6, 'M', u'4'),
    (0x1D7E7, 'M', u'5'),
    (0x1D7E8, 'M', u'6'),
    (0x1D7E9, 'M', u'7'),
    (0x1D7EA, 'M', u'8'),
    (0x1D7EB, 'M', u'9'),
    (0x1D7EC, 'M', u'0'),
    (0x1D7ED, 'M', u'1'),
    (0x1D7EE, 'M', u'2'),
    (0x1D7EF, 'M', u'3'),
    (0x1D7F0, 'M', u'4'),
    (0x1D7F1, 'M', u'5'),
    (0x1D7F2, 'M', u'6'),
    (0x1D7F3, 'M', u'7'),
    (0x1D7F4, 'M', u'8'),
    (0x1D7F5, 'M', u'9'),
    (0x1D7F6, 'M', u'0'),
    (0x1D7F7, 'M', u'1'),
    (0x1D7F8, 'M', u'2'),
    (0x1D7F9, 'M', u'3'),
    (0x1D7FA, 'M', u'4'),
    (0x1D7FB, 'M', u'5'),
    (0x1D7FC, 'M', u'6'),
    (0x1D7FD, 'M', u'7'),
    (0x1D7FE, 'M', u'8'),
    (0x1D7FF, 'M', u'9'),
    (0x1D800, 'V'),
    (0x1DA8C, 'X'),
    (0x1DA9B, 'V'),
    (0x1DAA0, 'X'),
    (0x1DAA1, 'V'),
    (0x1DAB0, 'X'),
    (0x1E000, 'V'),
    (0x1E007, 'X'),
    (0x1E008, 'V'),
    (0x1E019, 'X'),
    (0x1E01B, 'V'),
    (0x1E022, 'X'),
    (0x1E023, 'V'),
    (0x1E025, 'X'),
    (0x1E026, 'V'),
    (0x1E02B, 'X'),
    (0x1E100, 'V'),
    (0x1E12D, 'X'),
    (0x1E130, 'V'),
    (0x1E13E, 'X'),
    (0x1E140, 'V'),
    (0x1E14A, 'X'),
    (0x1E14E, 'V'),
    (0x1E150, 'X'),
    (0x1E2C0, 'V'),
    (0x1E2FA, 'X'),
    (0x1E2FF, 'V'),
    (0x1E300, 'X'),
    (0x1E800, 'V'),
    (0x1E8C5, 'X'),
    (0x1E8C7, 'V'),
    (0x1E8D7, 'X'),
    (0x1E900, 'M', u'𞤢'),
    (0x1E901, 'M', u'𞤣'),
    (0x1E902, 'M', u'𞤤'),
    (0x1E903, 'M', u'𞤥'),
    (0x1E904, 'M', u'𞤦'),
    (0x1E905, 'M', u'𞤧'),
    (0x1E906, 'M', u'𞤨'),
    (0x1E907, 'M', u'𞤩'),
    (0x1E908, 'M', u'𞤪'),
    (0x1E909, 'M', u'𞤫'),
    (0x1E90A, 'M', u'𞤬'),
    (0x1E90B, 'M', u'𞤭'),
    (0x1E90C, 'M', u'𞤮'),
    (0x1E90D, 'M', u'𞤯'),
    (0x1E90E, 'M', u'𞤰'),
    (0x1E90F, 'M', u'𞤱'),
    (0x1E910, 'M', u'𞤲'),
    (0x1E911, 'M', u'𞤳'),
    (0x1E912, 'M', u'𞤴'),
    (0x1E913, 'M', u'𞤵'),
    (0x1E914, 'M', u'𞤶'),
    (0x1E915, 'M', u'𞤷'),
    (0x1E916, 'M', u'𞤸'),
    (0x1E917, 'M', u'𞤹'),
    (0x1E918, 'M', u'𞤺'),
    (0x1E919, 'M', u'𞤻'),
    (0x1E91A, 'M', u'𞤼'),
    (0x1E91B, 'M', u'𞤽'),
    (0x1E91C, 'M', u'𞤾'),
    (0x1E91D, 'M', u'𞤿'),
    (0x1E91E, 'M', u'𞥀'),
    (0x1E91F, 'M', u'𞥁'),
    (0x1E920, 'M', u'𞥂'),
    (0x1E921, 'M', u'𞥃'),
    (0x1E922, 'V'),
    (0x1E94C, 'X'),
    (0x1E950, 'V'),
    (0x1E95A, 'X'),
    (0x1E95E, 'V'),
    (0x1E960, 'X'),
    ]

def _seg_70():
    return [
    (0x1EC71, 'V'),
    (0x1ECB5, 'X'),
    (0x1ED01, 'V'),
    (0x1ED3E, 'X'),
    (0x1EE00, 'M', u'ا'),
    (0x1EE01, 'M', u'ب'),
    (0x1EE02, 'M', u'ج'),
    (0x1EE03, 'M', u'د'),
    (0x1EE04, 'X'),
    (0x1EE05, 'M', u'و'),
    (0x1EE06, 'M', u'ز'),
    (0x1EE07, 'M', u'ح'),
    (0x1EE08, 'M', u'ط'),
    (0x1EE09, 'M', u'ي'),
    (0x1EE0A, 'M', u'ك'),
    (0x1EE0B, 'M', u'ل'),
    (0x1EE0C, 'M', u'م'),
    (0x1EE0D, 'M', u'ن'),
    (0x1EE0E, 'M', u'س'),
    (0x1EE0F, 'M', u'ع'),
    (0x1EE10, 'M', u'ف'),
    (0x1EE11, 'M', u'ص'),
    (0x1EE12, 'M', u'ق'),
    (0x1EE13, 'M', u'ر'),
    (0x1EE14, 'M', u'ش'),
    (0x1EE15, 'M', u'ت'),
    (0x1EE16, 'M', u'ث'),
    (0x1EE17, 'M', u'خ'),
    (0x1EE18, 'M', u'ذ'),
    (0x1EE19, 'M', u'ض'),
    (0x1EE1A, 'M', u'ظ'),
    (0x1EE1B, 'M', u'غ'),
    (0x1EE1C, 'M', u'ٮ'),
    (0x1EE1D, 'M', u'ں'),
    (0x1EE1E, 'M', u'ڡ'),
    (0x1EE1F, 'M', u'ٯ'),
    (0x1EE20, 'X'),
    (0x1EE21, 'M', u'ب'),
    (0x1EE22, 'M', u'ج'),
    (0x1EE23, 'X'),
    (0x1EE24, 'M', u'ه'),
    (0x1EE25, 'X'),
    (0x1EE27, 'M', u'ح'),
    (0x1EE28, 'X'),
    (0x1EE29, 'M', u'ي'),
    (0x1EE2A, 'M', u'ك'),
    (0x1EE2B, 'M', u'ل'),
    (0x1EE2C, 'M', u'م'),
    (0x1EE2D, 'M', u'ن'),
    (0x1EE2E, 'M', u'س'),
    (0x1EE2F, 'M', u'ع'),
    (0x1EE30, 'M', u'ف'),
    (0x1EE31, 'M', u'ص'),
    (0x1EE32, 'M', u'ق'),
    (0x1EE33, 'X'),
    (0x1EE34, 'M', u'ش'),
    (0x1EE35, 'M', u'ت'),
    (0x1EE36, 'M', u'ث'),
    (0x1EE37, 'M', u'خ'),
    (0x1EE38, 'X'),
    (0x1EE39, 'M', u'ض'),
    (0x1EE3A, 'X'),
    (0x1EE3B, 'M', u'غ'),
    (0x1EE3C, 'X'),
    (0x1EE42, 'M', u'ج'),
    (0x1EE43, 'X'),
    (0x1EE47, 'M', u'ح'),
    (0x1EE48, 'X'),
    (0x1EE49, 'M', u'ي'),
    (0x1EE4A, 'X'),
    (0x1EE4B, 'M', u'ل'),
    (0x1EE4C, 'X'),
    (0x1EE4D, 'M', u'ن'),
    (0x1EE4E, 'M', u'س'),
    (0x1EE4F, 'M', u'ع'),
    (0x1EE50, 'X'),
    (0x1EE51, 'M', u'ص'),
    (0x1EE52, 'M', u'ق'),
    (0x1EE53, 'X'),
    (0x1EE54, 'M', u'ش'),
    (0x1EE55, 'X'),
    (0x1EE57, 'M', u'خ'),
    (0x1EE58, 'X'),
    (0x1EE59, 'M', u'ض'),
    (0x1EE5A, 'X'),
    (0x1EE5B, 'M', u'غ'),
    (0x1EE5C, 'X'),
    (0x1EE5D, 'M', u'ں'),
    (0x1EE5E, 'X'),
    (0x1EE5F, 'M', u'ٯ'),
    (0x1EE60, 'X'),
    (0x1EE61, 'M', u'ب'),
    (0x1EE62, 'M', u'ج'),
    (0x1EE63, 'X'),
    (0x1EE64, 'M', u'ه'),
    (0x1EE65, 'X'),
    (0x1EE67, 'M', u'ح'),
    (0x1EE68, 'M', u'ط'),
    (0x1EE69, 'M', u'ي'),
    (0x1EE6A, 'M', u'ك'),
    ]

def _seg_71():
    return [
    (0x1EE6B, 'X'),
    (0x1EE6C, 'M', u'م'),
    (0x1EE6D, 'M', u'ن'),
    (0x1EE6E, 'M', u'س'),
    (0x1EE6F, 'M', u'ع'),
    (0x1EE70, 'M', u'ف'),
    (0x1EE71, 'M', u'ص'),
    (0x1EE72, 'M', u'ق'),
    (0x1EE73, 'X'),
    (0x1EE74, 'M', u'ش'),
    (0x1EE75, 'M', u'ت'),
    (0x1EE76, 'M', u'ث'),
    (0x1EE77, 'M', u'خ'),
    (0x1EE78, 'X'),
    (0x1EE79, 'M', u'ض'),
    (0x1EE7A, 'M', u'ظ'),
    (0x1EE7B, 'M', u'غ'),
    (0x1EE7C, 'M', u'ٮ'),
    (0x1EE7D, 'X'),
    (0x1EE7E, 'M', u'ڡ'),
    (0x1EE7F, 'X'),
    (0x1EE80, 'M', u'ا'),
    (0x1EE81, 'M', u'ب'),
    (0x1EE82, 'M', u'ج'),
    (0x1EE83, 'M', u'د'),
    (0x1EE84, 'M', u'ه'),
    (0x1EE85, 'M', u'و'),
    (0x1EE86, 'M', u'ز'),
    (0x1EE87, 'M', u'ح'),
    (0x1EE88, 'M', u'ط'),
    (0x1EE89, 'M', u'ي'),
    (0x1EE8A, 'X'),
    (0x1EE8B, 'M', u'ل'),
    (0x1EE8C, 'M', u'م'),
    (0x1EE8D, 'M', u'ن'),
    (0x1EE8E, 'M', u'س'),
    (0x1EE8F, 'M', u'ع'),
    (0x1EE90, 'M', u'ف'),
    (0x1EE91, 'M', u'ص'),
    (0x1EE92, 'M', u'ق'),
    (0x1EE93, 'M', u'ر'),
    (0x1EE94, 'M', u'ش'),
    (0x1EE95, 'M', u'ت'),
    (0x1EE96, 'M', u'ث'),
    (0x1EE97, 'M', u'خ'),
    (0x1EE98, 'M', u'ذ'),
    (0x1EE99, 'M', u'ض'),
    (0x1EE9A, 'M', u'ظ'),
    (0x1EE9B, 'M', u'غ'),
    (0x1EE9C, 'X'),
    (0x1EEA1, 'M', u'ب'),
    (0x1EEA2, 'M', u'ج'),
    (0x1EEA3, 'M', u'د'),
    (0x1EEA4, 'X'),
    (0x1EEA5, 'M', u'و'),
    (0x1EEA6, 'M', u'ز'),
    (0x1EEA7, 'M', u'ح'),
    (0x1EEA8, 'M', u'ط'),
    (0x1EEA9, 'M', u'ي'),
    (0x1EEAA, 'X'),
    (0x1EEAB, 'M', u'ل'),
    (0x1EEAC, 'M', u'م'),
    (0x1EEAD, 'M', u'ن'),
    (0x1EEAE, 'M', u'س'),
    (0x1EEAF, 'M', u'ع'),
    (0x1EEB0, 'M', u'ف'),
    (0x1EEB1, 'M', u'ص'),
    (0x1EEB2, 'M', u'ق'),
    (0x1EEB3, 'M', u'ر'),
    (0x1EEB4, 'M', u'ش'),
    (0x1EEB5, 'M', u'ت'),
    (0x1EEB6, 'M', u'ث'),
    (0x1EEB7, 'M', u'خ'),
    (0x1EEB8, 'M', u'ذ'),
    (0x1EEB9, 'M', u'ض'),
    (0x1EEBA, 'M', u'ظ'),
    (0x1EEBB, 'M', u'غ'),
    (0x1EEBC, 'X'),
    (0x1EEF0, 'V'),
    (0x1EEF2, 'X'),
    (0x1F000, 'V'),
    (0x1F02C, 'X'),
    (0x1F030, 'V'),
    (0x1F094, 'X'),
    (0x1F0A0, 'V'),
    (0x1F0AF, 'X'),
    (0x1F0B1, 'V'),
    (0x1F0C0, 'X'),
    (0x1F0C1, 'V'),
    (0x1F0D0, 'X'),
    (0x1F0D1, 'V'),
    (0x1F0F6, 'X'),
    (0x1F101, '3', u'0,'),
    (0x1F102, '3', u'1,'),
    (0x1F103, '3', u'2,'),
    (0x1F104, '3', u'3,'),
    (0x1F105, '3', u'4,'),
    (0x1F106, '3', u'5,'),
    (0x1F107, '3', u'6,'),
    (0x1F108, '3', u'7,'),
    ]

def _seg_72():
    return [
    (0x1F109, '3', u'8,'),
    (0x1F10A, '3', u'9,'),
    (0x1F10B, 'V'),
    (0x1F110, '3', u'(a)'),
    (0x1F111, '3', u'(b)'),
    (0x1F112, '3', u'(c)'),
    (0x1F113, '3', u'(d)'),
    (0x1F114, '3', u'(e)'),
    (0x1F115, '3', u'(f)'),
    (0x1F116, '3', u'(g)'),
    (0x1F117, '3', u'(h)'),
    (0x1F118, '3', u'(i)'),
    (0x1F119, '3', u'(j)'),
    (0x1F11A, '3', u'(k)'),
    (0x1F11B, '3', u'(l)'),
    (0x1F11C, '3', u'(m)'),
    (0x1F11D, '3', u'(n)'),
    (0x1F11E, '3', u'(o)'),
    (0x1F11F, '3', u'(p)'),
    (0x1F120, '3', u'(q)'),
    (0x1F121, '3', u'(r)'),
    (0x1F122, '3', u'(s)'),
    (0x1F123, '3', u'(t)'),
    (0x1F124, '3', u'(u)'),
    (0x1F125, '3', u'(v)'),
    (0x1F126, '3', u'(w)'),
    (0x1F127, '3', u'(x)'),
    (0x1F128, '3', u'(y)'),
    (0x1F129, '3', u'(z)'),
    (0x1F12A, 'M', u'〔s〕'),
    (0x1F12B, 'M', u'c'),
    (0x1F12C, 'M', u'r'),
    (0x1F12D, 'M', u'cd'),
    (0x1F12E, 'M', u'wz'),
    (0x1F12F, 'V'),
    (0x1F130, 'M', u'a'),
    (0x1F131, 'M', u'b'),
    (0x1F132, 'M', u'c'),
    (0x1F133, 'M', u'd'),
    (0x1F134, 'M', u'e'),
    (0x1F135, 'M', u'f'),
    (0x1F136, 'M', u'g'),
    (0x1F137, 'M', u'h'),
    (0x1F138, 'M', u'i'),
    (0x1F139, 'M', u'j'),
    (0x1F13A, 'M', u'k'),
    (0x1F13B, 'M', u'l'),
    (0x1F13C, 'M', u'm'),
    (0x1F13D, 'M', u'n'),
    (0x1F13E, 'M', u'o'),
    (0x1F13F, 'M', u'p'),
    (0x1F140, 'M', u'q'),
    (0x1F141, 'M', u'r'),
    (0x1F142, 'M', u's'),
    (0x1F143, 'M', u't'),
    (0x1F144, 'M', u'u'),
    (0x1F145, 'M', u'v'),
    (0x1F146, 'M', u'w'),
    (0x1F147, 'M', u'x'),
    (0x1F148, 'M', u'y'),
    (0x1F149, 'M', u'z'),
    (0x1F14A, 'M', u'hv'),
    (0x1F14B, 'M', u'mv'),
    (0x1F14C, 'M', u'sd'),
    (0x1F14D, 'M', u'ss'),
    (0x1F14E, 'M', u'ppv'),
    (0x1F14F, 'M', u'wc'),
    (0x1F150, 'V'),
    (0x1F16A, 'M', u'mc'),
    (0x1F16B, 'M', u'md'),
    (0x1F16C, 'M', u'mr'),
    (0x1F16D, 'V'),
    (0x1F190, 'M', u'dj'),
    (0x1F191, 'V'),
    (0x1F1AE, 'X'),
    (0x1F1E6, 'V'),
    (0x1F200, 'M', u'ほか'),
    (0x1F201, 'M', u'ココ'),
    (0x1F202, 'M', u'サ'),
    (0x1F203, 'X'),
    (0x1F210, 'M', u'手'),
    (0x1F211, 'M', u'字'),
    (0x1F212, 'M', u'双'),
    (0x1F213, 'M', u'デ'),
    (0x1F214, 'M', u'二'),
    (0x1F215, 'M', u'多'),
    (0x1F216, 'M', u'解'),
    (0x1F217, 'M', u'天'),
    (0x1F218, 'M', u'交'),
    (0x1F219, 'M', u'映'),
    (0x1F21A, 'M', u'無'),
    (0x1F21B, 'M', u'料'),
    (0x1F21C, 'M', u'前'),
    (0x1F21D, 'M', u'後'),
    (0x1F21E, 'M', u'再'),
    (0x1F21F, 'M', u'新'),
    (0x1F220, 'M', u'初'),
    (0x1F221, 'M', u'終'),
    (0x1F222, 'M', u'生'),
    (0x1F223, 'M', u'販'),
    ]

def _seg_73():
    return [
    (0x1F224, 'M', u'声'),
    (0x1F225, 'M', u'吹'),
    (0x1F226, 'M', u'演'),
    (0x1F227, 'M', u'投'),
    (0x1F228, 'M', u'捕'),
    (0x1F229, 'M', u'一'),
    (0x1F22A, 'M', u'三'),
    (0x1F22B, 'M', u'遊'),
    (0x1F22C, 'M', u'左'),
    (0x1F22D, 'M', u'中'),
    (0x1F22E, 'M', u'右'),
    (0x1F22F, 'M', u'指'),
    (0x1F230, 'M', u'走'),
    (0x1F231, 'M', u'打'),
    (0x1F232, 'M', u'禁'),
    (0x1F233, 'M', u'空'),
    (0x1F234, 'M', u'合'),
    (0x1F235, 'M', u'満'),
    (0x1F236, 'M', u'有'),
    (0x1F237, 'M', u'月'),
    (0x1F238, 'M', u'申'),
    (0x1F239, 'M', u'割'),
    (0x1F23A, 'M', u'営'),
    (0x1F23B, 'M', u'配'),
    (0x1F23C, 'X'),
    (0x1F240, 'M', u'〔本〕'),
    (0x1F241, 'M', u'〔三〕'),
    (0x1F242, 'M', u'〔二〕'),
    (0x1F243, 'M', u'〔安〕'),
    (0x1F244, 'M', u'〔点〕'),
    (0x1F245, 'M', u'〔打〕'),
    (0x1F246, 'M', u'〔盗〕'),
    (0x1F247, 'M', u'〔勝〕'),
    (0x1F248, 'M', u'〔敗〕'),
    (0x1F249, 'X'),
    (0x1F250, 'M', u'得'),
    (0x1F251, 'M', u'可'),
    (0x1F252, 'X'),
    (0x1F260, 'V'),
    (0x1F266, 'X'),
    (0x1F300, 'V'),
    (0x1F6D8, 'X'),
    (0x1F6E0, 'V'),
    (0x1F6ED, 'X'),
    (0x1F6F0, 'V'),
    (0x1F6FD, 'X'),
    (0x1F700, 'V'),
    (0x1F774, 'X'),
    (0x1F780, 'V'),
    (0x1F7D9, 'X'),
    (0x1F7E0, 'V'),
    (0x1F7EC, 'X'),
    (0x1F800, 'V'),
    (0x1F80C, 'X'),
    (0x1F810, 'V'),
    (0x1F848, 'X'),
    (0x1F850, 'V'),
    (0x1F85A, 'X'),
    (0x1F860, 'V'),
    (0x1F888, 'X'),
    (0x1F890, 'V'),
    (0x1F8AE, 'X'),
    (0x1F8B0, 'V'),
    (0x1F8B2, 'X'),
    (0x1F900, 'V'),
    (0x1F979, 'X'),
    (0x1F97A, 'V'),
    (0x1F9CC, 'X'),
    (0x1F9CD, 'V'),
    (0x1FA54, 'X'),
    (0x1FA60, 'V'),
    (0x1FA6E, 'X'),
    (0x1FA70, 'V'),
    (0x1FA75, 'X'),
    (0x1FA78, 'V'),
    (0x1FA7B, 'X'),
    (0x1FA80, 'V'),
    (0x1FA87, 'X'),
    (0x1FA90, 'V'),
    (0x1FAA9, 'X'),
    (0x1FAB0, 'V'),
    (0x1FAB7, 'X'),
    (0x1FAC0, 'V'),
    (0x1FAC3, 'X'),
    (0x1FAD0, 'V'),
    (0x1FAD7, 'X'),
    (0x1FB00, 'V'),
    (0x1FB93, 'X'),
    (0x1FB94, 'V'),
    (0x1FBCB, 'X'),
    (0x1FBF0, 'M', u'0'),
    (0x1FBF1, 'M', u'1'),
    (0x1FBF2, 'M', u'2'),
    (0x1FBF3, 'M', u'3'),
    (0x1FBF4, 'M', u'4'),
    (0x1FBF5, 'M', u'5'),
    (0x1FBF6, 'M', u'6'),
    (0x1FBF7, 'M', u'7'),
    (0x1FBF8, 'M', u'8'),
    (0x1FBF9, 'M', u'9'),
    ]

def _seg_74():
    return [
    (0x1FBFA, 'X'),
    (0x20000, 'V'),
    (0x2A6DE, 'X'),
    (0x2A700, 'V'),
    (0x2B735, 'X'),
    (0x2B740, 'V'),
    (0x2B81E, 'X'),
    (0x2B820, 'V'),
    (0x2CEA2, 'X'),
    (0x2CEB0, 'V'),
    (0x2EBE1, 'X'),
    (0x2F800, 'M', u'丽'),
    (0x2F801, 'M', u'丸'),
    (0x2F802, 'M', u'乁'),
    (0x2F803, 'M', u'𠄢'),
    (0x2F804, 'M', u'你'),
    (0x2F805, 'M', u'侮'),
    (0x2F806, 'M', u'侻'),
    (0x2F807, 'M', u'倂'),
    (0x2F808, 'M', u'偺'),
    (0x2F809, 'M', u'備'),
    (0x2F80A, 'M', u'僧'),
    (0x2F80B, 'M', u'像'),
    (0x2F80C, 'M', u'㒞'),
    (0x2F80D, 'M', u'𠘺'),
    (0x2F80E, 'M', u'免'),
    (0x2F80F, 'M', u'兔'),
    (0x2F810, 'M', u'兤'),
    (0x2F811, 'M', u'具'),
    (0x2F812, 'M', u'𠔜'),
    (0x2F813, 'M', u'㒹'),
    (0x2F814, 'M', u'內'),
    (0x2F815, 'M', u'再'),
    (0x2F816, 'M', u'𠕋'),
    (0x2F817, 'M', u'冗'),
    (0x2F818, 'M', u'冤'),
    (0x2F819, 'M', u'仌'),
    (0x2F81A, 'M', u'冬'),
    (0x2F81B, 'M', u'况'),
    (0x2F81C, 'M', u'𩇟'),
    (0x2F81D, 'M', u'凵'),
    (0x2F81E, 'M', u'刃'),
    (0x2F81F, 'M', u'㓟'),
    (0x2F820, 'M', u'刻'),
    (0x2F821, 'M', u'剆'),
    (0x2F822, 'M', u'割'),
    (0x2F823, 'M', u'剷'),
    (0x2F824, 'M', u'㔕'),
    (0x2F825, 'M', u'勇'),
    (0x2F826, 'M', u'勉'),
    (0x2F827, 'M', u'勤'),
    (0x2F828, 'M', u'勺'),
    (0x2F829, 'M', u'包'),
    (0x2F82A, 'M', u'匆'),
    (0x2F82B, 'M', u'北'),
    (0x2F82C, 'M', u'卉'),
    (0x2F82D, 'M', u'卑'),
    (0x2F82E, 'M', u'博'),
    (0x2F82F, 'M', u'即'),
    (0x2F830, 'M', u'卽'),
    (0x2F831, 'M', u'卿'),
    (0x2F834, 'M', u'𠨬'),
    (0x2F835, 'M', u'灰'),
    (0x2F836, 'M', u'及'),
    (0x2F837, 'M', u'叟'),
    (0x2F838, 'M', u'𠭣'),
    (0x2F839, 'M', u'叫'),
    (0x2F83A, 'M', u'叱'),
    (0x2F83B, 'M', u'吆'),
    (0x2F83C, 'M', u'咞'),
    (0x2F83D, 'M', u'吸'),
    (0x2F83E, 'M', u'呈'),
    (0x2F83F, 'M', u'周'),
    (0x2F840, 'M', u'咢'),
    (0x2F841, 'M', u'哶'),
    (0x2F842, 'M', u'唐'),
    (0x2F843, 'M', u'啓'),
    (0x2F844, 'M', u'啣'),
    (0x2F845, 'M', u'善'),
    (0x2F847, 'M', u'喙'),
    (0x2F848, 'M', u'喫'),
    (0x2F849, 'M', u'喳'),
    (0x2F84A, 'M', u'嗂'),
    (0x2F84B, 'M', u'圖'),
    (0x2F84C, 'M', u'嘆'),
    (0x2F84D, 'M', u'圗'),
    (0x2F84E, 'M', u'噑'),
    (0x2F84F, 'M', u'噴'),
    (0x2F850, 'M', u'切'),
    (0x2F851, 'M', u'壮'),
    (0x2F852, 'M', u'城'),
    (0x2F853, 'M', u'埴'),
    (0x2F854, 'M', u'堍'),
    (0x2F855, 'M', u'型'),
    (0x2F856, 'M', u'堲'),
    (0x2F857, 'M', u'報'),
    (0x2F858, 'M', u'墬'),
    (0x2F859, 'M', u'𡓤'),
    (0x2F85A, 'M', u'売'),
    (0x2F85B, 'M', u'壷'),
    ]

def _seg_75():
    return [
    (0x2F85C, 'M', u'夆'),
    (0x2F85D, 'M', u'多'),
    (0x2F85E, 'M', u'夢'),
    (0x2F85F, 'M', u'奢'),
    (0x2F860, 'M', u'𡚨'),
    (0x2F861, 'M', u'𡛪'),
    (0x2F862, 'M', u'姬'),
    (0x2F863, 'M', u'娛'),
    (0x2F864, 'M', u'娧'),
    (0x2F865, 'M', u'姘'),
    (0x2F866, 'M', u'婦'),
    (0x2F867, 'M', u'㛮'),
    (0x2F868, 'X'),
    (0x2F869, 'M', u'嬈'),
    (0x2F86A, 'M', u'嬾'),
    (0x2F86C, 'M', u'𡧈'),
    (0x2F86D, 'M', u'寃'),
    (0x2F86E, 'M', u'寘'),
    (0x2F86F, 'M', u'寧'),
    (0x2F870, 'M', u'寳'),
    (0x2F871, 'M', u'𡬘'),
    (0x2F872, 'M', u'寿'),
    (0x2F873, 'M', u'将'),
    (0x2F874, 'X'),
    (0x2F875, 'M', u'尢'),
    (0x2F876, 'M', u'㞁'),
    (0x2F877, 'M', u'屠'),
    (0x2F878, 'M', u'屮'),
    (0x2F879, 'M', u'峀'),
    (0x2F87A, 'M', u'岍'),
    (0x2F87B, 'M', u'𡷤'),
    (0x2F87C, 'M', u'嵃'),
    (0x2F87D, 'M', u'𡷦'),
    (0x2F87E, 'M', u'嵮'),
    (0x2F87F, 'M', u'嵫'),
    (0x2F880, 'M', u'嵼'),
    (0x2F881, 'M', u'巡'),
    (0x2F882, 'M', u'巢'),
    (0x2F883, 'M', u'㠯'),
    (0x2F884, 'M', u'巽'),
    (0x2F885, 'M', u'帨'),
    (0x2F886, 'M', u'帽'),
    (0x2F887, 'M', u'幩'),
    (0x2F888, 'M', u'㡢'),
    (0x2F889, 'M', u'𢆃'),
    (0x2F88A, 'M', u'㡼'),
    (0x2F88B, 'M', u'庰'),
    (0x2F88C, 'M', u'庳'),
    (0x2F88D, 'M', u'庶'),
    (0x2F88E, 'M', u'廊'),
    (0x2F88F, 'M', u'𪎒'),
    (0x2F890, 'M', u'廾'),
    (0x2F891, 'M', u'𢌱'),
    (0x2F893, 'M', u'舁'),
    (0x2F894, 'M', u'弢'),
    (0x2F896, 'M', u'㣇'),
    (0x2F897, 'M', u'𣊸'),
    (0x2F898, 'M', u'𦇚'),
    (0x2F899, 'M', u'形'),
    (0x2F89A, 'M', u'彫'),
    (0x2F89B, 'M', u'㣣'),
    (0x2F89C, 'M', u'徚'),
    (0x2F89D, 'M', u'忍'),
    (0x2F89E, 'M', u'志'),
    (0x2F89F, 'M', u'忹'),
    (0x2F8A0, 'M', u'悁'),
    (0x2F8A1, 'M', u'㤺'),
    (0x2F8A2, 'M', u'㤜'),
    (0x2F8A3, 'M', u'悔'),
    (0x2F8A4, 'M', u'𢛔'),
    (0x2F8A5, 'M', u'惇'),
    (0x2F8A6, 'M', u'慈'),
    (0x2F8A7, 'M', u'慌'),
    (0x2F8A8, 'M', u'慎'),
    (0x2F8A9, 'M', u'慌'),
    (0x2F8AA, 'M', u'慺'),
    (0x2F8AB, 'M', u'憎'),
    (0x2F8AC, 'M', u'憲'),
    (0x2F8AD, 'M', u'憤'),
    (0x2F8AE, 'M', u'憯'),
    (0x2F8AF, 'M', u'懞'),
    (0x2F8B0, 'M', u'懲'),
    (0x2F8B1, 'M', u'懶'),
    (0x2F8B2, 'M', u'成'),
    (0x2F8B3, 'M', u'戛'),
    (0x2F8B4, 'M', u'扝'),
    (0x2F8B5, 'M', u'抱'),
    (0x2F8B6, 'M', u'拔'),
    (0x2F8B7, 'M', u'捐'),
    (0x2F8B8, 'M', u'𢬌'),
    (0x2F8B9, 'M', u'挽'),
    (0x2F8BA, 'M', u'拼'),
    (0x2F8BB, 'M', u'捨'),
    (0x2F8BC, 'M', u'掃'),
    (0x2F8BD, 'M', u'揤'),
    (0x2F8BE, 'M', u'𢯱'),
    (0x2F8BF, 'M', u'搢'),
    (0x2F8C0, 'M', u'揅'),
    (0x2F8C1, 'M', u'掩'),
    (0x2F8C2, 'M', u'㨮'),
    ]

def _seg_76():
    return [
    (0x2F8C3, 'M', u'摩'),
    (0x2F8C4, 'M', u'摾'),
    (0x2F8C5, 'M', u'撝'),
    (0x2F8C6, 'M', u'摷'),
    (0x2F8C7, 'M', u'㩬'),
    (0x2F8C8, 'M', u'敏'),
    (0x2F8C9, 'M', u'敬'),
    (0x2F8CA, 'M', u'𣀊'),
    (0x2F8CB, 'M', u'旣'),
    (0x2F8CC, 'M', u'書'),
    (0x2F8CD, 'M', u'晉'),
    (0x2F8CE, 'M', u'㬙'),
    (0x2F8CF, 'M', u'暑'),
    (0x2F8D0, 'M', u'㬈'),
    (0x2F8D1, 'M', u'㫤'),
    (0x2F8D2, 'M', u'冒'),
    (0x2F8D3, 'M', u'冕'),
    (0x2F8D4, 'M', u'最'),
    (0x2F8D5, 'M', u'暜'),
    (0x2F8D6, 'M', u'肭'),
    (0x2F8D7, 'M', u'䏙'),
    (0x2F8D8, 'M', u'朗'),
    (0x2F8D9, 'M', u'望'),
    (0x2F8DA, 'M', u'朡'),
    (0x2F8DB, 'M', u'杞'),
    (0x2F8DC, 'M', u'杓'),
    (0x2F8DD, 'M', u'𣏃'),
    (0x2F8DE, 'M', u'㭉'),
    (0x2F8DF, 'M', u'柺'),
    (0x2F8E0, 'M', u'枅'),
    (0x2F8E1, 'M', u'桒'),
    (0x2F8E2, 'M', u'梅'),
    (0x2F8E3, 'M', u'𣑭'),
    (0x2F8E4, 'M', u'梎'),
    (0x2F8E5, 'M', u'栟'),
    (0x2F8E6, 'M', u'椔'),
    (0x2F8E7, 'M', u'㮝'),
    (0x2F8E8, 'M', u'楂'),
    (0x2F8E9, 'M', u'榣'),
    (0x2F8EA, 'M', u'槪'),
    (0x2F8EB, 'M', u'檨'),
    (0x2F8EC, 'M', u'𣚣'),
    (0x2F8ED, 'M', u'櫛'),
    (0x2F8EE, 'M', u'㰘'),
    (0x2F8EF, 'M', u'次'),
    (0x2F8F0, 'M', u'𣢧'),
    (0x2F8F1, 'M', u'歔'),
    (0x2F8F2, 'M', u'㱎'),
    (0x2F8F3, 'M', u'歲'),
    (0x2F8F4, 'M', u'殟'),
    (0x2F8F5, 'M', u'殺'),
    (0x2F8F6, 'M', u'殻'),
    (0x2F8F7, 'M', u'𣪍'),
    (0x2F8F8, 'M', u'𡴋'),
    (0x2F8F9, 'M', u'𣫺'),
    (0x2F8FA, 'M', u'汎'),
    (0x2F8FB, 'M', u'𣲼'),
    (0x2F8FC, 'M', u'沿'),
    (0x2F8FD, 'M', u'泍'),
    (0x2F8FE, 'M', u'汧'),
    (0x2F8FF, 'M', u'洖'),
    (0x2F900, 'M', u'派'),
    (0x2F901, 'M', u'海'),
    (0x2F902, 'M', u'流'),
    (0x2F903, 'M', u'浩'),
    (0x2F904, 'M', u'浸'),
    (0x2F905, 'M', u'涅'),
    (0x2F906, 'M', u'𣴞'),
    (0x2F907, 'M', u'洴'),
    (0x2F908, 'M', u'港'),
    (0x2F909, 'M', u'湮'),
    (0x2F90A, 'M', u'㴳'),
    (0x2F90B, 'M', u'滋'),
    (0x2F90C, 'M', u'滇'),
    (0x2F90D, 'M', u'𣻑'),
    (0x2F90E, 'M', u'淹'),
    (0x2F90F, 'M', u'潮'),
    (0x2F910, 'M', u'𣽞'),
    (0x2F911, 'M', u'𣾎'),
    (0x2F912, 'M', u'濆'),
    (0x2F913, 'M', u'瀹'),
    (0x2F914, 'M', u'瀞'),
    (0x2F915, 'M', u'瀛'),
    (0x2F916, 'M', u'㶖'),
    (0x2F917, 'M', u'灊'),
    (0x2F918, 'M', u'災'),
    (0x2F919, 'M', u'灷'),
    (0x2F91A, 'M', u'炭'),
    (0x2F91B, 'M', u'𠔥'),
    (0x2F91C, 'M', u'煅'),
    (0x2F91D, 'M', u'𤉣'),
    (0x2F91E, 'M', u'熜'),
    (0x2F91F, 'X'),
    (0x2F920, 'M', u'爨'),
    (0x2F921, 'M', u'爵'),
    (0x2F922, 'M', u'牐'),
    (0x2F923, 'M', u'𤘈'),
    (0x2F924, 'M', u'犀'),
    (0x2F925, 'M', u'犕'),
    (0x2F926, 'M', u'𤜵'),
    ]

def _seg_77():
    return [
    (0x2F927, 'M', u'𤠔'),
    (0x2F928, 'M', u'獺'),
    (0x2F929, 'M', u'王'),
    (0x2F92A, 'M', u'㺬'),
    (0x2F92B, 'M', u'玥'),
    (0x2F92C, 'M', u'㺸'),
    (0x2F92E, 'M', u'瑇'),
    (0x2F92F, 'M', u'瑜'),
    (0x2F930, 'M', u'瑱'),
    (0x2F931, 'M', u'璅'),
    (0x2F932, 'M', u'瓊'),
    (0x2F933, 'M', u'㼛'),
    (0x2F934, 'M', u'甤'),
    (0x2F935, 'M', u'𤰶'),
    (0x2F936, 'M', u'甾'),
    (0x2F937, 'M', u'𤲒'),
    (0x2F938, 'M', u'異'),
    (0x2F939, 'M', u'𢆟'),
    (0x2F93A, 'M', u'瘐'),
    (0x2F93B, 'M', u'𤾡'),
    (0x2F93C, 'M', u'𤾸'),
    (0x2F93D, 'M', u'𥁄'),
    (0x2F93E, 'M', u'㿼'),
    (0x2F93F, 'M', u'䀈'),
    (0x2F940, 'M', u'直'),
    (0x2F941, 'M', u'𥃳'),
    (0x2F942, 'M', u'𥃲'),
    (0x2F943, 'M', u'𥄙'),
    (0x2F944, 'M', u'𥄳'),
    (0x2F945, 'M', u'眞'),
    (0x2F946, 'M', u'真'),
    (0x2F948, 'M', u'睊'),
    (0x2F949, 'M', u'䀹'),
    (0x2F94A, 'M', u'瞋'),
    (0x2F94B, 'M', u'䁆'),
    (0x2F94C, 'M', u'䂖'),
    (0x2F94D, 'M', u'𥐝'),
    (0x2F94E, 'M', u'硎'),
    (0x2F94F, 'M', u'碌'),
    (0x2F950, 'M', u'磌'),
    (0x2F951, 'M', u'䃣'),
    (0x2F952, 'M', u'𥘦'),
    (0x2F953, 'M', u'祖'),
    (0x2F954, 'M', u'𥚚'),
    (0x2F955, 'M', u'𥛅'),
    (0x2F956, 'M', u'福'),
    (0x2F957, 'M', u'秫'),
    (0x2F958, 'M', u'䄯'),
    (0x2F959, 'M', u'穀'),
    (0x2F95A, 'M', u'穊'),
    (0x2F95B, 'M', u'穏'),
    (0x2F95C, 'M', u'𥥼'),
    (0x2F95D, 'M', u'𥪧'),
    (0x2F95F, 'X'),
    (0x2F960, 'M', u'䈂'),
    (0x2F961, 'M', u'𥮫'),
    (0x2F962, 'M', u'篆'),
    (0x2F963, 'M', u'築'),
    (0x2F964, 'M', u'䈧'),
    (0x2F965, 'M', u'𥲀'),
    (0x2F966, 'M', u'糒'),
    (0x2F967, 'M', u'䊠'),
    (0x2F968, 'M', u'糨'),
    (0x2F969, 'M', u'糣'),
    (0x2F96A, 'M', u'紀'),
    (0x2F96B, 'M', u'𥾆'),
    (0x2F96C, 'M', u'絣'),
    (0x2F96D, 'M', u'䌁'),
    (0x2F96E, 'M', u'緇'),
    (0x2F96F, 'M', u'縂'),
    (0x2F970, 'M', u'繅'),
    (0x2F971, 'M', u'䌴'),
    (0x2F972, 'M', u'𦈨'),
    (0x2F973, 'M', u'𦉇'),
    (0x2F974, 'M', u'䍙'),
    (0x2F975, 'M', u'𦋙'),
    (0x2F976, 'M', u'罺'),
    (0x2F977, 'M', u'𦌾'),
    (0x2F978, 'M', u'羕'),
    (0x2F979, 'M', u'翺'),
    (0x2F97A, 'M', u'者'),
    (0x2F97B, 'M', u'𦓚'),
    (0x2F97C, 'M', u'𦔣'),
    (0x2F97D, 'M', u'聠'),
    (0x2F97E, 'M', u'𦖨'),
    (0x2F97F, 'M', u'聰'),
    (0x2F980, 'M', u'𣍟'),
    (0x2F981, 'M', u'䏕'),
    (0x2F982, 'M', u'育'),
    (0x2F983, 'M', u'脃'),
    (0x2F984, 'M', u'䐋'),
    (0x2F985, 'M', u'脾'),
    (0x2F986, 'M', u'媵'),
    (0x2F987, 'M', u'𦞧'),
    (0x2F988, 'M', u'𦞵'),
    (0x2F989, 'M', u'𣎓'),
    (0x2F98A, 'M', u'𣎜'),
    (0x2F98B, 'M', u'舁'),
    (0x2F98C, 'M', u'舄'),
    (0x2F98D, 'M', u'辞'),
    ]

def _seg_78():
    return [
    (0x2F98E, 'M', u'䑫'),
    (0x2F98F, 'M', u'芑'),
    (0x2F990, 'M', u'芋'),
    (0x2F991, 'M', u'芝'),
    (0x2F992, 'M', u'劳'),
    (0x2F993, 'M', u'花'),
    (0x2F994, 'M', u'芳'),
    (0x2F995, 'M', u'芽'),
    (0x2F996, 'M', u'苦'),
    (0x2F997, 'M', u'𦬼'),
    (0x2F998, 'M', u'若'),
    (0x2F999, 'M', u'茝'),
    (0x2F99A, 'M', u'荣'),
    (0x2F99B, 'M', u'莭'),
    (0x2F99C, 'M', u'茣'),
    (0x2F99D, 'M', u'莽'),
    (0x2F99E, 'M', u'菧'),
    (0x2F99F, 'M', u'著'),
    (0x2F9A0, 'M', u'荓'),
    (0x2F9A1, 'M', u'菊'),
    (0x2F9A2, 'M', u'菌'),
    (0x2F9A3, 'M', u'菜'),
    (0x2F9A4, 'M', u'𦰶'),
    (0x2F9A5, 'M', u'𦵫'),
    (0x2F9A6, 'M', u'𦳕'),
    (0x2F9A7, 'M', u'䔫'),
    (0x2F9A8, 'M', u'蓱'),
    (0x2F9A9, 'M', u'蓳'),
    (0x2F9AA, 'M', u'蔖'),
    (0x2F9AB, 'M', u'𧏊'),
    (0x2F9AC, 'M', u'蕤'),
    (0x2F9AD, 'M', u'𦼬'),
    (0x2F9AE, 'M', u'䕝'),
    (0x2F9AF, 'M', u'䕡'),
    (0x2F9B0, 'M', u'𦾱'),
    (0x2F9B1, 'M', u'𧃒'),
    (0x2F9B2, 'M', u'䕫'),
    (0x2F9B3, 'M', u'虐'),
    (0x2F9B4, 'M', u'虜'),
    (0x2F9B5, 'M', u'虧'),
    (0x2F9B6, 'M', u'虩'),
    (0x2F9B7, 'M', u'蚩'),
    (0x2F9B8, 'M', u'蚈'),
    (0x2F9B9, 'M', u'蜎'),
    (0x2F9BA, 'M', u'蛢'),
    (0x2F9BB, 'M', u'蝹'),
    (0x2F9BC, 'M', u'蜨'),
    (0x2F9BD, 'M', u'蝫'),
    (0x2F9BE, 'M', u'螆'),
    (0x2F9BF, 'X'),
    (0x2F9C0, 'M', u'蟡'),
    (0x2F9C1, 'M', u'蠁'),
    (0x2F9C2, 'M', u'䗹'),
    (0x2F9C3, 'M', u'衠'),
    (0x2F9C4, 'M', u'衣'),
    (0x2F9C5, 'M', u'𧙧'),
    (0x2F9C6, 'M', u'裗'),
    (0x2F9C7, 'M', u'裞'),
    (0x2F9C8, 'M', u'䘵'),
    (0x2F9C9, 'M', u'裺'),
    (0x2F9CA, 'M', u'㒻'),
    (0x2F9CB, 'M', u'𧢮'),
    (0x2F9CC, 'M', u'𧥦'),
    (0x2F9CD, 'M', u'䚾'),
    (0x2F9CE, 'M', u'䛇'),
    (0x2F9CF, 'M', u'誠'),
    (0x2F9D0, 'M', u'諭'),
    (0x2F9D1, 'M', u'變'),
    (0x2F9D2, 'M', u'豕'),
    (0x2F9D3, 'M', u'𧲨'),
    (0x2F9D4, 'M', u'貫'),
    (0x2F9D5, 'M', u'賁'),
    (0x2F9D6, 'M', u'贛'),
    (0x2F9D7, 'M', u'起'),
    (0x2F9D8, 'M', u'𧼯'),
    (0x2F9D9, 'M', u'𠠄'),
    (0x2F9DA, 'M', u'跋'),
    (0x2F9DB, 'M', u'趼'),
    (0x2F9DC, 'M', u'跰'),
    (0x2F9DD, 'M', u'𠣞'),
    (0x2F9DE, 'M', u'軔'),
    (0x2F9DF, 'M', u'輸'),
    (0x2F9E0, 'M', u'𨗒'),
    (0x2F9E1, 'M', u'𨗭'),
    (0x2F9E2, 'M', u'邔'),
    (0x2F9E3, 'M', u'郱'),
    (0x2F9E4, 'M', u'鄑'),
    (0x2F9E5, 'M', u'𨜮'),
    (0x2F9E6, 'M', u'鄛'),
    (0x2F9E7, 'M', u'鈸'),
    (0x2F9E8, 'M', u'鋗'),
    (0x2F9E9, 'M', u'鋘'),
    (0x2F9EA, 'M', u'鉼'),
    (0x2F9EB, 'M', u'鏹'),
    (0x2F9EC, 'M', u'鐕'),
    (0x2F9ED, 'M', u'𨯺'),
    (0x2F9EE, 'M', u'開'),
    (0x2F9EF, 'M', u'䦕'),
    (0x2F9F0, 'M', u'閷'),
    (0x2F9F1, 'M', u'𨵷'),
    ]

def _seg_79():
    return [
    (0x2F9F2, 'M', u'䧦'),
    (0x2F9F3, 'M', u'雃'),
    (0x2F9F4, 'M', u'嶲'),
    (0x2F9F5, 'M', u'霣'),
    (0x2F9F6, 'M', u'𩅅'),
    (0x2F9F7, 'M', u'𩈚'),
    (0x2F9F8, 'M', u'䩮'),
    (0x2F9F9, 'M', u'䩶'),
    (0x2F9FA, 'M', u'韠'),
    (0x2F9FB, 'M', u'𩐊'),
    (0x2F9FC, 'M', u'䪲'),
    (0x2F9FD, 'M', u'𩒖'),
    (0x2F9FE, 'M', u'頋'),
    (0x2FA00, 'M', u'頩'),
    (0x2FA01, 'M', u'𩖶'),
    (0x2FA02, 'M', u'飢'),
    (0x2FA03, 'M', u'䬳'),
    (0x2FA04, 'M', u'餩'),
    (0x2FA05, 'M', u'馧'),
    (0x2FA06, 'M', u'駂'),
    (0x2FA07, 'M', u'駾'),
    (0x2FA08, 'M', u'䯎'),
    (0x2FA09, 'M', u'𩬰'),
    (0x2FA0A, 'M', u'鬒'),
    (0x2FA0B, 'M', u'鱀'),
    (0x2FA0C, 'M', u'鳽'),
    (0x2FA0D, 'M', u'䳎'),
    (0x2FA0E, 'M', u'䳭'),
    (0x2FA0F, 'M', u'鵧'),
    (0x2FA10, 'M', u'𪃎'),
    (0x2FA11, 'M', u'䳸'),
    (0x2FA12, 'M', u'𪄅'),
    (0x2FA13, 'M', u'𪈎'),
    (0x2FA14, 'M', u'𪊑'),
    (0x2FA15, 'M', u'麻'),
    (0x2FA16, 'M', u'䵖'),
    (0x2FA17, 'M', u'黹'),
    (0x2FA18, 'M', u'黾'),
    (0x2FA19, 'M', u'鼅'),
    (0x2FA1A, 'M', u'鼏'),
    (0x2FA1B, 'M', u'鼖'),
    (0x2FA1C, 'M', u'鼻'),
    (0x2FA1D, 'M', u'𪘀'),
    (0x2FA1E, 'X'),
    (0x30000, 'V'),
    (0x3134B, 'X'),
    (0xE0100, 'I'),
    (0xE01F0, 'X'),
    ]

uts46data = tuple(
    _seg_0()
    + _seg_1()
    + _seg_2()
    + _seg_3()
    + _seg_4()
    + _seg_5()
    + _seg_6()
    + _seg_7()
    + _seg_8()
    + _seg_9()
    + _seg_10()
    + _seg_11()
    + _seg_12()
    + _seg_13()
    + _seg_14()
    + _seg_15()
    + _seg_16()
    + _seg_17()
    + _seg_18()
    + _seg_19()
    + _seg_20()
    + _seg_21()
    + _seg_22()
    + _seg_23()
    + _seg_24()
    + _seg_25()
    + _seg_26()
    + _seg_27()
    + _seg_28()
    + _seg_29()
    + _seg_30()
    + _seg_31()
    + _seg_32()
    + _seg_33()
    + _seg_34()
    + _seg_35()
    + _seg_36()
    + _seg_37()
    + _seg_38()
    + _seg_39()
    + _seg_40()
    + _seg_41()
    + _seg_42()
    + _seg_43()
    + _seg_44()
    + _seg_45()
    + _seg_46()
    + _seg_47()
    + _seg_48()
    + _seg_49()
    + _seg_50()
    + _seg_51()
    + _seg_52()
    + _seg_53()
    + _seg_54()
    + _seg_55()
    + _seg_56()
    + _seg_57()
    + _seg_58()
    + _seg_59()
    + _seg_60()
    + _seg_61()
    + _seg_62()
    + _seg_63()
    + _seg_64()
    + _seg_65()
    + _seg_66()
    + _seg_67()
    + _seg_68()
    + _seg_69()
    + _seg_70()
    + _seg_71()
    + _seg_72()
    + _seg_73()
    + _seg_74()
    + _seg_75()
    + _seg_76()
    + _seg_77()
    + _seg_78()
    + _seg_79()
)
