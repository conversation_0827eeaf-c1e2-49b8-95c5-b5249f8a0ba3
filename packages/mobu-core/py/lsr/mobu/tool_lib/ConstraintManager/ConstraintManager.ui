<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWidget</class>
 <widget class="QWidget" name="MainWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>390</width>
    <height>151</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>390</width>
    <height>140</height>
   </size>
  </property>
  <property name="baseSize">
   <size>
    <width>390</width>
    <height>180</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>LSR Constraint Manager</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="QFrame" name="vFrame_for_3WidgetGroupBox">
     <layout class="QVBoxLayout" name="vLayout_for_3_widgets">
      <item>
       <widget class="QGroupBox" name="groupBox_3Widgets">
        <property name="title">
         <string>Constraint Manager 1.0</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="flat">
         <bool>false</bool>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_ConstraintName">
           <property name="text">
            <string>Constraint Type</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="combo_ListOfConstraints">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="maximumSize">
            <size>
             <width>400</width>
             <height>19</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkbox_SnapOrNot">
           <property name="text">
            <string>Snap</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="vBoxLayoutForConstraintUsageInfoLAbel">
     <item>
      <widget class="QLabel" name="label_ConstraintUsageInfo">
       <property name="text">
        <string>Sample Text</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="vBoxLayoutForPushButton">
     <item>
      <widget class="QPushButton" name="btn_ForCreateConstraint">
       <property name="text">
        <string>Create Constraint</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
