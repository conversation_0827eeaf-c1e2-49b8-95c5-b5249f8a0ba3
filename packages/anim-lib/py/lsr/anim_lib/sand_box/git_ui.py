# -*- coding: utf-8 -*-

from git import Repo
import os


###########################################################################################################
# thread --- 判断是否dirty并添加标记

import threading
import time
from git import Repo
def GitStatusWatchDog(*args, **kwargs):
    t = threading.currentThread()
    repo_path = "C:/Users/<USER>/Desktop/__today/__AnimLib_git/"
    repo = Repo(repo_path)
    changed_files = []
    changed_files.extend([x.a_path for x in repo.index.diff(None)])
    changed_files.extend([x for x in repo.untracked_files])

    while getattr(t, "do_run", True):
        for item in args[0]:
            current_folder = item.io_data["flipbook_sequence_folder"].replace("\\", "/").partition(repo_path)[-1]
            clean = True
            for changed_file in changed_files:
                if current_folder in changed_file:
                    if "git_modified" not in item.io_data["flipbook_tags"]:
                        item.io_data["flipbook_tags"].append("git_modified")
                    clean = False
                    break
            if clean:
                if "git_modified" in item.io_data["flipbook_tags"]:
                    item.io_data["flipbook_tags"].remove("git_modified")
        time.sleep(1.5)
    print("Stopping as you wish.")


###########################################################################################################
# git preview delegate

# draw git icon
if "git_modified" in item.io_data["flipbook_tags"]:
    border = QtGui.QPainterPath(rect.topLeft())
    border.lineTo(rect.topRight())
    border.lineTo(rect.bottomRight())
    border.lineTo(rect.bottomLeft())
    border.lineTo(rect.topLeft())
    painter.setBrush(QtGui.QBrush(QtCore.Qt.NoBrush))
    pen = QtGui.QPen(QtCore.Qt.white, 2)
    pen.setColor(QtGui.QColor("#ebe120"))
    painter.setPen(pen)
    painter.drawPath(border)

    # pixmap = QtGui.QPixmap(uti_pyside.resolve_icon_path(
    #     "/library/circle.png"))  # Change the path and size as needed
    # mask = pixmap.mask()
    # pixmap.fill(QtGui.QColor('#1bc800'))  # Change the color as needed
    # pixmap.setMask(mask)
    # px = x + rect.width() * 0.91
    # py = y - rect.height() * 0.09
    # pw = rect.width() * 0.04
    # ph = rect.height() * 0.04
    #
    # painter.setRenderHints(QtGui.QPainter.SmoothPixmapTransform)
    # painter.drawPixmap(px, py, pw, ph, pixmap)

