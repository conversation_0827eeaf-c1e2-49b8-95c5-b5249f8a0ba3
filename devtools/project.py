from packages.project import Project


class PyEXT_Project(Project):
    def __init__(self, name, flavor) -> None:
        super().__init__(name=name, flavor=flavor)

    def build(self):
        pass

def get_project(flavor):
    project = PyEXT_Project(name='python-ext', flavor=flavor)
    project.install(src='py/parameterized', dst='py/parameterized', local_edit=True)
    project.install(src='py/gorilla.py', dst='py/gorilla.py', local_edit=True)
    project.install(src='py/six.py', dst='py/six.py', local_edit=True)
    project.install(src='py/lsr', dst='py/lsr', local_edit=True)
    return project


def build(flavor, maya_ver):
    project = PyEXT_Project(name='python-ext', flavor=flavor)
    project.build()
