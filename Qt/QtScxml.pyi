# This Python file uses the following encoding: utf-8
#############################################################################
##
## Copyright (C) 2020 The Qt Company Ltd.
## Contact: https://www.qt.io/licensing/
##
## This file is part of Qt for Python.
##
## $QT_BEGIN_LICENSE:LGPL$
## Commercial License Usage
## Licensees holding valid commercial Qt licenses may use this file in
## accordance with the commercial license agreement provided with the
## Software or, alternatively, in accordance with the terms contained in
## a written agreement between you and The Qt Company. For licensing terms
## and conditions see https://www.qt.io/terms-conditions. For further
## information use the contact form at https://www.qt.io/contact-us.
##
## GNU Lesser General Public License Usage
## Alternatively, this file may be used under the terms of the GNU Lesser
## General Public License version 3 as published by the Free Software
## Foundation and appearing in the file LICENSE.LGPL3 included in the
## packaging of this file. Please review the following information to
## ensure the GNU Lesser General Public License version 3 requirements
## will be met: https://www.gnu.org/licenses/lgpl-3.0.html.
##
## GNU General Public License Usage
## Alternatively, this file may be used under the terms of the GNU
## General Public License version 2.0 or (at your option) the GNU General
## Public license version 3 or any later version approved by the KDE Free
## Qt Foundation. The licenses are as published by the Free Software
## Foundation and appearing in the file LICENSE.GPL2 and LICENSE.GPL3
## included in the packaging of this file. Please review the following
## information to ensure the GNU General Public License requirements will
## be met: https://www.gnu.org/licenses/gpl-2.0.html and
## https://www.gnu.org/licenses/gpl-3.0.html.
##
## $QT_END_LICENSE$
##
#############################################################################

"""
This file contains the exact signatures for all functions in module
PySide2.QtScxml, except for defaults which are replaced by "...".
"""

# Module PySide2.QtScxml
import PySide2
try:
    import typing
except ImportError:
    from PySide2.support.signature import typing
from PySide2.support.signature.mapping import (
    Virtual, Missing, Invalid, Default, Instance)

class Object(object): pass

import shiboken2 as Shiboken
Shiboken.Object = Object

import PySide2.QtCore
import PySide2.QtScxml


class QScxmlCompiler(Shiboken.Object):

    class Loader(Shiboken.Object):

        def __init__(self): ...

        def load(self, name:str, baseDir:str) -> typing.Tuple: ...

    def __init__(self, xmlReader:PySide2.QtCore.QXmlStreamReader): ...

    def compile(self) -> PySide2.QtScxml.QScxmlStateMachine: ...
    def errors(self) -> typing.List: ...
    def fileName(self) -> str: ...
    def loader(self) -> PySide2.QtScxml.QScxmlCompiler.Loader: ...
    def setFileName(self, fileName:str): ...
    def setLoader(self, newLoader:PySide2.QtScxml.QScxmlCompiler.Loader): ...


class QScxmlCppDataModel(PySide2.QtScxml.QScxmlDataModel):

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def evaluateAssignment(self, id:int) -> bool: ...
    def evaluateForeach(self, id:int, body:PySide2.QtScxml.QScxmlDataModel.ForeachLoopBody) -> bool: ...
    def evaluateInitialization(self, id:int) -> bool: ...
    def hasScxmlProperty(self, name:str) -> bool: ...
    def inState(self, stateName:str) -> bool: ...
    def scxmlEvent(self) -> PySide2.QtScxml.QScxmlEvent: ...
    def scxmlProperty(self, name:str) -> typing.Any: ...
    def setScxmlEvent(self, scxmlEvent:PySide2.QtScxml.QScxmlEvent): ...
    def setScxmlProperty(self, name:str, value:typing.Any, context:str) -> bool: ...
    def setup(self, initialDataValues:typing.Dict) -> bool: ...


class QScxmlDataModel(PySide2.QtCore.QObject):

    class ForeachLoopBody(Shiboken.Object):

        def __init__(self): ...

        def run(self) -> bool: ...

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def evaluateAssignment(self, id:int) -> bool: ...
    def evaluateForeach(self, id:int, body:PySide2.QtScxml.QScxmlDataModel.ForeachLoopBody) -> bool: ...
    def evaluateInitialization(self, id:int) -> bool: ...
    def evaluateToBool(self, id:int) -> typing.Tuple: ...
    def evaluateToString(self, id:int) -> typing.Tuple: ...
    def evaluateToVariant(self, id:int) -> typing.Tuple: ...
    def evaluateToVoid(self, id:int) -> bool: ...
    def hasScxmlProperty(self, name:str) -> bool: ...
    def scxmlProperty(self, name:str) -> typing.Any: ...
    def setScxmlEvent(self, event:PySide2.QtScxml.QScxmlEvent): ...
    def setScxmlProperty(self, name:str, value:typing.Any, context:str) -> bool: ...
    def setStateMachine(self, stateMachine:PySide2.QtScxml.QScxmlStateMachine): ...
    def setup(self, initialDataValues:typing.Dict) -> bool: ...
    def stateMachine(self) -> PySide2.QtScxml.QScxmlStateMachine: ...


class QScxmlDynamicScxmlServiceFactory(PySide2.QtScxml.QScxmlInvokableServiceFactory):

    def __init__(self, invokeInfo:PySide2.QtScxml.QScxmlExecutableContent.InvokeInfo, names:typing.List, parameters:typing.List, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def invoke(self, parentStateMachine:PySide2.QtScxml.QScxmlStateMachine) -> PySide2.QtScxml.QScxmlInvokableService: ...


class QScxmlEcmaScriptDataModel(PySide2.QtScxml.QScxmlDataModel):

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def evaluateAssignment(self, id:int) -> bool: ...
    def evaluateForeach(self, id:int, body:PySide2.QtScxml.QScxmlDataModel.ForeachLoopBody) -> bool: ...
    def evaluateInitialization(self, id:int) -> bool: ...
    def evaluateToBool(self, id:int) -> typing.Tuple: ...
    def evaluateToString(self, id:int) -> typing.Tuple: ...
    def evaluateToVariant(self, id:int) -> typing.Tuple: ...
    def evaluateToVoid(self, id:int) -> bool: ...
    def hasScxmlProperty(self, name:str) -> bool: ...
    def scxmlProperty(self, name:str) -> typing.Any: ...
    def setScxmlEvent(self, event:PySide2.QtScxml.QScxmlEvent): ...
    def setScxmlProperty(self, name:str, value:typing.Any, context:str) -> bool: ...
    def setup(self, initialDataValues:typing.Dict) -> bool: ...


class QScxmlError(Shiboken.Object):

    @typing.overload
    def __init__(self): ...
    @typing.overload
    def __init__(self, arg__1:PySide2.QtScxml.QScxmlError): ...
    @typing.overload
    def __init__(self, fileName:str, line:int, column:int, description:str): ...

    def __copy__(self): ...
    def column(self) -> int: ...
    def description(self) -> str: ...
    def fileName(self) -> str: ...
    def isValid(self) -> bool: ...
    def line(self) -> int: ...
    def toString(self) -> str: ...


class QScxmlEvent(Shiboken.Object):
    PlatformEvent            : QScxmlEvent = ... # 0x0
    InternalEvent            : QScxmlEvent = ... # 0x1
    ExternalEvent            : QScxmlEvent = ... # 0x2

    class EventType(object):
        PlatformEvent            : QScxmlEvent.EventType = ... # 0x0
        InternalEvent            : QScxmlEvent.EventType = ... # 0x1
        ExternalEvent            : QScxmlEvent.EventType = ... # 0x2

    @typing.overload
    def __init__(self): ...
    @typing.overload
    def __init__(self, other:PySide2.QtScxml.QScxmlEvent): ...

    def clear(self): ...
    def data(self) -> typing.Any: ...
    def delay(self) -> int: ...
    def errorMessage(self) -> str: ...
    def eventType(self) -> PySide2.QtScxml.QScxmlEvent.EventType: ...
    def invokeId(self) -> str: ...
    def isErrorEvent(self) -> bool: ...
    def name(self) -> str: ...
    def origin(self) -> str: ...
    def originType(self) -> str: ...
    def scxmlType(self) -> str: ...
    def sendId(self) -> str: ...
    def setData(self, data:typing.Any): ...
    def setDelay(self, delayInMiliSecs:int): ...
    def setErrorMessage(self, message:str): ...
    def setEventType(self, type:PySide2.QtScxml.QScxmlEvent.EventType): ...
    def setInvokeId(self, invokeId:str): ...
    def setName(self, name:str): ...
    def setOrigin(self, origin:str): ...
    def setOriginType(self, originType:str): ...
    def setSendId(self, sendId:str): ...


class QScxmlExecutableContent(Shiboken.Object):

    class AssignmentInfo(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, AssignmentInfo:PySide2.QtScxml.QScxmlExecutableContent.AssignmentInfo): ...

        def __copy__(self): ...

    class EvaluatorInfo(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, EvaluatorInfo:PySide2.QtScxml.QScxmlExecutableContent.EvaluatorInfo): ...

        def __copy__(self): ...

    class ForeachInfo(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, ForeachInfo:PySide2.QtScxml.QScxmlExecutableContent.ForeachInfo): ...

        def __copy__(self): ...

    class InvokeInfo(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, InvokeInfo:PySide2.QtScxml.QScxmlExecutableContent.InvokeInfo): ...

        def __copy__(self): ...

    class ParameterInfo(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, ParameterInfo:PySide2.QtScxml.QScxmlExecutableContent.ParameterInfo): ...

        def __copy__(self): ...


class QScxmlInvokableService(PySide2.QtCore.QObject):

    def __init__(self, parentStateMachine:PySide2.QtScxml.QScxmlStateMachine, parent:PySide2.QtScxml.QScxmlInvokableServiceFactory): ...

    def id(self) -> str: ...
    def name(self) -> str: ...
    def parentStateMachine(self) -> PySide2.QtScxml.QScxmlStateMachine: ...
    def postEvent(self, event:PySide2.QtScxml.QScxmlEvent): ...
    def start(self) -> bool: ...


class QScxmlInvokableServiceFactory(PySide2.QtCore.QObject):

    def __init__(self, invokeInfo:PySide2.QtScxml.QScxmlExecutableContent.InvokeInfo, names:typing.List, parameters:typing.List, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def invoke(self, parentStateMachine:PySide2.QtScxml.QScxmlStateMachine) -> PySide2.QtScxml.QScxmlInvokableService: ...
    def invokeInfo(self) -> PySide2.QtScxml.QScxmlExecutableContent.InvokeInfo: ...
    def names(self) -> typing.List: ...
    def parameters(self) -> typing.List: ...


class QScxmlNullDataModel(PySide2.QtScxml.QScxmlDataModel):

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def evaluateAssignment(self, id:int) -> bool: ...
    def evaluateForeach(self, id:int, body:PySide2.QtScxml.QScxmlDataModel.ForeachLoopBody) -> bool: ...
    def evaluateInitialization(self, id:int) -> bool: ...
    def evaluateToBool(self, id:int) -> typing.Tuple: ...
    def evaluateToString(self, id:int) -> typing.Tuple: ...
    def evaluateToVariant(self, id:int) -> typing.Tuple: ...
    def evaluateToVoid(self, id:int) -> bool: ...
    def hasScxmlProperty(self, name:str) -> bool: ...
    def scxmlProperty(self, name:str) -> typing.Any: ...
    def setScxmlEvent(self, event:PySide2.QtScxml.QScxmlEvent): ...
    def setScxmlProperty(self, name:str, value:typing.Any, context:str) -> bool: ...
    def setup(self, initialDataValues:typing.Dict) -> bool: ...


class QScxmlStateMachine(PySide2.QtCore.QObject):

    def __init__(self, metaObject:PySide2.QtCore.QMetaObject, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def activeStateNames(self, compress:bool=...) -> typing.List: ...
    def cancelDelayedEvent(self, sendId:str): ...
    def connectToEvent(self, scxmlEventSpec:str, receiver:PySide2.QtCore.QObject, method:bytes, type:PySide2.QtCore.Qt.ConnectionType=...) -> PySide2.QtCore.QMetaObject.Connection: ...
    def connectToState(self, scxmlStateName:str, receiver:PySide2.QtCore.QObject, method:bytes, type:PySide2.QtCore.Qt.ConnectionType=...) -> PySide2.QtCore.QMetaObject.Connection: ...
    def dataModel(self) -> PySide2.QtScxml.QScxmlDataModel: ...
    @staticmethod
    def fromData(data:PySide2.QtCore.QIODevice, fileName:str=...) -> PySide2.QtScxml.QScxmlStateMachine: ...
    @staticmethod
    def fromFile(fileName:str) -> PySide2.QtScxml.QScxmlStateMachine: ...
    def init(self) -> bool: ...
    def initialValues(self) -> typing.Dict: ...
    def invokedServices(self) -> typing.List: ...
    @typing.overload
    def isActive(self, scxmlStateName:str) -> bool: ...
    @typing.overload
    def isActive(self, stateIndex:int) -> bool: ...
    def isDispatchableTarget(self, target:str) -> bool: ...
    def isInitialized(self) -> bool: ...
    def isInvoked(self) -> bool: ...
    def isRunning(self) -> bool: ...
    def loader(self) -> PySide2.QtScxml.QScxmlCompiler.Loader: ...
    def name(self) -> str: ...
    def parseErrors(self) -> typing.List: ...
    def sessionId(self) -> str: ...
    def setDataModel(self, model:PySide2.QtScxml.QScxmlDataModel): ...
    def setInitialValues(self, initialValues:typing.Dict): ...
    def setLoader(self, loader:PySide2.QtScxml.QScxmlCompiler.Loader): ...
    def setRunning(self, running:bool): ...
    def setTableData(self, tableData:PySide2.QtScxml.QScxmlTableData): ...
    def start(self): ...
    def stateNames(self, compress:bool=...) -> typing.List: ...
    def stop(self): ...
    @typing.overload
    def submitEvent(self, event:PySide2.QtScxml.QScxmlEvent): ...
    @typing.overload
    def submitEvent(self, eventName:str): ...
    @typing.overload
    def submitEvent(self, eventName:str, data:typing.Any): ...
    def tableData(self) -> PySide2.QtScxml.QScxmlTableData: ...


class QScxmlStaticScxmlServiceFactory(PySide2.QtScxml.QScxmlInvokableServiceFactory):

    def __init__(self, metaObject:PySide2.QtCore.QMetaObject, invokeInfo:PySide2.QtScxml.QScxmlExecutableContent.InvokeInfo, nameList:typing.List, parameters:typing.List, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def invoke(self, parentStateMachine:PySide2.QtScxml.QScxmlStateMachine) -> PySide2.QtScxml.QScxmlInvokableService: ...


class QScxmlTableData(Shiboken.Object):

    def __init__(self): ...

    def assignmentInfo(self, assignmentId:int) -> PySide2.QtScxml.QScxmlExecutableContent.AssignmentInfo: ...
    def dataNames(self) -> typing.Tuple: ...
    def evaluatorInfo(self, evaluatorId:int) -> PySide2.QtScxml.QScxmlExecutableContent.EvaluatorInfo: ...
    def foreachInfo(self, foreachId:int) -> PySide2.QtScxml.QScxmlExecutableContent.ForeachInfo: ...
    def initialSetup(self) -> int: ...
    def instructions(self) -> typing.List: ...
    def name(self) -> str: ...
    def serviceFactory(self, id:int) -> PySide2.QtScxml.QScxmlInvokableServiceFactory: ...
    def stateMachineTable(self) -> typing.List: ...
    def string(self, id:int) -> str: ...

# eof
