from functools import partial
from Qt import QtWidgets, QtCore

import pyfbsdk as fb

from lsr.mobu.utils import PlaybackControl
from lsr.qt.core.base_main_window import get_window_class
from lsr.mobu.tool_lib.MoEditTool.AnimTakeEdit import TakeEdit

base_class = get_window_class(app_name="Take Manager v1.0")


def _add_separator_line(edit_layout, direction="horizontal", *args, **kwargs):
    """
    Add a separator line to the layout.
    Args:
        edit_layout (QtWidgets.QLayout): The layout to add the separator line to.
        direction (str): The direction of the separator line.
    """
    line = QtWidgets.QFrame()
    if direction == "horizontal":
        line.setFrameShape(QtWidgets.QFrame.HLine)
    elif direction == "vertical":
        line.setFrameShape(QtWidgets.QFrame.VLine)
    line.setFrameShadow(QtWidgets.QFrame.Sunken)
    edit_layout.addWidget(line)


class TakeManagerUI(base_class):
    """Take Manager UI for managing animation takes in MotionBuilder"""

    _REUSE_SINGLETON = False

    def __init__(self, *args, **kwargs):
        super(TakeManagerUI, self).__init__(set_style=False, banner_widget=True, has_art=True)
        self.app = fb.FBApplication()
        self.take_edit = TakeEdit()
        self.create_connections()
        self.refresh_take_list()

    def setup_ui(self, *args, **kwargs):
        """Initialize and setup the user interface components"""
        central_widget = QtWidgets.QWidget(self)
        self.setCentralWidget(central_widget)
        layout = QtWidgets.QVBoxLayout(central_widget)

        # Setup Take List group
        self._setup_take_list_group(layout, central_widget)

        _add_separator_line(layout)

        # Setup Take Edit group
        self._setup_take_edit_group(layout)

    def showEvent(self, event, *args, **kwargs):
        """Save settings before showing."""
        super(TakeManagerUI, self).showEvent(event)
        self.on_show()

    def close_instances(self, event, *args, **kwargs):
        """Save settings before closing."""
        self.on_hide()
        super(TakeManagerUI, self).closeEvent(event)
        self.close()

    def hideEvent(self, event, *args, **kwargs):
        """ Save settings before hiding. """
        self.on_hide()
        # self.closeEvent(event)
        super(TakeManagerUI, self).hideEvent(event)

    def on_show(self, *args, **kwargs):
        """Refresh UI state when the window is shown"""
        self.refresh_ui_state()

    def on_hide(self, *args, **kwargs):
        """Refresh UI state when the window is hidden"""
        self.app.OnFileNewCompleted.Remove(self.on_file_new)
        self.app.OnFileOpenCompleted.Remove(self.on_file_open)

    def _setup_take_edit_group(self, parent_layout, *args, **kwargs):
        """Setup the Take Edit group containing frame controls and take operations
        
        Args:
            parent_layout (QLayout): Parent layout to add the group to
        """
        time_group = QtWidgets.QGroupBox("Take Edit")
        time_group.setStyleSheet("QGroupBox { font-weight: bold; color: yellow; }")
        time_vbox = QtWidgets.QVBoxLayout(time_group)
        
        # Setup frame range controls
        self._setup_frame_range_controls(time_vbox)
        
        # Setup take name controls
        self._setup_take_name_controls(time_vbox)
        
        # Add separator
        _add_separator_line(time_vbox)
        
        # Setup take operation buttons
        self._setup_take_operation_buttons(time_vbox)

        # Add separator
        _add_separator_line(time_vbox)

        # Setup Current Take group
        self._setup_current_take_group(time_vbox)
        
        parent_layout.addWidget(time_group)

    def _setup_frame_range_controls(self, parent_layout, *args, **kwargs):
        """Setup the frame range control widgets
        
        Args:
            parent_layout (QLayout): Parent layout to add the controls to
        """
        time_layout = QtWidgets.QHBoxLayout()

        # Start frame controls
        self.start_frame_label = QtWidgets.QLabel("Start Frame")
        self.start_frame_spinbox = QtWidgets.QSpinBox(minimum=0, maximum=9999)
        self.start_frame_button = QtWidgets.QPushButton("<")
        self.start_frame_button.setMaximumWidth(20)

        # End frame controls
        self.end_frame_label = QtWidgets.QLabel("End Frame")
        self.end_frame_spinbox = QtWidgets.QSpinBox(minimum=0, maximum=9999)
        self.end_frame_button = QtWidgets.QPushButton("<")
        self.end_frame_button.setMaximumWidth(20)

        # Add widgets to layout
        time_layout.addWidget(self.start_frame_label)
        time_layout.addWidget(self.start_frame_spinbox)
        time_layout.addWidget(self.start_frame_button)

        # Add spacer
        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        time_layout.addItem(_space_item)

        time_layout.addWidget(self.end_frame_label)
        time_layout.addWidget(self.end_frame_spinbox)
        time_layout.addWidget(self.end_frame_button)

        # Set stretch factors
        for i in range(7):
            time_layout.setStretch(i, 2)

        parent_layout.addLayout(time_layout)

    def _setup_take_name_controls(self, parent_layout, *args, **kwargs):
        """Setup the take name input and refresh controls
        
        Args:
            parent_layout (QLayout): Parent layout to add the controls to
        """
        name_layout = QtWidgets.QHBoxLayout()
        
        self.new_take_label = QtWidgets.QLabel("New Take Name:")
        self.new_take_lineedit = QtWidgets.QLineEdit("Take 001")
        self.refresh_button = QtWidgets.QPushButton("Refresh Info")
        # set refresh_button background color to green
        self.refresh_button.setStyleSheet("background-color: green")
        
        name_layout.addWidget(self.new_take_label)
        name_layout.addWidget(self.new_take_lineedit)
        name_layout.addWidget(self.refresh_button)
        
        name_layout.setStretch(0, 1)
        name_layout.setStretch(1, 10)
        name_layout.setStretch(2, 6)
        
        parent_layout.addLayout(name_layout)

    def _setup_take_operation_buttons(self, parent_layout, *args, **kwargs):
        """Setup the take operation buttons (crop, copy, etc.)
        
        Args:
            parent_layout (QLayout): Parent layout to add the buttons to
        """
        button_layout = QtWidgets.QHBoxLayout()
        
        # Stay checkbox and new take button
        self.stay_checkbox = QtWidgets.QCheckBox("Stay")
        self.crop_to_new_take_button = QtWidgets.QPushButton("Crop To New Take")
        button_layout.addWidget(self.stay_checkbox)
        button_layout.addWidget(self.crop_to_new_take_button)
        button_layout.setStretch(0, 1)
        button_layout.setStretch(1, 10)

        # Current take operations
        _add_separator_line(button_layout, direction="vertical")
        self.crop_current_take_button = QtWidgets.QPushButton("Crop On Current Take")
        button_layout.addWidget(self.crop_current_take_button)

        _add_separator_line(button_layout, direction="vertical")
        self.copy_current_take_button = QtWidgets.QPushButton("Copy Current Take")
        button_layout.addWidget(self.copy_current_take_button)

        button_layout.setStretch(3, 10)
        button_layout.setStretch(5, 10)

        parent_layout.addLayout(button_layout)

    def _setup_current_take_group(self, parent_layout, *args, **kwargs):
        """Setup the Current Take group containing navigation and delete controls
        
        Args:
            parent_layout (QLayout): Parent layout to add the group to
        """
        current_take_widget = QtWidgets.QWidget()
        current_take_layout = QtWidgets.QVBoxLayout(current_take_widget)

        # Navigation buttons
        nav_buttons_layout = QtWidgets.QHBoxLayout()
        self.previous_take_button = QtWidgets.QPushButton("<< Previous Take")
        self.next_take_button = QtWidgets.QPushButton("Next Take >>")
        nav_buttons_layout.addWidget(self.previous_take_button)
        nav_buttons_layout.addWidget(self.next_take_button)
        current_take_layout.addLayout(nav_buttons_layout)

        # Delete button
        self.delete_take_button = QtWidgets.QPushButton("Delete Current Take")
        current_take_layout.addWidget(self.delete_take_button)

        parent_layout.addWidget(current_take_widget)

    def _setup_take_list_group(self, parent_layout, center_widget, *args, **kwargs):
        """Setup the Take List group containing the list of takes

        Args:
            parent_layout (QLayout): Parent layout to add the group to
        """
        take_list_group = QtWidgets.QGroupBox("Rename Take List")
        take_list_group.setStyleSheet("QGroupBox { font-weight: bold; color: yellow; }")
        take_list_layout = QtWidgets.QVBoxLayout(take_list_group)

        gridLayout = QtWidgets.QGridLayout()

        scrollArea = QtWidgets.QScrollArea(center_widget)
        scrollArea.setWidgetResizable(True)

        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 395, 441))

        self.verticalLayout_new = QtWidgets.QVBoxLayout(self.scrollAreaWidgetContents)

        self.take_list_widget = QtWidgets.QListWidget(self.scrollAreaWidgetContents)
        self.take_list_widget.setGeometry(QtCore.QRect(10, 10, 371, 400))
        self.take_list_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)

        self.verticalLayout_new.addWidget(self.take_list_widget)

        scrollArea.setWidget(self.scrollAreaWidgetContents)
        gridLayout.addWidget(scrollArea, 0, 0, 1, 1)
        take_list_layout.addLayout(gridLayout)

        self._setup_prefix_controls(take_list_layout)
        self._setup_suffix_controls(take_list_layout)
        self._setup_replace_controls(take_list_layout)

        parent_layout.addWidget(take_list_group)

    def _create_text_control_layout(self, label_text, button_text):
        """
        create a layout with a label, a line edit, and a button
        Args:
            label_text (str): label text
            button_text (str): button text
        Returns:
            tuple: layout, label, line_edit, button
        """
        layout = QtWidgets.QHBoxLayout()
        label = QtWidgets.QLabel(label_text)
        line_edit = QtWidgets.QLineEdit()
        button = QtWidgets.QPushButton(button_text)

        layout.addWidget(label)
        layout.addWidget(line_edit)
        layout.addWidget(button)

        layout.setStretch(0, 1)
        layout.setStretch(1, 3)
        layout.setStretch(2, 4)

        return layout, label, line_edit, button

    def _setup_prefix_controls(self, parent_layout, *args, **kwargs):
        """
        setup prefix controls
        Args:
            parent_layout (QLayout): parent layout
        """
        prefix_layout, prefix_label, self.prefix_lineedit, self.prefix_button = \
            self._create_text_control_layout("Prefix:", "Apply")
        parent_layout.addLayout(prefix_layout)

    def _setup_suffix_controls(self, parent_layout, *args, **kwargs):
        """
        setup suffix controls
        Args:
            parent_layout (QLayout): parent layout

        """
        suffix_layout, suffix_label, self.suffix_lineedit, self.suffix_button = \
            self._create_text_control_layout("Suffix:", "Apply")
        parent_layout.addLayout(suffix_layout)

    def _setup_replace_controls(self, parent_layout, *args, **kwargs):
        """
        setup replace controls
        Args:
            parent_layout (QLayout): parent layout
        """
        replace_layout = QtWidgets.QHBoxLayout()
        search_label = QtWidgets.QLabel("Search:")
        self.search_lineedit = QtWidgets.QLineEdit()
        replace_label = QtWidgets.QLabel("Replace:")
        self.replace_lineedit = QtWidgets.QLineEdit()
        self.replace_button = QtWidgets.QPushButton("Apply")

        replace_layout.addWidget(search_label)
        replace_layout.addWidget(self.search_lineedit)
        replace_layout.addWidget(replace_label)
        replace_layout.addWidget(self.replace_lineedit)
        replace_layout.addWidget(self.replace_button)

        replace_layout.setStretch(0, 1)
        replace_layout.setStretch(1, 2)
        replace_layout.setStretch(2, 1)
        replace_layout.setStretch(3, 2)
        replace_layout.setStretch(4, 6)

        parent_layout.addLayout(replace_layout)

    def create_connections(self, *args, **kwargs):
        """Create signal-slot connections for all UI elements"""
        # Connect all button signals
        self.start_frame_button.clicked.connect(partial(self.update_start_frame))
        self.end_frame_button.clicked.connect(partial(self.update_end_frame))
        self.crop_to_new_take_button.clicked.connect(partial(self.create_new_take))
        self.crop_current_take_button.clicked.connect(partial(self.crop_current_take))
        self.copy_current_take_button.clicked.connect(partial(self.copy_current_take))
        self.previous_take_button.clicked.connect(partial(self.navigate_to_previous_take))
        self.next_take_button.clicked.connect(partial(self.navigate_to_next_take))
        self.delete_take_button.clicked.connect(partial(self.delete_current_take))
        self.refresh_button.clicked.connect(partial(self.refresh_all))

        self.take_list_widget.itemDoubleClicked.connect(partial(self.take_list_double_clicked))

        # add a context menu inside the list widget
        self.take_list_widget.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.take_list_widget.customContextMenuRequested.connect(partial(self.show_context_menu))

        self.prefix_button.clicked.connect(partial(self.add_prefix))
        self.suffix_button.clicked.connect(partial(self.add_suffix))
        self.replace_button.clicked.connect(partial(self.replace_text))

        self.app.OnFileNewCompleted.Add(self.on_file_new)
        self.app.OnFileOpenCompleted.Add(self.on_file_open)

    def on_file_new(self, *args, **kwargs):
        """Create a new take with the specified frame range"""
        self.take_edit = TakeEdit()
        self.refresh_all()

    def on_file_open(self, *args, **kwargs):
        """Open an existing take"""
        self.take_edit = TakeEdit()
        self.refresh_all()

    def show_context_menu(self, point, *args, **kwargs):
        """Show context menu when right-clicking on the list widget"""
        menu = QtWidgets.QMenu(self.take_list_widget)
        refresh_take_action = menu.addAction("Refresh Take List")
        refresh_take_action.triggered.connect(partial(self.refresh_take_list))

        clear_take_action = menu.addAction("Clear Take List")
        clear_take_action.triggered.connect(partial(self.clear_take_list))

        menu.exec_(self.take_list_widget.mapToGlobal(point))

    def refresh_ui_state(self, *args, **kwargs):
        """Update UI with current timeline and take information"""
        start_frame, stop_frame = PlaybackControl.get_time_range()
        self.start_frame_spinbox.setValue(start_frame)
        self.end_frame_spinbox.setValue(stop_frame)
        self.refresh_ui_take_name()

    def refresh_ui_take_name(self, *args, **kwargs):
        """Update UI with current take name"""
        take_name = fb.FBSystem().CurrentTake.Name
        self.new_take_lineedit.setText(take_name)

    def refresh_all(self, *args, **kwargs):
        """Refresh all UI elements"""
        self.refresh_ui_state()
        self.refresh_take_list()

    def update_start_frame(self, *args, **kwargs):
        """Update start frame with current timeline position"""
        self.take_edit = TakeEdit()
        frame = self.take_edit.get_current_frame()
        self.start_frame_spinbox.setValue(frame)

    def update_end_frame(self, *args, **kwargs):
        """Update end frame with current timeline position"""
        self.take_edit = TakeEdit()
        frame = self.take_edit.get_current_frame()
        self.end_frame_spinbox.setValue(frame)

    def create_new_take(self, *args, **kwargs):
        """Create a new take with the specified frame range"""
        take_edit = TakeEdit()
        take_edit.crop_to_new_take(
            str(self.new_take_lineedit.text()),
            self.start_frame_spinbox.value(),
            self.end_frame_spinbox.value(),
            self.stay_checkbox.isChecked()
        )
        self.refresh_all()

    def crop_current_take(self, *args, **kwargs):
        """Crop the current take to the specified frame range"""
        take_edit = TakeEdit()
        take_edit.crop_current_take(
            self.start_frame_spinbox.value(),
            self.end_frame_spinbox.value()
        )
        self.refresh_all()

    def navigate_to_previous_take(self, *args, **kwargs):
        """Switch to the previous take in the timeline"""
        self.take_edit = TakeEdit()
        self.take_edit.switch_to_previous_take()
        self.refresh_ui_state()

    def navigate_to_next_take(self, *args, **kwargs):
        """Switch to the next take in the timeline"""
        self.take_edit = TakeEdit()
        self.take_edit.switch_to_next_take()
        self.refresh_ui_state()

    def delete_current_take(self, *args, **kwargs):
        """Delete the current take from the timeline"""
        self.take_edit = TakeEdit()
        self.take_edit.delete_current_take()
        self.refresh_all()

    def copy_current_take(self, *args, **kwargs):
        """Create a copy of the current take"""
        take_name = str(self.new_take_lineedit.text())
        self.take_edit = TakeEdit()
        self.take_edit.copy_current_take(take_name)
        self.refresh_all()

    def take_list_double_clicked(self, item, *args, **kwargs):
        """Double-clicked on a take in the list"""
        take_name = str(item.text())
        self.take_edit = TakeEdit()
        self.take_edit.switch_to_take(take_name)
        self.refresh_all()

    def refresh_take_list(self, *args, **kwargs):
        """Refresh the list of takes"""
        self.take_list_widget.clear()
        take_names = TakeEdit.get_take_list()
        for take in take_names:
            self.take_list_widget.addItem(take)

    def clear_take_list(self, *args, **kwargs):
        """Clear the list of takes"""
        self.take_list_widget.clear()

    def get_take_list(self, *args, **kwargs):
        """Get the list of takes"""
        return [item.text() for item in self.take_list_widget.selectedItems()]

    def _validate_input(self, text, *args, **kwargs):
        """
        Validate if the input text is valid
        Args:
            text (str): Text to validate
        Returns:
            bool: Returns True if text is valid, otherwise False
        """
        return bool(text and text.strip())

    def _rename_takes(self, take_list, name_modifier, *args, **kwargs):
        """
        Rename multiple takes
        Args:
            take_list (list): List of takes to rename
            name_modifier (function): Function to modify take names
        """
        for take in take_list:
            try:
                new_take_name = name_modifier(take)
                TakeEdit.rename_take(take, new_take_name)
            except Exception as e:
                print(f"Error renaming take {take}: {str(e)}")

    def add_prefix(self, *args, **kwargs):
        """ Add prefix to the selected takes """
        prefix = str(self.prefix_lineedit.text())
        if not self._validate_input(prefix):
            return

        take_list = self.get_take_list()
        if not take_list:
            return

        self._rename_takes(take_list, lambda take: prefix + "_" + take)

        self.refresh_take_list()

    def add_suffix(self, *args, **kwargs):
        """ Add suffix to the selected takes """
        suffix = str(self.suffix_lineedit.text())
        if not self._validate_input(suffix):
            return

        take_list = self.get_take_list()
        if not take_list:
            return

        self._rename_takes(take_list, lambda take: take + "_" + suffix)

        self.refresh_take_list()

    def replace_text(self, *args, **kwargs):
        """ Replace text in the selected takes """
        search_text = str(self.search_lineedit.text())
        if not self._validate_input(search_text):
            return

        replace_text = str(self.replace_lineedit.text())

        take_list = self.get_take_list()
        if not take_list:
            return

        self._rename_takes(take_list, lambda take: take.replace(search_text, replace_text))

        self.refresh_take_list()
