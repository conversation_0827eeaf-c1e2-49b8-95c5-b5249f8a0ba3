from functools import partial

from Qt import QtWidgets, QtCore
from lsr.qt.core.base_main_window import get_window_class
from lsr.qt.core.widgets.mobu_widget import MB_Button, MB_Checkbox
from lsr.qt.core.widgets.extensible_widget import ExtensibleWidget

import lsr.mobu.rig.file_options as file_options
from lsr.mobu.tool_lib.MoEditTool.AnimTakeEdit import TakeEdit
from lsr.mobu.tool_lib.MoEditTool.CharacterEdit import CharEdit
from lsr.mobu.tool_lib.MoEditTool.SpeedCheck import CheckBoneSpeed
from lsr.mobu.tool_lib.MoEditTool.GenerateCurve import GenerateCurve
from lsr.mobu.tool_lib.MoEditTool.batch_rm_input import BatchRmInput
import lsr.mobu.tool_lib.MoEditTool.AdjustmentBlend as AdjustmentBlend

from pyfbsdk import FBApplication, FBMessageBox


# #------Debug------#
# import lsr.mobu.tool_lib.MoEditTool.AnimTakeEdit
# reload(lsr.mobu.tool_lib.MoEditTool.AnimTakeEdit)
# from lsr.mobu.tool_lib.MoEditTool.AnimTakeEdit import TakeEdit
#
# import lsr.mobu.tool_lib.MoEditTool.CharacterEdit
# reload(lsr.mobu.tool_lib.MoEditTool.CharacterEdit)
# from lsr.mobu.tool_lib.MoEditTool.CharacterEdit import CharEdit
#
# import lsr.mobu.tool_lib.MoEditTool.SpeedCheck
# reload(lsr.mobu.tool_lib.MoEditTool.SpeedCheck)
# from lsr.mobu.tool_lib.MoEditTool.SpeedCheck import CheckBoneSpeed
# #-----------------#

# get the base main window class
base_class = get_window_class(app_name='MoEdit_Tool_UI v3.5')


# Todo List
# 1- frame range and text check on UI
# 2- sth wrong with spin box num range(only two-digit)
# 3- get root and pelvis from xml
# 4- CharacterEdit-fix_root_rotation, delete temp constraint with "Parent/Child", not proper
# 5- can't input negative number for Degrees
# 6- PlotAllTakes is invalid (always all)
# 7- create root motion-z is selectable (check)

class MoEdit_Tool_UI(base_class):
    """ This is the main window for the MoEdit Tool. """

    _REUSE_SINGLETON = False

    def __init__(self):
        """ Creates and initializes this window. """
        self.__thread = None
        self.__cur_progress_max = 0
        self.__save_path = None
        super(MoEdit_Tool_UI, self).__init__()

        self.FBX_FILE_FILTERS = "FBX (*.fbx *.FBX);;FBX (*.FBX);;fbx (*.fbx);;All Files (*.*)"
        self.FBX_selected_filter = "FBX (*.fbx,*.FBX)"
        self.json_selected_filter = "Json (*.json)"

        self.take_edit = TakeEdit()
        self.char_edit = CharEdit()
        self.check_bone_speed = CheckBoneSpeed()
        self.generate_curve = GenerateCurve()

        self.create_connections()

    def setup_ui(self):
        """Creates UI elements."""
        self.resize(635, 512)
        centralwidget = QtWidgets.QWidget(self)
        parent_vbox = QtWidgets.QVBoxLayout(centralwidget)
        self.tabWidget = QtWidgets.QTabWidget(centralwidget)

        # 1st tab
        self.cr_tab = QtWidgets.QWidget()
        cr_tab_vbox = QtWidgets.QVBoxLayout(self.cr_tab)
        self.scroll_area = QtWidgets.QScrollArea()
        cr_tab_vbox.addWidget(self.scroll_area)
        self.scroll_area.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOn)
        self.scroll_area.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)

        self.scroll_content = QtWidgets.QWidget()
        self.scroll_area.setWidget(self.scroll_content)
        self.scroll_area.setWidgetResizable(True)

        scroll_vbox = QtWidgets.QVBoxLayout(self.scroll_content)

        # 1st tab-character part
        cha_frame = ExtensibleWidget("Character Edit")
        cha_gbox = QtWidgets.QGroupBox(self.cr_tab)
        cha_gbox.setTitle('Character')

        cha_gbox_vbox = QtWidgets.QVBoxLayout(cha_gbox)

        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        cha_gbox_vbox.addItem(_space_item)

        self.sc_hbox = QtWidgets.QHBoxLayout()
        self.sc_btn = MB_Button(cha_gbox)
        self.sc_btn.setText('Skeleton / Control Rig')
        self.sc_hbox.addWidget(self.sc_btn)
        self.sc_CB = MB_Checkbox(cha_gbox)
        self.sc_CB.setText("All")
        self.sc_hbox.addWidget(self.sc_CB)
        self.sc_hbox.setStretch(0, 10)
        self.sc_hbox.setStretch(1, 1)
        cha_gbox_vbox.addLayout(self.sc_hbox)
        self.selCtrl_btn = MB_Button(cha_gbox)
        self.selCtrl_btn.setText('Select All Control Rig')
        cha_gbox_vbox.addWidget(self.selCtrl_btn)

        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        cha_gbox_vbox.addItem(_space_item)

        self.mirror_btn = MB_Button(cha_gbox)
        self.mirror_btn.setText('Mirror Animation')
        cha_gbox_vbox.addWidget(self.mirror_btn)
        self.adjust_btn = MB_Button(cha_gbox)
        self.adjust_btn.setText('Adjustment Blend')
        cha_gbox_vbox.addWidget(self.adjust_btn)
        cha_frame.add_widget(cha_gbox)

        scroll_vbox.addWidget(cha_frame)

        # 2 tab-animTake part
        anim_frame = ExtensibleWidget("Anim Edit")
        func_gbox = QtWidgets.QGroupBox(self.cr_tab)
        func_gbox.setTitle('Anim Take Edit')
        func_gbox.setMinimumHeight(330)
        func_gbox_vbox = QtWidgets.QVBoxLayout(func_gbox)

        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        func_gbox_vbox.addItem(_space_item)

        time_hbox = QtWidgets.QHBoxLayout()
        startFrame_LB = QtWidgets.QLabel(func_gbox)
        startFrame_LB.setText('Start Frame')
        time_hbox.addWidget(startFrame_LB)
        self.start_SB = QtWidgets.QSpinBox(func_gbox, minimum=0, maximum=9999)
        time_hbox.addWidget(self.start_SB)
        self.start_btn = MB_Button(func_gbox)
        self.start_btn.setText('<')
        self.start_btn.setMaximumWidth(20)
        time_hbox.addWidget(self.start_btn)
        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        time_hbox.addItem(_space_item)
        endFrame_LB = QtWidgets.QLabel(func_gbox)
        endFrame_LB.setText('End Frame')
        time_hbox.addWidget(endFrame_LB)
        self.end_SB = QtWidgets.QSpinBox(func_gbox, minimum=0, maximum=9999)
        time_hbox.addWidget(self.end_SB)
        self.end_btn = MB_Button(func_gbox)
        self.end_btn.setText('<')
        self.end_btn.setMaximumWidth(20)
        time_hbox.addWidget(self.end_btn)
        time_hbox.setStretch(0, 2)
        time_hbox.setStretch(1, 2)
        time_hbox.setStretch(2, 2)
        time_hbox.setStretch(3, 2)
        time_hbox.setStretch(4, 2)
        time_hbox.setStretch(5, 2)
        time_hbox.setStretch(5, 2)
        func_gbox_vbox.addLayout(time_hbox)
        newTake_hbox = QtWidgets.QHBoxLayout()
        newTake_LB = QtWidgets.QLabel(func_gbox)
        newTake_LB.setText('New Take:')
        newTake_hbox.addWidget(newTake_LB)
        self.newTake_LineEdit = QtWidgets.QLineEdit(func_gbox)
        self.newTake_LineEdit.setText('Take 001')
        newTake_hbox.addWidget(self.newTake_LineEdit)
        func_gbox_vbox.addLayout(newTake_hbox)

        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        func_gbox_vbox.addItem(_space_item)

        newCrop_hbox = QtWidgets.QHBoxLayout()
        self.newCrop_btn = MB_Button(func_gbox)
        self.newCrop_btn.setText('Crop To New Take')
        newCrop_hbox.addWidget(self.newCrop_btn)
        self.nc_CB = MB_Checkbox(func_gbox)
        self.nc_CB.setText("Stay")
        newCrop_hbox.addWidget(self.nc_CB)
        func_gbox_vbox.addLayout(newCrop_hbox)
        newCrop_hbox.setStretch(0, 10)
        newCrop_hbox.setStretch(1, 2)
        self.curCrop_btn = MB_Button(func_gbox)
        self.curCrop_btn.setText('Crop On Current Take')
        func_gbox_vbox.addWidget(self.curCrop_btn)

        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        func_gbox_vbox.addItem(_space_item)

        take_order_hbox = QtWidgets.QHBoxLayout()
        self.preTake_btn = MB_Button(func_gbox)
        self.preTake_btn.setText('<<  Previous Take')
        take_order_hbox.addWidget(self.preTake_btn)

        self.nxtTake_btn = MB_Button(func_gbox)
        self.nxtTake_btn.setText('Next Take  >>')
        take_order_hbox.addWidget(self.nxtTake_btn)

        func_gbox_vbox.addLayout(take_order_hbox)

        self.delTake_btn = MB_Button(func_gbox)
        self.delTake_btn.setText('Delete Current Take')
        func_gbox_vbox.addWidget(self.delTake_btn)

        anim_frame.add_widget(func_gbox)
        scroll_vbox.addWidget(anim_frame)

        # 3 tab-root part
        root_frame = ExtensibleWidget("Root Edit")
        fix_gbox = QtWidgets.QGroupBox(self.cr_tab)
        fix_gbox_vbox = QtWidgets.QVBoxLayout(fix_gbox)
        fix_gbox.setTitle('Root Edit')

        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        fix_gbox_vbox.addItem(_space_item)

        # -------------------------------base for root rotation------------------------------
        # root rotation
        self.create_root_rotation_cb = QtWidgets.QCheckBox(self)
        self.create_root_rotation_cb.setText("Create RootRotation")
        fix_gbox_vbox.addWidget(self.create_root_rotation_cb)

        self.batch_create_root_motion_cb = QtWidgets.QCheckBox(self)
        self.batch_create_root_motion_cb.setText("Batch Create RootMotion")
        fix_gbox_vbox.addWidget(self.batch_create_root_motion_cb)

        # root rotation aim setting
        self.root_rotation_grp_box = QtWidgets.QGroupBox(self)
        self.root_rotation_grp_box.setTitle("Root Rotation Aim Setting")
        fix_gbox_vbox.addWidget(self.root_rotation_grp_box)

        self.aim_axis_btn_box = QtWidgets.QButtonGroup(self)
        self.up_axis_btn_box = QtWidgets.QButtonGroup(self)
        self.aim_axis_btn_dict = dict()
        self.up_axis_btn_dict = dict()
        box_lay = QtWidgets.QVBoxLayout(self.root_rotation_grp_box)
        box_lay.addWidget(QtWidgets.QLabel('Aim Axis', self))

        box_aim_axis_plus_hlay = QtWidgets.QHBoxLayout()
        box_aim_axis_minus_hlay = QtWidgets.QHBoxLayout()
        for btn_names, btn_layout in zip([['x', 'y', 'z'], ['-x', '-y', '-z']],
                                         [box_aim_axis_plus_hlay, box_aim_axis_minus_hlay]):
            for btn_name in btn_names:
                btn = QtWidgets.QRadioButton(btn_name, self)
                self.aim_axis_btn_dict[btn] = btn_name
                self.aim_axis_btn_box.addButton(btn)
                btn_layout.addWidget(btn)
            box_lay.addLayout(btn_layout)

        box_lay.addWidget(QtWidgets.QLabel('Object Up Axis(World Up)', self))
        box_up_axis_plus_hlay = QtWidgets.QHBoxLayout()
        box_up_axis_minus_hlay = QtWidgets.QHBoxLayout()
        for btn_names, btn_layout in zip([['x', 'y', 'z'], ['-x', '-y', '-z']],
                                         [box_up_axis_plus_hlay, box_up_axis_minus_hlay]):
            for btn_name in btn_names:
                btn = QtWidgets.QRadioButton(btn_name, self)
                self.up_axis_btn_dict[btn] = btn_name
                self.up_axis_btn_box.addButton(btn)
                btn_layout.addWidget(btn)
            box_lay.addLayout(btn_layout)

        # btn selection init
        for btn, value in self.aim_axis_btn_dict.items():
            if value == '-y':
                btn.setChecked(True)
        for btn, value in self.up_axis_btn_dict.items():
            if value == 'z':
                btn.setChecked(True)
        # -------------------------------base for root rotation------------------------------
        self.batch_rm_widget = BatchRmInput(self)
        fix_gbox_vbox.addWidget(self.batch_rm_widget)

        self.root_hbox = QtWidgets.QHBoxLayout()
        self.root_btn = MB_Button(fix_gbox)
        self.root_btn.setText('Create Root Rotation')
        fix_gbox_vbox.addWidget(self.root_btn)

        self.og_CB = MB_Checkbox(fix_gbox)
        self.og_CB.setText("Keep on Ground")
        self.og_CB.setChecked(True)
        self.root_hbox.addWidget(self.og_CB)
        self.fs_CB = MB_Checkbox(fix_gbox)
        self.fs_CB.setText("Follow Selected")
        self.root_hbox.addWidget(self.fs_CB)
        self.apply_all_take_CB = MB_Checkbox(fix_gbox)
        self.apply_all_take_CB.setText("Apply All Takes")
        self.root_hbox.addWidget(self.apply_all_take_CB)
        self.root_hbox.setStretch(0, 1)
        self.root_hbox.setStretch(1, 1)
        self.root_hbox.setStretch(1, 1)
        fix_gbox_vbox.addLayout(self.root_hbox)

        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        fix_gbox_vbox.addItem(_space_item)

        self.fix_hbox = QtWidgets.QHBoxLayout()
        degree_Label = QtWidgets.QLabel(fix_gbox)
        degree_Label.setText('Degrees')
        self.fix_hbox.addWidget(degree_Label)
        self.degree_DSB = QtWidgets.QDoubleSpinBox(fix_gbox, minimum=-999, maximum=999)
        self.fix_hbox.addWidget(self.degree_DSB)
        self.degree_btn = MB_Button(fix_gbox)
        self.degree_btn.setText('Rotate')
        self.fix_hbox.addWidget(self.degree_btn)
        self.fix_hbox.setStretch(0, 1)
        self.fix_hbox.setStretch(1, 1)
        self.fix_hbox.setStretch(2, 3)
        fix_gbox_vbox.addLayout(self.fix_hbox)
        self.rep_btn = MB_Button(fix_gbox)
        self.rep_btn.setText('Re-Position To Zero of WorldSpace')
        fix_gbox_vbox.addWidget(self.rep_btn)

        root_frame.add_widget(fix_gbox)
        scroll_vbox.addWidget(root_frame)

        # 4 tab-speed part
        speed_frame = ExtensibleWidget("Speed Edit")
        speed_gbox = QtWidgets.QGroupBox(self.cr_tab)
        speed_gbox.setTitle('Speed')
        speed_gbox_vbox = QtWidgets.QVBoxLayout(speed_gbox)

        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        speed_gbox_vbox.addItem(_space_item)

        self.speedCheck_btn = MB_Button(speed_gbox)
        self.speedCheck_btn.setText('Speed Check')
        speed_gbox_vbox.addWidget(self.speedCheck_btn)

        self.print_speed_btn = MB_Button(speed_gbox)
        self.print_speed_btn.setText('Print Speed')
        speed_gbox_vbox.addWidget(self.print_speed_btn)

        self.clearRef_btn = MB_Button(speed_gbox)
        self.clearRef_btn.setText('Clear Ref Null')
        speed_gbox_vbox.addWidget(self.clearRef_btn)
        self.generateCurve_btn = MB_Button(speed_gbox)
        self.generateCurve_btn.setText('Generate Velocity Curve')
        speed_gbox_vbox.addWidget(self.generateCurve_btn)

        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        speed_gbox_vbox.addItem(_space_item)

        speed_frame.add_widget(speed_gbox)
        scroll_vbox.addWidget(speed_frame)

        # add a space item v direction
        _space_item = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        scroll_vbox.addItem(_space_item)

        self.tabWidget.addTab(self.cr_tab, 'Base')
        parent_vbox.addWidget(self.tabWidget)

        self.setCentralWidget(centralwidget)

        # self.re-translate Ui(MainWindow)
        self.tabWidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(self)

    def create_connections(self):
        """create connections for all buttons"""
        # anim take edit part
        self.start_btn.clicked.connect(partial(self.reset_start_time))
        self.end_btn.clicked.connect(partial(self.reset_end_time))
        self.newCrop_btn.clicked.connect(partial(self.crop_to_new_take))
        self.curCrop_btn.clicked.connect(partial(self.crop_on_current_take))
        self.preTake_btn.clicked.connect(partial(self.go_to_prev_take))
        self.nxtTake_btn.clicked.connect(partial(self.go_to_next_take))
        self.delTake_btn.clicked.connect(partial(self.remove_current_take))

        # root edit part
        self.degree_btn.clicked.connect(partial(self.rotate_root))
        self.rep_btn.clicked.connect(partial(self.reposition_root))

        # char edit part
        self.sc_btn.clicked.connect(partial(self.bake_char))
        self.root_btn.clicked.connect(partial(self.create_root_motion))
        self.mirror_btn.clicked.connect(partial(self.mirror_anim))
        self.selCtrl_btn.clicked.connect(partial(self.select_control_rig))
        self.adjust_btn.clicked.connect(partial(self.adjustment_blend))

        # speed check part
        self.speedCheck_btn.clicked.connect(partial(self.check_speed))
        self.print_speed_btn.clicked.connect(partial(self.print_speed))
        self.clearRef_btn.clicked.connect(partial(self.clear_ref_null))
        self.generateCurve_btn.clicked.connect(partial(self.do_generate_curve))

    def paintEvent(self, event):
        super(MoEdit_Tool_UI, self).paintEvent(event)
        if self.create_root_rotation_cb.isChecked():
            self.root_rotation_grp_box.setVisible(True)
        else:
            self.root_rotation_grp_box.setVisible(False)

        if self.batch_create_root_motion_cb.isChecked():
            self.root_btn.setText('Batch Create Root Motion')
            self.batch_rm_widget.setVisible(True)
        else:
            self.root_btn.setText('Create Root Motion')
            self.batch_rm_widget.setVisible(False)

    def reset_start_time(self, *args, **kwargs):
        """Reset start time to current frame."""
        frame = self.take_edit.get_current_frame()
        self.start_SB.setValue(frame)

    def reset_end_time(self, *args, **kwargs):
        """Reset end time to current frame."""
        frame = self.take_edit.get_current_frame()
        self.end_SB.setValue(frame)

    def crop_to_new_take(self, *args, **kwargs):
        """Crop current take to a new take."""
        self.take_edit = TakeEdit()
        self.take_edit.crop_to_new_take(
            str(self.newTake_LineEdit.text()),
            self.start_SB.value(),
            self.end_SB.value(),
            self.nc_CB.isChecked()
        )

    def crop_on_current_take(self, *args, **kwargs):
        """Crop current take on current take."""
        self.take_edit = TakeEdit()
        self.take_edit.crop_current_take(
            self.start_SB.value(),
            self.end_SB.value()
        )

    def go_to_prev_take(self, *args, **kwargs):
        """Go to previous take."""
        self.take_edit.switch_to_previous_take()

    def go_to_next_take(self, *args, **kwargs):
        """Go to next take."""
        self.take_edit.switch_to_next_take()

    def remove_current_take(self, *args, **kwargs):
        """Remove current take."""
        self.take_edit = TakeEdit()
        self.take_edit.delete_current_take()

    def rotate_root(self, *args, **kwargs):
        """rotate the root of the character"""
        self.char_edit = CharEdit()
        self.char_edit.fix_char_root_rotation(self.degree_DSB.value())

    def reposition_root(self, *args, **kwargs):
        """ reposition the character """
        self.char_edit = CharEdit()
        self.char_edit.char_reposition()

    def bake_char(self, *args, **kwargs):
        """ bake the character """
        # sth wrong
        self.char_edit = CharEdit()
        self.char_edit.bake_btw_skeleton_and_control_rig(self.sc_CB.isChecked())

    def create_root_motion(self, *args, **kwargs):
        """ create root motion for the character """
        def create_char_root_motion():
            self.char_edit.create_char_root_motion(self.apply_all_take_CB.isChecked(),
                                                   self.fs_CB.isChecked(),
                                                   self.og_CB.isChecked(),
                                                   self.create_root_rotation_cb.isChecked(),
                                                   [value for btn, value in self.aim_axis_btn_dict.items() if
                                                    btn.isChecked()][0],
                                                   [value for btn, value in self.up_axis_btn_dict.items() if
                                                    btn.isChecked()][0])

        if self.batch_create_root_motion_cb.isChecked():
            batch_files = self.batch_rm_widget.get_batch_files()
            app = FBApplication()
            for batch_file in batch_files:
                batch_file = str(batch_file)
                app.FileOpen(batch_file, False)
                create_char_root_motion()
                save_options = file_options.SaveAllOption(False)
                app.FileSave(batch_file, save_options)
        else:
            create_char_root_motion()
        FBMessageBox("Creation Finish", "Root motion created finish!", "OK")

    def mirror_anim(self, *args, **kwargs):
        """ mirror the animation of the character """
        self.char_edit = CharEdit()
        self.char_edit.mirror_char_anim()

    @staticmethod
    def adjustment_blend(*args, **kwargs):
        """adjust the movement of the character"""
        AdjustmentBlend.AdjustmentBlendCharacter()

    def select_control_rig(self, *args, **kwargs):
        """select the control rig of the character"""
        self.char_edit = CharEdit()
        self.char_edit.select_all_ctrl()

    def clear_control_rig(self, *args, **kwargs):
        """clear the control rig of the character"""
        # TODO: clear the control rig of the character
        pass

    def check_speed(self, *args, **kwargs):
        """check the speed of the character"""
        self.check_bone_speed.get_speed()

    def print_speed(self, *args, **kwargs):
        """print the speed of the character"""
        CheckBoneSpeed.print_speed()

    def clear_ref_null(self, *args, **kwargs):
        """clear the reference null of the character"""
        self.check_bone_speed.clear_ref_null()

    def do_generate_curve(self, *args, **kwargs):
        """generate the velocity curve of the character"""
        self.generate_curve.generate_curve()
