import os

import pyfbsdk as fb

import lsr.mobu.tool_lib.file_ref_adv.NamespaceTableModel as NamespaceTableModel
from lsr.mobu.utils.FindObjects import mobu_print
from lsr.qt.core import QtWidgets, QtCore
from lsr.qt.core.base_main_window import get_window_class

base_class = get_window_class(app_name='File_Reference_Advanced_Tool v1.0')


def pick_ref_name(*args, **kwargs):
    """ Pick a file reference name """
    file_ref_name = ''
    btn_clicked, file_ref_name = fb.FBMessageBoxGetUserValue(
        'File Reference',
        'Enter your file reference name',
        '',
        fb.FBPopupInputType.kFBPopupString,
        'OK',
        'Cancel',
        None,
        1,
        True)

    if btn_clicked == 2:
        return btn_clicked, file_ref_name

    file_ref_name = file_ref_name.replace(' ', '')
    while file_ref_name == '':
        fb.FBMessageBox(
            'File Reference',
            'Error, please enter a file reference name',
            "OK")
        btn_clicked, file_ref_name = pick_ref_name()
        if btn_clicked == 2:
            break

    return btn_clicked, file_ref_name


class File_Reference_Advanced_Tool_UI(base_class):
    """
    The main animation ExportTool_UI UI
    """
    _REUSE_SINGLETON = True

    # _TRACK_SETTINGS = True

    def __init__(self, *args, **kwargs):
        """ Creates and initializes this window. """
        self.__thread = None
        self.__cur_progress_max = 0
        self.__save_path = None
        super(File_Reference_Advanced_Tool_UI, self).__init__()

        self.ns_tree_widget.header().resizeSection(0, 300)

        self.mSingleSelection = False

        self.system = fb.FBSystem()
        self.app = fb.FBApplication()

        self.mNSModel = NamespaceTableModel.NamespaceTableModel(self)
        self.ns_tabel_view.setModel(self.mNSModel)

        self.mTimer = QtCore.QTimer(self)

        self.mIsInitialized = False
        self.Init()

        self.DialogTitle = 'Reference Tool'

        self.create_connections()

        # self.OnShow()

    def setup_ui(self, *args, **kwargs):
        """Creates UI elements."""
        self.setWindowTitle("File_Reference_Advanced_Tool_UI")

        self.centralwidget = QtWidgets.QWidget(self)
        vbox = QtWidgets.QVBoxLayout(self.centralwidget)
        vbox.setSpacing(3)
        vbox.setContentsMargins(5, 5, 5, 5)

        self.verticalLayout = QtWidgets.QVBoxLayout()

        vbox.addLayout(self.verticalLayout)

        # ===== reference a file
        self.groupBox = QtWidgets.QGroupBox('Reference a File')
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.groupBox)
        self.uiEditFilePath = QtWidgets.QLineEdit(self.groupBox)
        self.horizontalLayout.addWidget(self.uiEditFilePath)
        self.uiBtnBrowsePath = QtWidgets.QPushButton('...', self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(
            QtWidgets.QSizePolicy.Fixed,
            QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(
            self.uiBtnBrowsePath.sizePolicy().hasHeightForWidth())
        self.uiBtnBrowsePath.setSizePolicy(sizePolicy)
        self.uiBtnBrowsePath.setMaximumSize(QtCore.QSize(23, 16777215))
        self.uiBtnBrowsePath.setAutoDefault(False)
        self.horizontalLayout.addWidget(self.uiBtnBrowsePath)
        spacerItem = QtWidgets.QSpacerItem(
            10, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.uiSpinLoadTimes = QtWidgets.QSpinBox(self.groupBox)
        self.uiSpinLoadTimes.setEnabled(True)
        self.uiSpinLoadTimes.setMinimum(1)
        self.uiSpinLoadTimes.setMaximum(999)
        self.uiSpinLoadTimes.setProperty("value", 1)
        self.horizontalLayout.addWidget(self.uiSpinLoadTimes)

        self.uiBtnLoad = QtWidgets.QPushButton('Load', self.groupBox)
        self.uiBtnLoad.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(
            QtWidgets.QSizePolicy.Fixed,
            QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(
            self.uiBtnLoad.sizePolicy().hasHeightForWidth())
        self.uiBtnLoad.setSizePolicy(sizePolicy)
        self.uiBtnLoad.setMaximumSize(QtCore.QSize(50, 16777215))

        self.horizontalLayout.addWidget(self.uiBtnLoad)
        self.verticalLayout.addWidget(self.groupBox)
        # ===== end of reference a file

        # ===== namespaces
        self.ns_group = QtWidgets.QGroupBox('Namespaces')
        sizePolicy = QtWidgets.QSizePolicy(
            QtWidgets.QSizePolicy.Preferred,
            QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(
            self.ns_group.sizePolicy().hasHeightForWidth())
        self.ns_group.setSizePolicy(sizePolicy)

        ns_hbox = QtWidgets.QHBoxLayout(self.ns_group)

        tree_vbox = QtWidgets.QVBoxLayout()

        self.ns_tabel_view = QtWidgets.QTableView(self.ns_group)
        self.ns_tabel_view.setMinimumSize(QtCore.QSize(400, 0))
        self.ns_tabel_view.setEditTriggers(
            QtWidgets.QAbstractItemView.DoubleClicked | QtWidgets.QAbstractItemView.EditKeyPressed)
        self.ns_tabel_view.setTabKeyNavigation(False)
        self.ns_tabel_view.setSelectionMode(
            QtWidgets.QAbstractItemView.ExtendedSelection)
        self.ns_tabel_view.setSelectionBehavior(
            QtWidgets.QAbstractItemView.SelectRows)
        self.ns_tabel_view.horizontalHeader().setHighlightSections(False)
        self.ns_tabel_view.horizontalHeader().setSortIndicatorShown(False)
        self.ns_tabel_view.horizontalHeader().setStretchLastSection(True)
        self.ns_tabel_view.verticalHeader().setVisible(True)
        self.ns_tabel_view.verticalHeader().setCascadingSectionResizes(False)
        tree_vbox.addWidget(self.ns_tabel_view)

        self.ns_tree_widget = QtWidgets.QTreeWidget(self.ns_group)
        self.ns_tree_widget.setEditTriggers(
            QtWidgets.QAbstractItemView.NoEditTriggers)
        self.ns_tree_widget.setAlternatingRowColors(True)
        self.ns_tree_widget.header().setCascadingSectionResizes(False)
        self.ns_tree_widget.header().setStretchLastSection(True)
        self.ns_tree_widget.headerItem().setText(0, "Name")
        self.ns_tree_widget.headerItem().setText(1, "Type")
        tree_vbox.addWidget(self.ns_tree_widget)

        ns_hbox.addLayout(tree_vbox)

        # ===== functions
        func_vbox = QtWidgets.QVBoxLayout()
        func_group = QtWidgets.QGroupBox(self.ns_group)
        func_group.setTitle("Functions")

        func_grp_vbox = QtWidgets.QVBoxLayout(func_group)

        self.uiBtnUnload = QtWidgets.QPushButton('Unload', func_group)
        self.uiBtnUnload.setCheckable(False)
        func_grp_vbox.addWidget(self.uiBtnUnload)

        self.uiBtnReload = QtWidgets.QPushButton('Reload', func_group)
        self.uiBtnReload.setCheckable(False)
        func_grp_vbox.addWidget(self.uiBtnReload)

        self.uiBtnDelete = QtWidgets.QPushButton('Delete', func_group)
        self.uiBtnDelete.setCheckable(False)
        func_grp_vbox.addWidget(self.uiBtnDelete)

        self.uiBtnExport = QtWidgets.QPushButton('Export', func_group)
        self.uiBtnExport.setCheckable(False)
        func_grp_vbox.addWidget(self.uiBtnExport)

        spacerItem1 = QtWidgets.QSpacerItem(
            20, 7, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        func_grp_vbox.addItem(spacerItem1)

        self.line = QtWidgets.QFrame(func_group)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        func_grp_vbox.addWidget(self.line)
        spacerItem2 = QtWidgets.QSpacerItem(
            20, 7, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        func_grp_vbox.addItem(spacerItem2)

        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.uiSpinInstanceTimes = QtWidgets.QSpinBox(func_group)
        self.uiSpinInstanceTimes.setMinimum(1)
        self.uiSpinInstanceTimes.setMaximum(999)
        self.uiSpinInstanceTimes.setProperty("value", 1)
        self.horizontalLayout_2.addWidget(self.uiSpinInstanceTimes)
        self.uiBtnInstance = QtWidgets.QPushButton('Instance', func_group)
        self.horizontalLayout_2.addWidget(self.uiBtnInstance)
        func_grp_vbox.addLayout(self.horizontalLayout_2)
        spacerItem3 = QtWidgets.QSpacerItem(
            20, 7, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        func_grp_vbox.addItem(spacerItem3)
        self.line_3 = QtWidgets.QFrame(func_group)
        self.line_3.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_3.setFrameShadow(QtWidgets.QFrame.Sunken)
        func_grp_vbox.addWidget(self.line_3)
        spacerItem4 = QtWidgets.QSpacerItem(
            20, 7, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        func_grp_vbox.addItem(spacerItem4)

        spacerItem5 = QtWidgets.QSpacerItem(
            20, 7, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        func_grp_vbox.addItem(spacerItem5)

        spacerItem6 = QtWidgets.QSpacerItem(
            20, 7, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        func_grp_vbox.addItem(spacerItem6)

        spacerItem7 = QtWidgets.QSpacerItem(
            20, 7, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        func_grp_vbox.addItem(spacerItem7)
        line_frame = QtWidgets.QFrame(func_group)
        line_frame.setFrameShape(QtWidgets.QFrame.HLine)
        line_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
        func_grp_vbox.addWidget(line_frame)
        spacerItem8 = QtWidgets.QSpacerItem(
            20, 7, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        func_grp_vbox.addItem(spacerItem8)

        self.uiBtnShowEdits = QtWidgets.QPushButton(
            'Show Edits in Popup', func_group)
        self.uiBtnShowEdits.setCheckable(False)
        func_grp_vbox.addWidget(self.uiBtnShowEdits)
        spacerItem9 = QtWidgets.QSpacerItem(
            20, 7, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        func_grp_vbox.addItem(spacerItem9)

        func_vbox.addWidget(func_group)
        spacerItem10 = QtWidgets.QSpacerItem(
            20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        func_vbox.addItem(spacerItem10)
        ns_hbox.addLayout(func_vbox)
        self.verticalLayout.addWidget(self.ns_group)

        QtCore.QMetaObject.connectSlotsByName(self)

        self.setCentralWidget(self.centralwidget)

    def showEvent(self, event, *args, **kwargs):
        """Save settings before showing."""
        super(File_Reference_Advanced_Tool_UI, self).showEvent(event)
        self.OnShow()

    def close_instances(self, event, *args, **kwargs):
        """Save settings before closing."""
        mobu_print('Closing File_Reference_Advanced_Tool_UI')
        self.OnHide()
        super(File_Reference_Advanced_Tool_UI, self).closeEvent(event)

        self.close()

        # self.hideEvent(event)

    def hideEvent(self, event, *args, **kwargs):
        """ Save settings before hiding. """
        mobu_print('Hiding File_Reference_Advanced_Tool_UI')
        self.OnHide()
        # self.closeEvent(event)
        super(File_Reference_Advanced_Tool_UI, self).hideEvent(event)

    def create_connections(self, *args, **kwargs):
        """
        Creates connections between UI elements.

        Returns:

        """
        self.uiBtnBrowsePath.clicked.connect(self.OnBtnBrowsePathClicked)
        self.uiBtnLoad.clicked.connect(self.OnBtnLoadClicked)
        self.uiBtnUnload.clicked.connect(self.OnBtnUnloadClicked)
        self.uiBtnReload.clicked.connect(self.OnBtnReloadClicked)
        self.uiBtnDelete.clicked.connect(self.OnBtnDeleteClicked)
        self.uiBtnExport.clicked.connect(self.OnBtnExportClicked)
        self.uiBtnInstance.clicked.connect(self.OnBtnInstanceClicked)
        self.uiBtnShowEdits.clicked.connect(self.OnBtnShowEditsClicked)

    def Init(self, *args, **kwargs):
        """ Initialize the UI """
        self.mNSModel.namespaceRenamed.connect(self.OnNamespaceRenamed)
        self.mNSModel.namespaceFilePathSwapped.connect(
            self.OnNamespaceFilePathSwapped)

        lSelectionModel = self.ns_tabel_view.selectionModel()
        lSelectionModel.selectionChanged.connect(self.OnTableNamespaceSelectionChanged)

        self.app.OnFileNewCompleted.Add(self.OnFileNew)
        self.app.OnFileOpenCompleted.Add(self.OnFileOpen)

        self.mTimer.timeout.connect(self.OnTimer)
        self.mTimer.start(2000)

        self.UpdateUI()

        self.mIsInitialized = True

    def Finish(self, *args, **kwargs):
        """ Finish the UI """
        self.mTimer.stop()
        self.mTimer.timeout.disconnect(self.OnTimer)

        # disconnecting the selectionChanged signal
        lSelectionModel = self.ns_tabel_view.selectionModel()
        lSelectionModel.selectionChanged.disconnect(
            self.OnTableNamespaceSelectionChanged)

        self.mNSModel.namespaceRenamed.disconnect(self.OnNamespaceRenamed)
        self.mNSModel.namespaceFilePathSwapped.disconnect(
            self.OnNamespaceFilePathSwapped)

        self.mIsInitialized = False

    def OnShow(self, *args, **kwargs):
        """ Called when the window is shown """
        print('OnShow')
        print(self.mIsInitialized)
        if not self.mIsInitialized:
            self.Init()
            self.mNSModel.Connect()

    def OnHide(self, *args, **kwargs):
        """ Called when the window is hidden """
        if self.mIsInitialized:
            self.Finish()
            self.mNSModel.Disconnect()
            self.app.OnFileNewCompleted.Remove(self.OnFileNew)
            self.app.OnFileOpenCompleted.Remove(self.OnFileOpen)

    def OnFileNew(self, pControl, pEvent, *args, **kwargs):
        """ Called when a new scene is created """
        mobu_print('OnFileNew: %s' % pEvent)
        self.mNSModel.removeRows(0, self.mNSModel.rowCount())
        self.mNSModel.Finish()
        self.mNSModel.Init()

    def OnFileOpen(self, pControl, pEvent, *args, **kwargs):
        """ Called when a scene is opened """
        self.mNSModel.removeRows(0, self.mNSModel.rowCount())
        self.mNSModel.Refresh()
        self.UpdateUI()
        self.update_tree_namespace()

    def OnNamespaceRenamed(self, pOldName, pNewName, *args, **kwargs):
        """ Called when a namespace is renamed """

        mobu_print('OnNamespaceRenamed(%s, %s)' % (pOldName, pNewName))
        self.update_tree_namespace()

    def OnNamespaceFilePathSwapped(self, objNS, pOldPath, pNewPath, applyEdits, *args, **kwargs):
        """
        Called when a namespace file path is swapped.

        Args:
            objNS ( FBComponent ):
            pOldPath ( str ):
            pNewPath ( str ):
            applyEdits ( bool ):

        Returns:

        """
        lScene = self.system.Scene

        if not applyEdits:
            # do we have any other instances with old path
            anyOtherInstance = False
            for ns in self.system.Scene.Namespaces:
                if isinstance(ns, fb.FBFileReference):
                    refPath = ns.ReferenceFilePath
                    if pOldPath == refPath and ns != objNS:
                        anyOtherInstance = True
                        break

            if anyOtherInstance:
                mobu_print(
                    'We have other instances with old path, so we will not delete shaders graph')
            else:
                mobu_print(
                    'We have no other instances with old path, so we will delete shaders graph')

    def OnBtnBrowsePathClicked(self, *args, **kwargs):
        """
        Browse button clicked.

        Returns:

        """
        lFileToLoad = QtWidgets.QFileDialog.getOpenFileName(
            self,
            "Pick FBX to reference",
            '',
            "*.fbx")[0]  # Returns a Tuple. Get the filename only
        lQFileInfo = QtCore.QFileInfo(lFileToLoad)

        lSuffix = str(lQFileInfo.suffix())

        if lQFileInfo.exists() and lSuffix.lower() == 'fbx':
            self.uiEditFilePath.setText(lFileToLoad)

        self.UpdateUI()

    def OnBtnLoadClicked(self, *args, **kwargs):
        """
        Load button clicked.

        Returns:

        """
        lLoadCount = self.uiSpinLoadTimes.value()
        lBtnClicked, lName = pick_ref_name()
        if lBtnClicked == 2:
            return

        lNameList = fb.FBStringList()
        if lLoadCount == 1:
            lNameList.Add(lName)
        else:
            for lCount in range(0, lLoadCount):
                if self.system.Scene.NamespaceGet(lName + str(lCount + 1)) is not None:
                    lMsgBox = QtWidgets.QMessageBox(
                        QtWidgets.QMessageBox.Information,
                        'Loading',
                        'Creation of file reference %s will be skipped as it is already there.' %
                        (lName + str(lCount + 1)),
                        QtWidgets.QMessageBox.Ok,
                        self)
                    lMsgBox.exec_()
                else:
                    lNameList.Add(lName + str(lCount + 1))

        lStatus = self.system.Scene.NamespaceImportToMultiple(
            lNameList, str(self.uiEditFilePath.text()), True)

        self.mNSModel.UpdateRefNamespaces()
        # self.mNSModel.insertRows(0, 1)
        self.mNSModel.Refresh()
        self.UpdateUI()

    def OnBtnUnloadClicked(self, *args, **kwargs):
        """
        Unload button clicked.

        Returns:

        """
        lIndexes = self.ns_tabel_view.selectedIndexes()
        for lIndex in lIndexes:
            if lIndex.column() == 0:
                lNSObj = self.mNSModel.GetNamespace(lIndex.row())
                self.mNSModel.RemoveFileFromWatcher(lNSObj)
                lNSObj.IsLoaded = False

                refPath = lNSObj.ReferenceFilePath
                if (0 == len(refPath)) or not os.path.isfile(refPath):
                    fb.FBMessageBox(
                        self.DialogTitle,
                        'Reference file is not found!\n Please enter a new file path',
                        'Ok')

            self.mNSModel.Refresh(lIndex)
        self.UpdateUI()
        self.update_tree_namespace()

    def OnBtnReloadClicked(self, *args, **kwargs):
        """ Reload button clicked. """
        lIndexes = self.ns_tabel_view.selectedIndexes()
        for lIndex in lIndexes:
            if lIndex.column() == 0:
                lNSObj = self.mNSModel.GetNamespace(lIndex.row())
                self.mNSModel.Refresh(lIndex)
        self.UpdateUI()
        self.update_tree_namespace()

    def OnBtnDeleteClicked(self, *args, **kwargs):
        """ Delete button clicked. """
        lIndexes = self.ns_tabel_view.selectedIndexes()
        lNameList = []
        for lIndex in lIndexes:
            if lIndex.column() == 0:
                lNSObj = self.mNSModel.GetNamespace(lIndex.row())
                lNameList.append(lNSObj.LongName)
                self.mNSModel.RemoveFileFromWatcher(lNSObj)
                self.mNSModel.removeRows(lIndex.row(), 1)

        for lName in lNameList:
            self.system.Scene.NamespaceDelete(lName)

        self.mNSModel.Refresh()
        self.UpdateUI()
        self.update_tree_namespace()

    def OnBtnExportClicked(self, *args, **kwargs):
        """ Export button clicked. """
        file_path, self.FBX_selected_filter = QtWidgets.QFileDialog.getSaveFileName(
            self, 'Save FBX File',
            '',
        "FBX (*.fbx *.FBX);;FBX (*.FBX);;fbx (*.fbx);;All Files (*.*)",
        "FBX (*.fbx,*.FBX)")

        if not file_path:
            return

        lIndexes = self.ns_tabel_view.selectedIndexes()
        lNameList = []
        for lIndex in lIndexes:
            if lIndex.column() == 0:
                lNSObj = self.mNSModel.GetNamespace(lIndex.row())
                lNameList.append(lNSObj.LongName)

        if lNameList:
            self.system.Scene.NamespaceExport(lNameList[0], file_path)

    def OnBtnInstanceClicked(self, *args, **kwargs):
        """
        Instance button clicked.

        Returns:

        """
        lIndexes = self.ns_tabel_view.selectedIndexes()
        for lIndex in lIndexes:
            if lIndex.column() == 0:
                lNSObj = self.mNSModel.GetNamespace(lIndex.row())

        lInstanceCount = self.uiSpinInstanceTimes.value()
        lBtnClicked, lName = pick_ref_name()
        if lBtnClicked == 2:
            return

        lNameList = fb.FBStringList()
        if lInstanceCount == 1:
            lNameList.Add(lName)
        else:
            for lCount in range(0, lInstanceCount):
                lNameList.Add(lName + str(lCount + 1))

        lApplyRefEdits = False
        if lNSObj.GetContentModified(fb.FBPlugModificationFlag.kFBContentAllModifiedMask):
            lMsgBox = QtWidgets.QMessageBox(
                QtWidgets.QMessageBox.Question,
                'Instancing',
                'Do you want to apply reference edits after instancing?',
                QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                self)
            if lMsgBox.exec_() == QtWidgets.QMessageBox.Yes:
                lApplyRefEdits = True

        lStatus = lNSObj.DuplicateFileRef(lNameList, lApplyRefEdits)

        self.mNSModel.Refresh()
        self.UpdateUI()
        self.update_tree_namespace()

    def OnBtnShowEditsClicked(self, *args, **kwargs):
        """
        Show edits button clicked.

        Returns:

        """
        lIndexes = self.ns_tabel_view.selectedIndexes()
        for lIndex in lIndexes:
            if lIndex.column() == 0:
                lNSObj = self.mNSModel.GetNamespace(lIndex.row())
                if lNSObj.GetContentModified(
                        fb.FBPlugModificationFlag.kFBContentAllModifiedMask):

                    text = lNSObj.GetRefEdit()

                    lDialog = fb.FBFilePopup()
                    lDialog.Filter = '*.txt'
                    lDialog.Style = fb.FBFilePopupStyle.kFBFilePopupSave

                    if (lDialog.Execute()):
                        fname = lDialog.FullFilename

                        with open(fname, "w") as text_file:
                            text_file.write(text)

                    # lMsgBox = QtWidgets.QMessageBox( self )
                    # lMsgBox.setText( lNSObj.GetRefEdit() )
                    # lMsgBox.exec_()

    def OnTableNamespaceSelectionChanged(self, pSelected, pUnselected, *args, **kwargs):
        """
        Called when the selection in the table view changes.
        Args:
            pSelected ():
            pUnselected ():

        Returns:

        """
        self.UpdateUI()
        self.update_tree_namespace()

    def update_tree_namespace(self, *args, **kwargs):
        """ Update the tree view of namespaces """
        self.ns_tree_widget.clear()

        if self.mSingleSelection:
            l_indexes = self.ns_tabel_view.selectedIndexes()

            # l_ns_obj is FBNamespace
            l_ns_obj = self.mNSModel.GetNamespace(l_indexes[0].row())

            l_str_list = [l_ns_obj.LongName]
            if l_ns_obj.ClassGroupName != '':
                l_str_list.append(l_ns_obj.ClassGroupName)

            l_item = QtWidgets.QTreeWidgetItem(l_str_list)
            self.ns_tree_widget.addTopLevelItem(l_item)
            self.update_tree_namespace_recursively(l_item, l_ns_obj)
            self.ns_tree_widget.expandItem(l_item)

    def update_tree_namespace_recursively(self, p_parent_item, p_ns_obj, *args, **kwargs):
        """
        Update the tree view of namespaces recursively
        Args:
            p_parent_item ( QtWidgets.QTreeWidgetItem ): parent item
            p_ns_obj ( FBNamespace ): parent namespace object

        Returns:
            None
        """
        l_list = fb.FBComponentList()
        p_ns_obj.GetContentList(l_list, fb.FBPlugModificationFlag.kFBPlugAllContent, False)
        for l_plug in l_list:
            l_str_list = [l_plug.LongName]
            if l_plug.ClassGroupName != '':
                l_str_list.append(l_plug.ClassGroupName)
            else:
                l_str_list.append(l_plug.ClassName())

            l_item = QtWidgets.QTreeWidgetItem(l_str_list)
            p_parent_item.addChild(l_item)
            self.ns_tree_widget.expandItem(l_item)
            if l_plug.TypeInfo == fb.FBNamespace.TypeInfo:
                self.update_tree_namespace_recursively(l_item, l_plug)

    def UpdateUI(self, *args, **kwargs):
        """
        Update the UI.

        Returns:
            None
        """
        luiBtnBrowsePath = True
        luiBtnLoad = False
        luiSpinLoadTimes = False
        luiBtnUnload = False
        luiBtnReload = False
        luiBtnDelete = False
        luiBtnExport = False
        luiBtnInstance = False
        luiBtnRestore = False
        luiBtnShowEdits = False
        lShaderEdits = False
        luiSpinInstanceTimes = False

        if os.path.exists(self.uiEditFilePath.text()):
            luiBtnLoad = True
            luiSpinLoadTimes = True

        # Update file reference related UI
        lIndexes = self.ns_tabel_view.selectedIndexes()
        mobu_print('UpdateUI: %s' % lIndexes)

        lRows = []
        lHasSelection = False
        if len(lIndexes) > 0:
            lHasSelection = True

        lAllSelectionFileReference = True
        lAllSelectionLoaded = True
        lAllSelectionUnLoaded = True
        lSelectionHasRefEdits = False
        for lIndex in lIndexes:
            if lIndex.column() == 0:
                mobu_print('UpdateUI lIndex: %s' % lIndex.row())
                lRows.append(lIndex.row())
                lNSObj = self.mNSModel.GetNamespace(lIndex.row())
                if lNSObj.TypeInfo == fb.FBNamespace.TypeInfo:
                    lAllSelectionFileReference = False
                else:
                    if lNSObj.IsLoaded:
                        if lNSObj.GetContentModified(
                                fb.FBPlugModificationFlag.kFBContentAllModifiedMask):
                            lSelectionHasRefEdits = True
                        lAllSelectionUnLoaded = False
                        lShaderEdits = True
                    else:
                        lAllSelectionLoaded = False

        self.mSingleSelection = False
        if len(lRows) == 1:
            self.mSingleSelection = True

        if lHasSelection:
            luiBtnDelete = True
            luiBtnExport = True

            if lAllSelectionFileReference:
                if lSelectionHasRefEdits:
                    luiBtnRestore = True

                if self.mSingleSelection:
                    if lSelectionHasRefEdits:
                        luiBtnShowEdits = True
                    luiBtnInstance = True
                    luiSpinInstanceTimes = True

                luiBtnUnload = True
                luiBtnReload = True
                if lAllSelectionLoaded:
                    luiBtnReload = False
                if lAllSelectionUnLoaded:
                    luiBtnUnload = False

        self.uiBtnBrowsePath.setEnabled(luiBtnBrowsePath)
        self.uiBtnLoad.setEnabled(luiBtnLoad)
        self.uiSpinLoadTimes.setEnabled(luiSpinLoadTimes)
        self.uiBtnUnload.setEnabled(luiBtnUnload)
        self.uiBtnReload.setEnabled(luiBtnReload)
        self.uiBtnDelete.setEnabled(luiBtnDelete)
        self.uiBtnExport.setEnabled(luiBtnExport)
        self.uiBtnInstance.setEnabled(luiBtnInstance)
        # self.uiBtnRestore.setEnabled(luiBtnRestore)
        self.uiBtnShowEdits.setEnabled(luiBtnShowEdits)
        self.uiSpinInstanceTimes.setEnabled(luiSpinInstanceTimes)

        # shader edis
        # self.uiBtnRestoreShaders.setEnabled(lShaderEdits)
        # self.uiBtnBakeShaders.setEnabled(lShaderEdits)
        # self.uiBtnShowShadersEdits.setEnabled(lShaderEdits)

    def OnTimer(self, *args, **kwargs):
        """
        Timer event.

        Returns:

        """
        # mobu_print('OnTimer')

        for lFilePath, lReload in self.mNSModel.mRefFileReload.items():

            if not os.path.isfile(lFilePath) and lReload:
                fb.FBMessageBox(
                    "External File Not Found",
                    "The referenced file '%s' is not found!\nReference goes offline" %
                    (lFilePath),
                    "Ok")

                if lFilePath in self.mNSModel.mRefFilePath:
                    for lFileRefName in self.mNSModel.mRefFilePath[lFilePath]:
                        lFileRefObj = fb.FBFindObjectByFullName(
                            'FileReference::' + lFileRefName)

                        if lFileRefObj is not None:
                            self.mNSModel.RemoveFileFromWatcher(lFileRefObj)
                            lFileRefObj.IsLoaded = False

                    self.mNSModel.mRefFileReload[lFilePath] = False

                self.UpdateUI()
                self.update_tree_namespace()

            elif lReload:
                fb.FBMessageBox(
                    "External File Changed",
                    "The referenced file '%s' has been changed externally!" %
                    (lFilePath),
                    "OK")
                if lFilePath in self.mNSModel.mRefFilePath:
                    for lFileRefName in self.mNSModel.mRefFilePath[lFilePath]:
                        lNSObj = fb.FBFindObjectByFullName('FileReference::' + lFileRefName)

                        lOption = fb.FBMessageBox(
                            "External File Changed",
                            "Please choose the following action for Reference: %s!" %
                            (lFileRefName),
                            "Load",
                            "Merge",
                            "Ignore")

                        if lOption != 3:
                            lUndo = fb.FBUndoManager()
                            lUndo.Clear()
                            diff = []
                            edits = ''

                    self.mNSModel.mRefFileReload[lFilePath] = False

        self.mNSModel.UpdateFileWatcher()
