from functools import partial

from Qt import QtWidgets, Qt<PERSON><PERSON>, QtGui
from lsr.qt.core.base_main_window import get_window_class

from lsr.mobu.tool_lib.HUDTool import hud_model
from lsr.mobu.utils.get_resource import get_built_in_icon
from lsr.mobu.utils.PlaybackControl import get_current_frame, get_time_range

# get the base main window class
base_class = get_window_class(app_name='HUD_Tool v1.0')


class HUD_Tool_UI(base_class):
    """
    The main HUD_Tool_UI UI
    """

    _REUSE_SINGLETON = False
    _TRACK_SETTINGS = True

    def __init__(self):
        """ Creates and initializes this window. """
        self.cam_box = None
        self.scene_box = None
        self.take_box = None
        self.tc_in_box = None
        self.tc_out_box = None
        self.frame_box = None
        self.tc_current_box = None
        self.speed_box = None
        self.actor_box = None

        self.force_check_box = None
        self.create_hud_btn = None
        self.delete_hud_btn = None
        self.actor_line_edit = None
        self.connect_speed_btn = None
        self.connect_time_btn = None
        self.vicon_adjust_check_box = None
        self.connect_vicon_time_btn = None

        self.hide_hub_btn = None
        self.show_hub_btn = None
        self.update_hub_btn = None
        self.refresh_hub_btn = None
        self.switch_hub_btn = None

        self.load_tc_spin = None
        self.set_120_btn = None
        self.set_60_btn = None
        self.set_30_btn = None
        self.load_tc_btn = None

        self.start_btn = None
        self.end_btn = None
        self.calculate_btn = None
        self.current_speed_btn = None
        self.start_line_edit = None
        self.end_line_edit = None
        self.calculate_line_edit = None
        self.current_speed_line_edit = None

        self.time_range = get_time_range()

        super(HUD_Tool_UI, self).__init__(
            banner_widget=True, has_art=True, top=True,
            email_address='<EMAIL>')
        self.create_connections()

    def create_menu_bar(self):
        """ Creates the menu bar. """
        bar = self.menuBar()
        menu = bar.addMenu('Project')
        action = menu.addAction('Save Project', None)
        action = menu.addAction('Load Project', None)

    def create_connections(self):
        """
        Create connections
        Returns:

        """

        self.create_hud_btn.clicked.connect(partial(self.create_hud))
        self.delete_hud_btn.clicked.connect(partial(hud_model.delete_hud))
        self.connect_speed_btn.clicked.connect(partial(self.connect_speed))
        self.connect_time_btn.clicked.connect(partial(self.connect_time))
        self.connect_vicon_time_btn.clicked.connect(partial(self.connect_vicon_time_code))

        self.hide_hub_btn.clicked.connect(partial(hud_model.hide_hud))
        self.show_hub_btn.clicked.connect(partial(hud_model.show_hud))
        self.update_hub_btn.clicked.connect(partial(hud_model.update_hud))
        self.refresh_hub_btn.clicked.connect(partial(hud_model.refresh_hud))
        self.switch_hub_btn.clicked.connect(partial(hud_model.switch_camera))

        self.set_120_btn.clicked.connect(partial(self.set_tc_spin, 0.25))
        self.set_60_btn.clicked.connect(partial(self.set_tc_spin, 0.5))
        self.set_30_btn.clicked.connect(partial(self.set_tc_spin, 1.0))
        self.load_tc_btn.clicked.connect(partial(self.load_tc_node_data))

        self.start_btn.clicked.connect(partial(self.set_line_edit, self.start_line_edit))
        self.end_btn.clicked.connect(partial(self.set_line_edit, self.end_line_edit))
        self.current_speed_btn.clicked.connect(partial(self.get_current_speed, cal_mode=False))
        self.calculate_btn.clicked.connect(partial(self.get_current_speed, cal_mode=True))

    def setup_ui(self):
        """Creates UI elements."""
        self.create_menu_bar()

        self.centralwidget = QtWidgets.QWidget(self)
        vbox = QtWidgets.QVBoxLayout(self.centralwidget)
        vbox.setSpacing(3)
        vbox.setContentsMargins(5, 5, 5, 5)

        main_widget = self.add_main_widget()

        vbox.addWidget(main_widget)

        self.setCentralWidget(self.centralwidget)

    def add_main_widget(self, *args, **kwargs):
        """Add main widget."""
        widget = QtWidgets.QWidget()

        layout = QtWidgets.QVBoxLayout()
        widget.setLayout(layout)

        widget.setStyleSheet("background-color: rgb(0, 0, 0);")

        temp_font = widget.font()
        temp_font.setFamily("Yu Gothic UI")
        # temp_font.setPointSize(7)

        font = QtGui.QFont()
        font.setPointSize(10)
        font.setFamily("Yu Gothic UI")
        font.setBold(True)

        self.cam_box = QtWidgets.QCheckBox("Add Camera HUD")
        self.cam_box.setStyleSheet("color: rgb(0, 255, 0);")

        self.scene_box = QtWidgets.QCheckBox("Add Scene HUD")
        self.scene_box.setStyleSheet("color: rgb(255, 0, 0);")

        self.take_box = QtWidgets.QCheckBox("Add Take HUD")
        self.take_box.setStyleSheet("color: rgb(255, 255, 0);")

        self.tc_in_box = QtWidgets.QCheckBox("Add TC-In HUD")
        self.tc_out_box = QtWidgets.QCheckBox("Add TC-Out HUD")

        # Top layout
        top_layout = QtWidgets.QHBoxLayout()
        top_layout.setContentsMargins(10, 10, 10, 10)

        top_layout.addWidget(self.cam_box, alignment=QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)

        scene_take_layout = QtWidgets.QVBoxLayout()
        scene_take_layout.addWidget(self.scene_box, alignment=QtCore.Qt.AlignCenter)
        scene_take_layout.addWidget(self.take_box, alignment=QtCore.Qt.AlignCenter)
        top_layout.addLayout(scene_take_layout, alignment=QtCore.Qt.AlignTop|QtCore.Qt.AlignCenter)

        tc_layout = QtWidgets.QVBoxLayout()
        tc_layout.addWidget(self.tc_in_box, alignment=QtCore.Qt.AlignRight|QtCore.Qt.AlignTop)
        tc_layout.addWidget(self.tc_out_box, alignment=QtCore.Qt.AlignRight|QtCore.Qt.AlignTop)
        top_layout.addLayout(tc_layout, alignment=QtCore.Qt.AlignTop|QtCore.Qt.AlignRight)

        # Bottom layout
        bottom_layout = QtWidgets.QHBoxLayout()
        bottom_layout.setContentsMargins(10, 10, 10, 10)

        frame_current_layout = QtWidgets.QVBoxLayout()
        self.frame_box = QtWidgets.QCheckBox("Add Frame HUD")
        self.tc_current_box = QtWidgets.QCheckBox("Add TC-Current HUD")
        frame_current_layout.addWidget(self.frame_box, alignment=QtCore.Qt.AlignLeft|QtCore.Qt.AlignBottom)
        frame_current_layout.addWidget(self.tc_current_box, alignment=QtCore.Qt.AlignLeft|QtCore.Qt.AlignBottom)
        bottom_layout.addLayout(frame_current_layout, alignment=QtCore.Qt.AlignLeft|QtCore.Qt.AlignBottom)

        self.speed_box = QtWidgets.QCheckBox("Add Speed HUD")
        bottom_layout.addWidget(self.speed_box, alignment=QtCore.Qt.AlignCenter|QtCore.Qt.AlignBottom)

        self.actor_box = QtWidgets.QCheckBox("Add Actor HUD")
        bottom_layout.addWidget(self.actor_box, alignment=QtCore.Qt.AlignRight|QtCore.Qt.AlignBottom)

        layout.addLayout(top_layout)
        center_layout = QtWidgets.QVBoxLayout()
        center_layout.setContentsMargins(20, 5, 20, 5)
        center_widget = self.add_center_widget()
        center_layout.addWidget(center_widget)
        layout.addLayout(center_layout)
        layout.addLayout(bottom_layout)

        # get all widgets and set font
        for wid in widget.findChildren(QtWidgets.QWidget):
            wid.setFont(font)

        for wid in center_widget.findChildren(QtWidgets.QWidget):
            wid.setFont(temp_font)

        return widget

    def add_edit_box(self, widget, *args, **kwargs):
        """ Add edit box."""
        edit_gbox = QtWidgets.QGroupBox(widget)
        edit_gbox_vbox = QtWidgets.QVBoxLayout(edit_gbox)
        edit_gbox_vbox.setContentsMargins(10, 10, 10, 10)
        edit_gbox.setTitle('HUD Edit')
        edit_gbox_vbox.addItem(
            QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed))
        h_box = QtWidgets.QHBoxLayout()
        self.force_check_box = QtWidgets.QCheckBox("Force")
        h_box.addWidget(self.force_check_box)
        self.create_hud_btn = QtWidgets.QPushButton("Create HUD")
        self.create_hud_btn.setIcon(get_built_in_icon('browsing/object_HUD.png'))
        h_box.addWidget(self.create_hud_btn)
        h_box.setStretch(0, 1)
        h_box.setStretch(1, 3)
        edit_gbox_vbox.addLayout(h_box)

        self.delete_hud_btn = QtWidgets.QPushButton("Delete HUD")
        self.delete_hud_btn.setIcon(get_built_in_icon('controls/DeleteKey.png'))
        edit_gbox_vbox.addWidget(self.delete_hud_btn)

        h_box = QtWidgets.QHBoxLayout()
        self.actor_line_edit = QtWidgets.QLineEdit()
        self.actor_line_edit.setPlaceholderText("Actor Name")
        actor_name = hud_model.get_hud_actor_name()
        if actor_name:
            self.actor_line_edit.setText(actor_name)
        h_box.addWidget(self.actor_line_edit)

        self.connect_speed_btn = QtWidgets.QPushButton("Connect Speed")
        self.connect_speed_btn.setIcon(get_built_in_icon('controls/link.png'))
        h_box.addWidget(self.connect_speed_btn)
        # set h_box elements width 1:2
        h_box.setStretch(0, 1)
        h_box.setStretch(1, 3)
        edit_gbox_vbox.addLayout(h_box)

        h_box = QtWidgets.QHBoxLayout()
        _label = QtWidgets.QLabel("Optitrack")
        _label.setPixmap(get_built_in_icon('icons/optitrack.png', use_mobu_res=True).pixmap(100, 21))
        h_box.addWidget(_label)

        self.connect_time_btn = QtWidgets.QPushButton("Connect Current Time")
        self.connect_time_btn.setIcon(get_built_in_icon('browsing/object_timeline.png'))
        h_box.addWidget(self.connect_time_btn)
        h_box.setStretch(0, 1)
        h_box.setStretch(1, 3)
        edit_gbox_vbox.addLayout(h_box)

        h_box = QtWidgets.QHBoxLayout()
        _label = QtWidgets.QLabel("Optitrack")
        _label.setPixmap(get_built_in_icon('icons/vicon.png', use_mobu_res=True).pixmap(100, 21))
        h_box.addWidget(_label)
        self.vicon_adjust_check_box = QtWidgets.QCheckBox("Adjust Time")
        h_box.addWidget(self.vicon_adjust_check_box)
        self.connect_vicon_time_btn = QtWidgets.QPushButton("Connect Current Time")
        self.connect_vicon_time_btn.setIcon(get_built_in_icon('browsing/object_timeline.png'))
        h_box.addWidget(self.connect_vicon_time_btn)
        h_box.setStretch(0, 1)
        h_box.setStretch(1, 1)
        h_box.setStretch(2, 2)
        edit_gbox_vbox.addLayout(h_box)

        h_box = QtWidgets.QHBoxLayout()
        _label = QtWidgets.QLabel("TC_Node Info")
        h_box.addWidget(_label)
        self.load_tc_spin = QtWidgets.QDoubleSpinBox()
        h_box.addWidget(self.load_tc_spin)
        self.load_tc_spin.setRange(0, 1.0)
        self.load_tc_spin.setValue(1)
        self.load_tc_spin.setSingleStep(0.1)

        self.set_120_btn = QtWidgets.QPushButton("120 fps (1/4)")
        h_box.addWidget(self.set_120_btn)
        self.set_60_btn = QtWidgets.QPushButton("60 fps (1/2)")
        h_box.addWidget(self.set_60_btn)
        self.set_30_btn = QtWidgets.QPushButton("30 fps (1)")
        h_box.addWidget(self.set_30_btn)

        self.load_tc_btn = QtWidgets.QPushButton("Load TC_Node")
        h_box.addWidget(self.load_tc_btn)

        h_box.setStretch(0, 3)
        h_box.setStretch(1, 2)
        h_box.setStretch(2, 1)
        h_box.setStretch(3, 1)
        h_box.setStretch(4, 1)
        h_box.setStretch(5, 4)
        edit_gbox_vbox.addLayout(h_box)

        edit_gbox_vbox.addItem(
            QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding))

        return edit_gbox

    def add_display_box(self, widget, *args, **kwargs):
        """ Add display box."""
        display_gbox = QtWidgets.QGroupBox(widget)
        display_gbox_vbox = QtWidgets.QVBoxLayout(display_gbox)
        display_gbox_vbox.setContentsMargins(10, 10, 10, 10)
        display_gbox.setTitle('Display Adjust')

        display_gbox_vbox.addItem(
            QtWidgets.QSpacerItem(10, 10, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed))

        h_box = QtWidgets.QHBoxLayout()
        self.hide_hub_btn = QtWidgets.QPushButton("Hide HUD")
        self.hide_hub_btn.setIcon(get_built_in_icon('story/selected_lock.png'))
        h_box.addWidget(self.hide_hub_btn)
        self.show_hub_btn = QtWidgets.QPushButton("Show HUD")
        self.show_hub_btn.setIcon(get_built_in_icon('story/selected_show.png'))
        h_box.addWidget(self.show_hub_btn)
        display_gbox_vbox.addLayout(h_box)

        self.update_hub_btn = QtWidgets.QPushButton("Save HUD")
        self.update_hub_btn.setIcon(get_built_in_icon('python/saveAs.png'))
        display_gbox_vbox.addWidget(self.update_hub_btn)

        self.refresh_hub_btn = QtWidgets.QPushButton("Refresh HUD")
        self.refresh_hub_btn.setIcon(get_built_in_icon('characterpose/updatePose.png'))
        display_gbox_vbox.addWidget(self.refresh_hub_btn)

        self.switch_hub_btn = QtWidgets.QPushButton("Switch HUD Camera")
        self.switch_hub_btn.setIcon(get_built_in_icon('browsing/object_camera.png'))
        display_gbox_vbox.addWidget(self.switch_hub_btn)

        display_gbox_vbox.addItem(
            QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding))

        return display_gbox

    def add_center_widget(self, *args, **kwargs):
        """Add center widget."""
        widget = QtWidgets.QWidget()
        widget.setStyleSheet("background-color: #31363b;")

        layout = QtWidgets.QVBoxLayout()
        h_layout = QtWidgets.QHBoxLayout()
        layout.addLayout(h_layout)

        edit_gbox = self.add_edit_box(widget)
        h_layout.addWidget(edit_gbox)

        right_layout = QtWidgets.QVBoxLayout()
        display_gbox = self.add_display_box(widget)
        right_layout.addWidget(display_gbox)

        # add a line
        _line = QtWidgets.QFrame()
        _line.setFrameShape(QtWidgets.QFrame.HLine)
        right_layout.addWidget(_line)

        speed_gbox = self.add_speed_box(widget)
        right_layout.addWidget(speed_gbox)
        h_layout.addLayout(right_layout)

        h_layout.setStretch(0, 1)
        h_layout.setStretch(1, 1)

        layout.addItem(
            QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding))
        widget.setLayout(layout)

        return widget

    def add_speed_box(self, widget, *args, **kwargs):
        """Add speed widget."""

        speed_gbox = QtWidgets.QGroupBox(widget)
        layout = QtWidgets.QVBoxLayout(speed_gbox)
        layout.setContentsMargins(10, 10, 10, 10)
        speed_gbox.setTitle('Speed Check')

        h_all_layout = QtWidgets.QHBoxLayout()
        h_01_layout = QtWidgets.QVBoxLayout()

        layout.addLayout(h_all_layout)
        h_all_layout.addLayout(h_01_layout)

        h_layout = QtWidgets.QHBoxLayout()
        self.start_btn = QtWidgets.QPushButton("Set Start")
        h_layout.addWidget(self.start_btn)
        self.end_btn = QtWidgets.QPushButton("Set End")
        h_layout.addWidget(self.end_btn)
        h_01_layout.addLayout(h_layout)

        h_layout = QtWidgets.QHBoxLayout()
        self.start_line_edit = QtWidgets.QLineEdit()
        self.start_line_edit.setPlaceholderText("Start Frame")
        self.start_line_edit.setText(str(self.time_range[0]))
        self.start_line_edit.setAlignment(QtCore.Qt.AlignCenter)
        self.start_line_edit.setValidator(QtGui.QIntValidator())
        h_layout.addWidget(self.start_line_edit)
        self.end_line_edit = QtWidgets.QLineEdit()
        self.end_line_edit.setPlaceholderText("End Frame")
        self.end_line_edit.setText(str(self.time_range[1]))
        self.end_line_edit.setAlignment(QtCore.Qt.AlignCenter)
        self.end_line_edit.setValidator(QtGui.QIntValidator())
        h_layout.addWidget(self.end_line_edit)
        h_01_layout.addLayout(h_layout)

        h_layout = QtWidgets.QHBoxLayout()
        self.calculate_btn = QtWidgets.QPushButton("Calculate")
        h_layout.addWidget(self.calculate_btn)
        self.calculate_line_edit = QtWidgets.QLineEdit()
        h_layout.addWidget(self.calculate_line_edit)
        h_01_layout.addLayout(h_layout)

        _line = QtWidgets.QFrame()
        _line.setFrameShape(QtWidgets.QFrame.VLine)
        h_all_layout.addWidget(_line)

        h_02_layout = QtWidgets.QVBoxLayout()
        self.current_speed_btn = QtWidgets.QPushButton("Current Speed")
        h_02_layout.addWidget(self.current_speed_btn)
        self.current_speed_line_edit = QtWidgets.QLineEdit()
        h_02_layout.addWidget(self.current_speed_line_edit)
        h_all_layout.addLayout(h_02_layout)

        h_all_layout.setStretch(0, 10)
        h_all_layout.setStretch(1, 1)
        h_all_layout.setStretch(2, 5)

        return speed_gbox

    def set_line_edit(self, line_edit, *args, **kwargs):
        """Set line edit."""
        value = get_current_frame()
        line_edit.setText(str(value))

    def save_settings(self):
        """
        Updates the app settings and saves it to disk.

        Returns:
            QSettings: The settings object.
        """
        settings = super(HUD_Tool_UI, self).save_settings()
        settings.beginGroup('hud_part')
        settings.setValue('cam_box', self.cam_box.isChecked())
        settings.setValue('scene_box', self.scene_box.isChecked())
        settings.setValue('take_box', self.take_box.isChecked())
        settings.setValue('tc_in_box', self.tc_in_box.isChecked())
        settings.setValue('tc_out_box', self.tc_out_box.isChecked())
        settings.setValue('frame_box', self.frame_box.isChecked())
        settings.setValue('tc_current_box', self.tc_current_box.isChecked())
        settings.setValue('speed_box', self.speed_box.isChecked())
        settings.setValue('actor_box', self.actor_box.isChecked())
        settings.endGroup()

        settings.sync()
        return settings

    def load_settings(self):
        """
        Loads the app settings.

        Returns:
            QSettings: The settings object.
        """
        settings = super(HUD_Tool_UI, self).load_settings()

        settings.beginGroup('hud_part')

        self.cam_box.setChecked(settings.value('cam_box', False, type=bool))
        self.scene_box.setChecked(settings.value('scene_box', False, type=bool))
        self.take_box.setChecked(settings.value('take_box', False, type=bool))
        self.tc_in_box.setChecked(settings.value('tc_in_box', False, type=bool))
        self.tc_out_box.setChecked(settings.value('tc_out_box', False, type=bool))
        self.frame_box.setChecked(settings.value('frame_box', False, type=bool))
        self.tc_current_box.setChecked(settings.value('tc_current_box', False, type=bool))
        self.speed_box.setChecked(settings.value('speed_box', False, type=bool))
        self.actor_box.setChecked(settings.value('actor_box', False, type=bool))

        settings.endGroup()

        return settings

    def create_hud(self, *args, **kwargs):
        """Create HUD."""
        hud_model.add_hud_element(
            force=self.force_check_box.isChecked(),
            add_came=self.cam_box.isChecked(),
            add_scene=self.scene_box.isChecked(),
            add_take=self.take_box.isChecked(),
            add_tc_in=self.tc_in_box.isChecked(),
            add_tc_out=self.tc_out_box.isChecked(),
            add_frame=self.frame_box.isChecked(),
            add_tc_current=self.tc_current_box.isChecked(),
            add_speed=self.speed_box.isChecked(),
            add_actor=self.actor_box.isChecked()
        )

    def connect_speed(self, *args, **kwargs):
        """Connect speed."""
        hud_model.connect_speed(self.actor_line_edit)

    def connect_time(self, *args, **kwargs):
        """Connect time."""
        hud_model.connect_time_code(self.actor_line_edit)

    def connect_vicon_time_code(self, *args, **kwargs):
        """Connect vicon time code."""
        adjust_time = self.vicon_adjust_check_box.isChecked()
        hud_model.connect_vicon_time_code(adjust_time=adjust_time)

    def load_tc_node_data(self, *args, **kwargs):
        """Get time code spin."""
        value = self.load_tc_spin.value()
        hud_model.load_tc_node(value)

    def set_tc_spin(self, value, *args, **kwargs):
        """Set time code spin."""
        self.load_tc_spin.setValue(value)

    def get_current_speed(self, cal_mode=False, *args, **kwargs):
        """Get current speed."""
        actor_name = hud_model.get_hud_actor_name()
        params = {}

        if cal_mode:
            start_frame = self.start_line_edit.text()
            end_frame = self.end_line_edit.text()
            params['start_frame'] = int(start_frame)
            params['end_frame'] = int(end_frame)

        if actor_name:
            params['actor_name'] = actor_name

        speed_info = hud_model.get_node_speed(**params)

        if cal_mode:
            self.calculate_line_edit.setText(str(speed_info))
        else:
            self.current_speed_line_edit.setText(str(speed_info))
