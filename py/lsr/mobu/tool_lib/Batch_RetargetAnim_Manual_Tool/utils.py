# -*- coding: utf-8 -*-
import os
import shutil
import re

import pyfbsdk as fb
import lsr.mobu.rig.file_options as file_options
import lsr.mobu.utils.PlaybackControl as PlaybackControl
from lsr.mobu.nodezoo.node import Character
from lsr.protostar.lib import ActionLibrary as alib
from lsr.mobu.rig.file_operator import export_clean_animation

alib.refresh()


class BatchManualRetarget(object):

    def __init__(self, batch_data, *args, **kwargs):
        self.batch_data = batch_data
        self.merged_source_rig_path = None
        self.source_char_name = None

    def get_file_list(self, filepath, *args, **kwargs):
        """
        get file list
        Args:
            filepath (str):

        Returns:

        """
        file_list = []
        fbx_file_param = r'.*\.(fbx)$'
        bck_file_str = r'.*\.(bck)$'
        for root, dirs, files in os.walk(filepath):
            for filename in files:
                if re.match(fbx_file_param, filename, re.IGNORECASE):
                    if not re.match(bck_file_str, root, re.IGNORECASE):
                        file_list.append(os.path.normpath(os.path.join(root, filename)))
        return file_list

    def source_fbx_rename(self, *args, **kwargs):
        """
        rename source fbx file to match target fbx file name
        """
        source_fbx_folder = self.batch_data["input_fbx_folder"]
        target_fbx_folder = os.path.join(self.batch_data["output_fbx_folder"], 'output_fbx')
        if not os.path.exists(target_fbx_folder):
            os.makedirs(target_fbx_folder)

        source_fbx_list = self.get_file_list(source_fbx_folder)
        for fbx_file in source_fbx_list:
            fbx_anim_file = str(fbx_file)
            new_fbx_name = fbx_anim_file.replace(source_fbx_folder, target_fbx_folder)
            new_fbx_name_dir = os.path.dirname(new_fbx_name)
            if not os.path.exists(new_fbx_name_dir):
                os.makedirs(new_fbx_name_dir)

            fbx_base_name = os.path.basename(fbx_anim_file)
            fbx_file_name = os.path.splitext(fbx_base_name)[0]
            fb.FBApplication().FileOpen(fbx_anim_file, False)
            fb.FBSystem().CurrentTake.Name = fbx_file_name
            save_options = file_options.SaveAllOption(False)
            fb.FBApplication().FileSave(new_fbx_name, save_options)

        fb.FBApplication().FileOpen(self.batch_data["input_fbx_source_rig"], False)
        self.source_char_name = fb.FBApplication().CurrentCharacter.LongName

    def merge_fbx_to_target(self, *args, **kwargs):
        """
        merge fbx to target rig
        """
        source_rename_folder = os.path.join(self.batch_data["output_fbx_folder"], 'output_fbx')
        source_fbx_files = self.get_file_list(source_rename_folder)

        target_rig_path = self.batch_data["input_fbx_target_rig"]

        fb.FBApplication().FileOpen(target_rig_path, False)
        for take in fb.FBSystem().Scene.Takes:
            take.FBDelete()

        all_char = Character.all_characters()
        if len(all_char) != 2:
            fb.FBMessageBox("Error", "角色数量错误", "OK")
            raise RuntimeError("角色数量错误")
        char = [char.fb_node for char in all_char if char.fb_node.LongName != self.source_char_name][0]
        fb.FBApplication().CurrentCharacter = char

        path_take_dict = dict()
        path_timerange_dict = dict()
        for fbx_file in source_fbx_files:
            fbx_anim_file = str(fbx_file)
            fb.FBApplication().FileMerge(fbx_anim_file, False)
            path_take_dict[fbx_anim_file] = fb.FBSystem().CurrentTake
            st_fb_time, ed_fb_time = fb.FBSystem().CurrentTake.LocalTimeSpan.GetStart(), fb.FBSystem().CurrentTake.LocalTimeSpan.GetStop()
            path_timerange_dict[fbx_anim_file] = [st_fb_time, ed_fb_time]
        shutil.rmtree(source_rename_folder)
        os.makedirs(source_rename_folder)
        fps = self.batch_data["plot_fps"]
        PlaybackControl.set_frame_rate(fps)

        fb.FBApplication().CurrentCharacter.PropertyList.Find('Active Source').Data = True
        plot_options = fb.FBPlotOptions()
        plot_options.PlotAllTakes = True
        plot_options.PlotOnFrame = True
        plot_options.PlotPeriod = fb.FBTime(0, 0, 0, 1)
        plot_options.UseConstantKeyReducer = False
        plot_options.ConstantKeyReducerKeepOneKey = False
        plot_options.SmartPlot = False
        fb.FBApplication().CurrentCharacter.PlotAnimation(fb.FBCharacterPlotWhere.kFBCharacterPlotOnSkeleton,
                                                          plot_options)

        # bake root anim
        pelvis_joint = char.GetModel(fb.FBBodyNodeId.kFBHipsNodeId)
        if not pelvis_joint:
            fb.FBMessageBox("Error", "No pelvis joint in character", "OK")
            raise RuntimeError("No pelvis joint in character")
        pelvis_parent = pelvis_joint.Parent
        pelvis_parent.Selected = True

        all_takes = list(fb.FBSystem().Scene.Takes)
        for take in all_takes:
            fb.FBSystem().CurrentTake = take
            plot_options = fb.FBPlotOptions()
            plot_options.PlotAllTakes = True
            plot_options.PlotOnFrame = True
            plot_options.PlotPeriod = fb.FBTime(0, 0, 0, 1)
            plot_options.UseConstantKeyReducer = False
            plot_options.ConstantKeyReducerKeepOneKey = False
            plot_options.SmartPlot = False
            take.PlotTakeOnSelected(plot_options)
            if take.Name == "Take 001":
                take.FBDelete()

        fb.FBSystem().Scene.Evaluate()

        char = fb.FBApplication().CurrentCharacter
        model_list = list(model for model in self.get_current_char_rig_mesh())
        joint_list = self.get_current_char_all_joints()
        if self.batch_data["is_exp_mesh"]:
            exp_list = [char] + model_list + joint_list
        else:
            exp_list = [char] + joint_list
        for obj in exp_list:
            obj.Selected = True

        for path, take in path_take_dict.items():
            fb.FBSystem().CurrentTake = take
            st_fb_time, ed_fb_time = path_timerange_dict[path][0], path_timerange_dict[path][1]

            for model in exp_list:
                if isinstance(model, fb.FBCharacter):
                    continue
                (lLTime, lRTime) = PlaybackControl.find_limits(model.AnimationNode)

                if lLTime:
                    lCurrentTime = fb.FBSystem().LocalTime
                    lDelta = fb.FBTime()
                    lDelta.Set(lCurrentTime.Get() - lLTime.Get())
                    PlaybackControl.offset_keys(model.AnimationNode, lDelta)
            lSetTimeSpan = fb.FBTimeSpan(st_fb_time, ed_fb_time)
            fb.FBSystem().CurrentTake.LocalTimeSpan = lSetTimeSpan

            save_option = file_options.SaveOption(False)
            for index in range(save_option.GetTakeCount()):
                if save_option.GetTakeName(index) == take.Name:
                    save_option.SetTakeSelect(index, True)
                else:
                    save_option.SetTakeSelect(index, False)
            save_option.TransportSettings = True
            char.PropertyList.Find('Active Source').Data = False
            dir_path = os.path.dirname(path)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
            fb.FBApplication().FileSave(path, save_option)

        fb.FBApplication().FileNew()
        if self.batch_data["is_remove_name_space"]:
            for path in path_take_dict.keys():
                graph = alib.create_graph(name='anim_exp_graph')
                action = alib.create_action(
                    'RNSAction', name='anim_exp_act', graph=graph)
                action.file_path.value = path
                graph.execute()

    def clean_out_put(self, *args, **kwargs):
        out_put_path = os.path.join(self.batch_data["output_fbx_folder"], 'output_fbx')
        for root, dirs, files in os.walk(out_put_path):
            for dir_path in dirs:
                if dir_path.endswith('.bck'):
                    shutil.rmtree(os.path.join(root, dir_path))

    def get_current_char_rig_mesh(self, *args, **kwargs):
        """
        get current character rig mesh
        """
        char = fb.FBApplication().CurrentCharacter
        if not char:
            fb.FBMessageBox("Error", "No character in scene", "OK")
            raise RuntimeError("No character in scene")
        model_list = fb.FBModelList()
        char.GetSkinModelList(model_list)
        return model_list

    def get_current_char_all_joints(self, *args, **kwargs):
        """
        get current character all joints
        """
        def get_joint_heirarchy(joint, my_joint_list):
            if not joint in my_joint_list:
                my_joint_list.append(joint)
            for child in joint.Children:
                get_joint_heirarchy(child, my_joint_list)
        char = fb.FBApplication().CurrentCharacter
        pelvis_joint = char.GetModel(fb.FBBodyNodeId.kFBHipsNodeId)
        if not pelvis_joint:
            fb.FBMessageBox("Error", "No pelvis joint in character", "OK")
            raise RuntimeError("No pelvis joint in character")
        pelvis_parent = pelvis_joint.Parent
        if not pelvis_parent:
            fb.FBMessageBox("Error", "No pelvis parent in character", "OK")
            raise RuntimeError("No pelvis parent in character")
        joint_list = []
        get_joint_heirarchy(pelvis_parent, joint_list)
        return joint_list

    def run(self, *args, **kwargs):
        self.source_fbx_rename()
        self.merge_fbx_to_target()
        self.clean_out_put()
        fb.FBApplication().FileNew()
        fb.FBMessageBox("Finish", "Batch retarget finish!", "OK")
