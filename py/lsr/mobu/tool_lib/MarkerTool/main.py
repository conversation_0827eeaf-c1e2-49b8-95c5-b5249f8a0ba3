from Qt import QtWidgets
from lsr.qt.core.base_main_window import get_window_class
import lsr.mobu.tool_lib.MarkerTool.marker_shape_widget as msw
import lsr.mobu.tool_lib.MarkerTool.marker_create_widget as mcw


base_class = get_window_class(app_name='LSR_Mobu_Marker_Tool')


class LSR_Mobu_Marker_Tool(base_class):
    """
    LSR_Mobu_Marker_Tool
    """

    def __init__(self, *args, **kwargs):

        super(LSR_Mobu_Marker_Tool, self).__init__()

        # self.setupUi(self)
        self.setWindowTitle(u"LSR Mobu Marker Tool")

    def setup_ui(self, *args, **kwargs):
        """Creates UI elements."""

        self.central_widget = QtWidgets.QTabWidget()

        self.create_tab = mcw.MarkerCreateWidget()
        self.central_widget.addTab(self.create_tab, "Marker Create")

        self.marker_tab = msw.MarkerShapeWidget()
        self.central_widget.addTab(self.marker_tab, "Marker Shape")

        # Set the central widget of the main window
        self.setCentralWidget(self.central_widget)

    def showEvent(self, event, *args, **kwargs):
        """Save settings before showing."""
        self.marker_tab.lSysOnUIIdle.Add(self.marker_tab.OnUIIdle)
        super(LSR_Mobu_Marker_Tool, self).showEvent(event)

    def closeEvent(self, event, *args, **kwargs):
        """Save settings before closing."""
        self.marker_tab.lSysOnUIIdle.Remove(self.marker_tab.OnUIIdle)
        super(LSR_Mobu_Marker_Tool, self).closeEvent(event)

    def hideEvent(self, event, *args, **kwargs):
        """ Save settings before hiding. """
        # print('Hiding LSR_Mobu_Marker_Tool')
        self.marker_tab.lSysOnUIIdle.Remove(self.marker_tab.OnUIIdle)
        super(LSR_Mobu_Marker_Tool, self).hideEvent(event)


def show(*args, **kwargs):
    """
    Show LSR_Mobu_Marker_Tool UI

    Returns:
        None
    """
    LSR_Mobu_Marker_Tool().holder_show()
