import os.path

import pyfbsdk as fb
from functools import partial

from Qt import QtGui, QtWidgets
from lsr.qt.core.widgets.buttons import ColorPushButton
from lsr.mobu.tool_lib.MarkerTool import ICONS_PATH
from lsr.mobu.tool_lib.MarkerTool.marker_shape_limit_widget import CtrlLimitGBoxWidget
from lsr.mobu.nodezoo.node import Marker
from lsr.mobu.rig.constants import LOOK_KEYS
import lsr.mobu.rig.data as dutil
import lsr.mobu.utils.FindObjects as FindObjects

ENUM_ATTRS = ('look',)
BOOL_ATTRS = ()


class MarkerShapeWidget(QtWidgets.QWidget):
    """
    A widget for modifying ctrl shapes.
    """

    def __init__(self, *args, **kwargs):
        self.lSystem = fb.FBSystem()
        self.lScene = fb.FBSystem().Scene
        self.lSysOnUIIdle = self.lSystem.OnUIIdle

        super(MarkerShapeWidget, self).__init__(*args, **kwargs)

        central_layout = QtWidgets.QVBoxLayout()
        self.setLayout(central_layout)

        # settings
        grid = QtWidgets.QGridLayout()
        central_layout.addLayout(grid)

        self.lb_sel = QtWidgets.QLineEdit()
        self.lb_sel.setReadOnly(True)
        grid.addWidget(QtWidgets.QLabel('Selection: '), 0, 0, 1, 1)
        grid.addWidget(self.lb_sel, 0, 1, 1, 1)

        i = 1
        for attr in ENUM_ATTRS:
            cbx = QtWidgets.QComboBox()
            setattr(self, attr, cbx)
            cbx.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
            cbx.currentIndexChanged.connect(partial(self.set_look, attr))
            grid.addWidget(QtWidgets.QLabel(attr + ': '), i, 0, 1, 1)
            grid.addWidget(cbx, i, 1, 1, 1)
            i += 1

        # toggles
        for attr in BOOL_ATTRS:
            cbx = QtWidgets.QCheckBox()
            setattr(self, attr, cbx)
            cbx.toggled.connect(partial(self.set_attr, attr))
            grid.addWidget(QtWidgets.QLabel(attr + ': '), i, 0, 1, 1)
            grid.addWidget(cbx, i, 1, 1, 1)
            i += 1

        # color
        hbox = QtWidgets.QHBoxLayout()
        grid.addWidget(QtWidgets.QLabel('Color: '), i, 0, 1, 1)
        grid.addLayout(hbox, i, 1, 1, 1)

        self.btn_color = ColorPushButton(default=QtGui.QColor(30, 30, 30))
        self.btn_color.color_changed.connect(partial(self.set_color))
        self.btn_color.setMaximumWidth(100)
        hbox.addWidget(self.btn_color)

        # self.btn_color_auto = QtWidgets.QPushButton('Auto')
        # self.btn_color_auto.clicked.connect(self.set_color_auto)
        # self.btn_color_auto.setMaximumWidth(100)
        # hbox.addWidget(self.btn_color_auto)
        hbox.addStretch(10)

        # transform
        gbx = QtWidgets.QGroupBox('Transform')
        central_layout.addWidget(gbx)
        grid = QtWidgets.QGridLayout()
        gbx.setLayout(grid)

        grid.addWidget(QtWidgets.QLabel('Space: '), 0, 0, 1, 1)
        hbox = QtWidgets.QHBoxLayout()
        grid.addLayout(hbox, 0, 1, 1, 1)
        self.rb_local = QtWidgets.QRadioButton('Local')
        hbox.addWidget(self.rb_local)
        self.rb_world = QtWidgets.QRadioButton('World')
        hbox.addWidget(self.rb_world)
        self.rb_local.setChecked(True)
        self.rb_world.toggled.connect(partial(self.refresh))
        hbox.addStretch(10)

        i = 1
        for attr, step, icon in zip(('Position', 'Rotate', 'Scale'), (1, 10, .5),
                                    ('HIKCustomRigToolTranslate', 'HIKCustomRigToolRotate', 'polyScaleUV')):
            lb = QtWidgets.QLabel()
            lb.setPixmap(QtGui.QPixmap('{}/{}.png'.format(ICONS_PATH, icon)))
            grid.addWidget(lb, i, 0, 1, 1)

            sbx = QtWidgets.QDoubleSpinBox()
            sbx.setPrefix('Step: ')
            sbx.setRange(0, 99999)
            sbx.setValue(step)
            sbx.setSingleStep(step)
            sbx.setSizePolicy(
                QtWidgets.QSizePolicy.Expanding,
                QtWidgets.QSizePolicy.Expanding)
            grid.addWidget(sbx, i, 1, 1, 1)
            sbx.valueChanged.connect(partial(self.update_step, attr))
            setattr(self, attr, sbx)

            j = 2
            for ax, color in zip(
                    'XYZ', ((66, 33, 28), (28, 66, 40), (28, 36, 66))):
                sbx = QtWidgets.QDoubleSpinBox()
                sbx.setRange(-99999, 99999)
                sbx.setPrefix(ax + ': ')
                sbx.setSingleStep(step)
                sbx.setSizePolicy(
                    QtWidgets.QSizePolicy.Expanding,
                    QtWidgets.QSizePolicy.Expanding)
                grid.addWidget(sbx, i, j, 1, 1)
                sbx.setStyleSheet(
                    ('QDoubleSpinBox{{'
                     'background-color: rgb({}, {}, {});}}').format(*color))
                sbx.valueChanged.connect(partial(self.xform_ctrl, attr))
                setattr(self, attr + ax, sbx)
                j += 1

            i += 1

            if attr == 'Scale':
                lb = QtWidgets.QLabel('')
                lb.setPixmap(QtGui.QPixmap('{}/{}.png'.format(ICONS_PATH, icon)))
                grid.addWidget(lb, i, 0, 1, 1)

                btn = QtWidgets.QPushButton()
                btn.setIcon(QtGui.QIcon('{}/moveUVUp.png'.format(ICONS_PATH)))
                grid.addWidget(btn, i, 1, 1, 1)
                btn.clicked.connect(partial(self.scale_all, False))
                btn.setSizePolicy(
                    QtWidgets.QSizePolicy.Expanding,
                    QtWidgets.QSizePolicy.Expanding)

                btn = QtWidgets.QPushButton()
                btn.setIcon(QtGui.QIcon('{}/moveUVDown.png'.format(ICONS_PATH)))
                grid.addWidget(btn, i, 2, 1, 1)
                btn.clicked.connect(partial(self.scale_all, True))
                btn.setSizePolicy(
                    QtWidgets.QSizePolicy.Expanding,
                    QtWidgets.QSizePolicy.Expanding)

            i += 1

        # enable dof
        gbx = QtWidgets.QGroupBox('Ctrl Limit')
        vbox = QtWidgets.QVBoxLayout()
        vbox.setContentsMargins(0, 0, 0, 0)
        gbx.setLayout(vbox)
        self.dof_box = CtrlLimitGBoxWidget(self)
        vbox.addWidget(self.dof_box)
        central_layout.addWidget(gbx)

        # mirror
        hbox = QtWidgets.QHBoxLayout()
        central_layout.addLayout(hbox)

        self.btn_mirror_sel = QtWidgets.QPushButton('Mirror Selected')
        self.btn_mirror_sel.clicked.connect(partial(self.mirror, sel=True))
        self.btn_mirror_sel.setFixedHeight(30)
        hbox.addWidget(self.btn_mirror_sel)
        self.btn_mirror_all = QtWidgets.QPushButton('Mirror All')
        self.btn_mirror_all.clicked.connect(partial(self.mirror, sel=False))
        self.btn_mirror_all.setFixedHeight(30)
        hbox.addWidget(self.btn_mirror_all)

        # import & export
        hbox = QtWidgets.QHBoxLayout()
        central_layout.addLayout(hbox)
        self.btn_export_shape = QtWidgets.QPushButton('Export Shape')
        self.btn_export_shape.clicked.connect(partial(self.export_shape))
        self.btn_export_shape.setFixedHeight(30)
        hbox.addWidget(self.btn_export_shape)
        self.btn_import_shape = QtWidgets.QPushButton('Import Shape')
        self.btn_import_shape.clicked.connect(partial(self.import_shape))
        self.btn_import_shape.setFixedHeight(30)
        hbox.addWidget(self.btn_import_shape)

        self._markers = []
        # self.lSysOnUIIdle.Add(self.OnUIIdle)
        self.refresh()

    @property
    def markers(self):
        """Returns the selected marker."""
        current_selection = FindObjects.get_selected_objects()
        if len(current_selection) > 0:
            if isinstance(current_selection[-1], fb.FBModelMarker):
                self._markers = [Marker(x) for x in current_selection]
                return self._markers
        return None

    @markers.setter
    def marker(self, value):
        """Sets the selected marker."""
        if isinstance(value, list):
            self._markers = value
        elif isinstance(value, fb.FBModelMarker):
            self._markers = [Marker(value)]
        else:
            raise TypeError('MarkerShapeWidget.marker must be a list or FBModelMarker.')

    def OnUIIdle(self, control, event, *args, **kwargs):
        """Refreshes this UI on idle, based on current selection."""
        if self.markers:
            self.lb_sel.setText(self.markers[-1].name)
        else:
            self.lb_sel.setText('None')
            return

    def change_marker_parameters(self, *args, **kwargs):
        """Changes the marker parameters."""
        if self.markers:
            self.lb_sel.setText(self.markers[-1].name)
        else:
            self.lb_sel.setText('None')
            return

        for attr in ENUM_ATTRS:
            cbx = getattr(self, attr)
            cbx.addItems(LOOK_KEYS)
            cbx.setCurrentText(str(self.markers[-1].look))

        # update color
        color = self.markers[-1].color
        qt_color = QtGui.QColor(*[x * 255 for x in color])
        self.btn_color.color = qt_color

        # update xform
        for attr in ('Position', 'Rotate', 'Scale'):
            val = None
            if attr == 'Position':
                val = self.markers[-1].look_translation
            elif attr == 'Rotate':
                val = self.markers[-1].look_rotation
            else:
                val = self.markers[-1].look_scale

            for i, ax in enumerate('XYZ'):
                sbx = getattr(self, attr + ax)
                sbx.setValue(val[i])

    def enterEvent(self, event, *args, **kwargs):
        """Refreshes this UI on cursor enter, based on current selection."""
        self.refresh()
        self.dof_box.refresh()
        return super(MarkerShapeWidget, self).enterEvent(event)

    def _set_signal_blocked(self, blocked, *args, **kwargs):
        for each in ENUM_ATTRS:
            getattr(self, each).blockSignals(blocked)
        for attr in ('Position', 'Rotate', 'Scale'):
            for ax in 'XYZ':
                getattr(self, attr + ax).blockSignals(blocked)
        self.btn_color.blockSignals(blocked)

    def reset(self, *args, **kwargs):
        """Resets the entire UI."""
        self._set_signal_blocked(True)
        self.lb_sel.setText('None')
        for each in ENUM_ATTRS:
            getattr(self, each).clear()

        self.btn_color.reset()
        for attr in ('Position', 'Rotate', 'Scale'):
            for ax in 'XYZ':
                getattr(self, attr + ax).setValue(0)
        self._set_signal_blocked(False)

    def refresh(self, *args, **kwargs):
        """Refresh the entire UI."""

        self.reset()
        if not self.markers:
            return

        self._set_signal_blocked(True)

        for each in ENUM_ATTRS:
            getattr(self, each).blockSignals(True)

        self.change_marker_parameters()
        self._set_signal_blocked(False)

    def set_look(self, attr, val, *args, **kwargs):
        """Sets the selected marker attribute."""
        if not self.markers:
            return
        for marker in self.markers:
            marker.look = LOOK_KEYS[val]

    def update_step(self, attr, step, *args, **kwargs):
        for ax in 'XYZ':
            getattr(self, attr + ax).setSingleStep(step)
        return

    def set_color(self, color, *args, **kwargs):
        """Sets the selected marker color.

        Args:
            color (QColor or tuple): A color to set.
                If tuple is used, it's range must be from 0 ~ 1.

        Returns:
            None
        """
        if isinstance(color, QtGui.QColor):
            color = [
                color.red() / 255.0,
                color.green() / 255.0,
                color.blue() / 255.0]

        if self.markers:
            for marker in self.markers:
                marker.color = color

    def xform_ctrl(self, attr, *args, **kwargs):
        """Transforms the selected ctrls.

        Args:
            attr (str): An attribute to set.
                It can be 'Position', 'Rotate', or 'Scale'.
        Returns:
            None
        """
        space = 'world' if self.rb_world.isChecked() else 'object'

        if not self.markers:
            return

        new_value = []
        for ax in 'XYZ':
            new_value.append(getattr(self, attr + ax).value())
        new_value = fb.FBVector3d(*new_value)

        if attr.startswith('Position'):
            for marker in self.markers:
                marker.look_translation = new_value
        elif attr.startswith('Rotate'):
            for marker in self.markers:
                marker.look_rotation = new_value
        else:
            for marker in self.markers:
                marker.look_scale = new_value

    def scale_all(self, reverse=False, *args, **kwargs):
        """
        Scales all channels.
        Args:
            reverse (bool): If True, scales in reverse direction.

        Returns:
            None
        """
        if not self.markers:
            return

        step = getattr(self, 'Scale').value()
        if reverse:
            step *= -1

        for marker in self.markers:
            marker.size += step

    def mirror(self, sel=False, *args, **kwargs):
        """Mirrors the selected ctrls."""
        return

    def export_shape(self, *args, **kwargs):
        """Exports the selected marker shape."""
        file_name = QtWidgets.QFileDialog.getSaveFileName(
            self, 'Save shape', '', '*.json')[0]

        if file_name:
            dutil.export_ctrl_data(file_name)

    def import_shape(self, *args, **kwargs):
        """Imports the selected marker shape."""
        file_name = QtWidgets.QFileDialog.getOpenFileName(
            parent=self, caption='Import from', dir='.', filter='Json Files (*.json)')[0]

        if file_name:
            if os.path.exists(file_name):
                dutil.import_ctrl_data(file_name)
            else:
                fb.FBMessageBox('ERROR', 'No ctrl data found.', 'OK')
