from lsr.mobu.utils.FindObjects import select_branch
from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_base import *
from pyfbsdk import *
from lsr.mobu.nodezoo.node import Character

FBX_PATH = os.getenv('MOTIONBUILDER_FBX_PATH')


class FBActorRetarget(object):

    def __init__(self, method='vicon', *args, **kwargs):
        self.temp_path = os.path.normpath(os.path.join(FBX_PATH, 'lsr_fbactor_rig_temp_{method}.fbx'.format(method=method)))
        self.finger_character = None
        self.actor_character = None
        self.final_character = None
        self.load_temp()

        self.final_anim_dict = dict()

    def load_temp(self, *args, **kwargs):
        import_file_all(self.temp_path)

        self.finger_character = find_character_by_name('Finger')
        self.actor_character = find_character_by_name('constraint:constraint')
        self.final_character = find_character_by_name('Final_out')

    def run_export_final_out(self, data_path, temp_actor, finger_character, rig_path, *args, **kwargs):
        self.finger_character.ActiveInput = True
        self.actor_character.ActiveInput = True
        self.finger_character.InputCharacter = finger_character
        self.finger_character.InputType = FBCharacterInputType.kFBCharacterInputCharacter
        self.actor_character.InputActor = temp_actor
        self.actor_character.InputType = FBCharacterInputType.kFBCharacterInputActor

        self.final_character.PlotAnimation(FBCharacterPlotWhere.kFBCharacterPlotOnSkeleton,
                                           FBPlotOptions())
        clear_selection()
        self.final_character.Selected = True
        final_hip_bone = list(self.final_character.PropertyList.Find('HipsLink'))[0]
        parent = final_hip_bone
        while parent.Parent:
            parent = parent.Parent
        select_branch(parent)

        data_dir = os.path.dirname(data_path)
        base_name = os.path.splitext(os.path.basename(data_path))[0]
        new_dir = os.path.join(data_dir, base_name)
        final_anim_name = temp_actor.LongName.split(':')[0] + '_Final_out.fbx'
        save_path = os.path.join(new_dir, final_anim_name)
        save_selected(save_path)
        self.final_anim_dict[save_path] = rig_path

    def run_retarget(self, split_export=True, all_export=True, *args, **kwargs):
        if self.final_anim_dict:
            values = list(self.final_anim_dict.keys())
            if values:
                one_anim_name = values[0]
                dir_path = os.path.dirname(one_anim_name)
                data_base_name = os.path.basename(dir_path)

                app = FBApplication()
                anim_paths = list()
                if split_export:
                    app.FileNew()
                    for final_path, rig_path in self.final_anim_dict.items():
                        open_file(rig_path)
                        cha_node = Character.current_character()
                        if not cha_node:
                            FBMessageBox("Error", "No character in scene", "OK")
                            raise RuntimeError("No character in scene")
                        cha_node.load_animation(final_path)

                        fb_folder_list = list(
                            filter(lambda x: x is not None and x.Is(FBFolder.TypeInfo), FBSystem().Scene.Components))
                        for fb_folder in fb_folder_list:
                            if not fb_folder.Items:
                                fb_folder.FBDelete()
                        actor_name = os.path.basename(final_path).replace('_Final_out.fbx', '')
                        rig_anim_name = '{data_base_name}_{actor_name}.fbx'.format(data_base_name=data_base_name, actor_name=actor_name)

                        clean_tab_001()
                        save_options = file_options.SaveAllOption(False)
                        output_file = six_encode_gb2312(os.path.join(dir_path, rig_anim_name))
                        app.FileSave(output_file, save_options)
                        os.remove(final_path)
                        anim_paths.append(output_file)

                if all_export:
                    app.FileNew()
                    for anim_path in anim_paths:
                        import_file_all(anim_path)
                        dir_path = os.path.dirname(anim_path)
                        data_base_name = os.path.basename(dir_path) + '_all.fbx'

                    clean_tab_001()
                    save_options = file_options.SaveAllOption(False)
                    output_file = six_encode_gb2312(os.path.join(dir_path, data_base_name))
                    app.FileSave(output_file, save_options)


def run_retarget(data_path, actor_files_path, rig_files_path, method='vicon', split_export=True, all_export=True, *args, **kwargs):
    retarget = FBActorRetarget(method)
    for actor_file, rig_path in zip(actor_files_path, rig_files_path):
        # actor part
        exist_actors = set(find_nodes_by_class(FBActor))
        exist_characters = set(find_nodes_by_class(FBCharacter))

        import_actor(actor_file)

        new_exist_actors = set(find_nodes_by_class(FBActor))
        new_exist_characters = set(find_nodes_by_class(FBCharacter))

        new_actors = list(new_exist_actors - exist_actors)
        new_characters = list(new_exist_characters - exist_characters)

        actor_temp = new_actors[0]
        character_temp = new_characters[0]

        retarget.run_export_final_out(data_path, actor_temp, character_temp, rig_path)
    retarget.run_retarget(split_export, all_export)