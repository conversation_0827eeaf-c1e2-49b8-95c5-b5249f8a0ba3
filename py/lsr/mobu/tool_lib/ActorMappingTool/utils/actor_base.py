import math
import os
import six


import lsr.mobu.rig.file_options as file_options
from lsr.mobu.utils.FindObjects import clear_selection
from pyfbsdk import FBTimeSpan, FBTime, FBSystem, FBVector3d, FBActor, FBVector4d, FBRecalcMarkerSetOffset, FBSkeletonNodeId, FBFbxOptions, \
    FBTake, FBApplication, FBElementAction, FBCharacter


def get_mb_version(*args, **kwargs):
    version = int(str(FBSystem().Version)[:2])
    return version


def find_nodes_by_class(cls, *args, **kwargs):
    """
    get all nodes by class
    :param cls:

    :return: fb nodes
    :rtype: list

    """
    return list(filter(lambda x: x is not None and x.Is(cls.TypeInfo), FBSystem().Scene.Components))


def find_character_by_name(name, *args, **kwargs):
    """
    get character node by name
    :param name:

    :return: fb character node
    :rtype: FBCharacter

    """
    return list(node for node in find_nodes_by_class(FBCharacter) if node.LongName == name)[0]


def all_components(*args, **kwargs):
    """
    get all nodes

    :return: fb nodes
    :rtype: list
    """
    return list(filter(lambda x: x, getattr(FBSystem().Scene, "Components")))


def get_selected_components(args, **kwargs):
    """
    get selected nodes

    :return: fb nodes selected
    :rtype: list
    """
    all_nodes = all_components()
    return list(node for node in all_nodes if node.Selected)


def get_center_by_2_points(point1, point2, *args, **kwargs):
    x1, y1, z1 = point1[0], point1[1], point1[2]
    x2, y2, z2 = point2[0], point2[1], point2[2]

    return FBVector3d((x1 + x2) / 2, (y1 + y2) / 2, (z1 + z2) / 2)


def get_points_len(point1, point2, *args, **kwargs):
    return ((point2[0] - point1[0]) ** 2 + (point2[1] - point1[1]) ** 2 + (point2[2] - point1[2]) ** 2) ** 0.5


def vector_plus(point1, point2, *args, **kwargs):
    return FBVector3d(point2[0] + point1[0], point2[1] + point1[1], point2[2] + point1[2])


def load_current_actors(*args, **kwargs):
    all_actors = find_nodes_by_class(FBActor)

    actor_dict = dict()
    for actor in all_actors:
        actor_dict[actor.LongName] = actor
    return actor_dict


def get_no_marker_set_actor(actors, *args, **kwargs):
    for actor in actors:
        marker_set = actor.PropertyList.Find("Marker Set")
        if not marker_set:
            return actor.LongName


def snap_actor(actor, *args, **kwargs):
    actor.Snap(FBRecalcMarkerSetOffset.kFBRecalcMarkerSetOffsetTR)


def save_selected(temp_path, *args, **kwargs):
    dir_name = os.path.dirname(temp_path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    save_options = file_options.SaveOption(False)
    app = FBApplication()
    temp_path = six_encode_gb2312(temp_path)
    app.FileSave(temp_path, save_options)


def open_file(file_path, *args, **kwargs):
    file_path = six_encode_gb2312(file_path)
    app = FBApplication()
    app.FileOpen(file_path, False)


def six_encode_gb2312(m_str, *args, **kwargs):
    if six.PY2:
        if type(m_str) == unicode:
            m_str = m_str.encode("gb2312")
    return m_str


def create_new_dir(dir_path, *args, **kwargs):
    if dir_path:
        dir_path = six_encode_gb2312(dir_path)
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)
    return dir_path


def get_all_markers(marker_set, *args, **kwargs):
    markers = list()
    for skeleton_id in dir(FBSkeletonNodeId):
        if skeleton_id.startswith("kFBSkeleton"):
            skeleton_index = getattr(FBSkeletonNodeId, skeleton_id)
            for num in range(marker_set.GetUsedMarkerCount(skeleton_index)):
                marker = marker_set.GetMarkerModel(skeleton_index, num)
                if marker:
                    markers.append(marker)
    return markers


def get_all_children(node, *args, **kwargs):
    all_children = list()
    extend_children(node, all_children)
    return all_children


def clean_tab_001(args, **kwargs):
    takes = find_nodes_by_class(FBTake)
    take_001 = list(node for node in find_nodes_by_class(FBTake) if node.LongName == 'Take 001')
    if take_001:
        take_001 = take_001[0]
    current_take = FBSystem().CurrentTake
    if current_take == take_001:
        for take in takes:
            if take != take_001:
                FBSystem().CurrentTake = take
                break
    FBSystem().Scene.Evaluate()
    take_001.FBDelete()


def extend_children(node, child_list, *args, **kwargs):
    if node not in child_list:
        child_list.append(node)
    for child in node.Children:
        child_list.append(child)
        extend_children(child, child_list)


def export_actor(actor, export_path, *args, **kwargs):
    if export_path:
        export_path = six_encode_gb2312(export_path)
    if not os.path.exists(export_path):
        os.makedirs(export_path)
    export_path = os.path.normpath(export_path)
    clear_selection()
    marker_set = actor.PropertyList.Find("Marker Set")[0]
    character = find_character_by_name(actor.LongName.split(":")[0] + ":Character")
    all_markers = get_all_markers(marker_set)
    top_grp = all_markers[0].Parent
    top_grp_children = get_all_children(top_grp)

    export_list = [actor, character, marker_set, top_grp]
    export_list.extend(all_markers)
    for node in top_grp_children:
        if node not in export_list:
            export_list.append(node)

    for node in export_list:
        node.Selected = True

    new_take = FBTake('Take 001')
    takes = FBSystem().Scene.Takes
    takes.append(new_take)

    save_options = file_options.SaveOption(False)
    for index in range(save_options.GetTakeCount()):
        if save_options.GetTakeName(index) == new_take.Name:
            save_options.SetTakeSelect(index, True)
        else:
            save_options.SetTakeSelect(index, False)
    actor_l_name = actor.LongName
    if ':' in actor_l_name:
        actor_name = actor_l_name.split(':')[0]
        file_name = actor_name + '_Actor'
    else:
        file_name = actor.Name
    save_path = six_encode_gb2312(os.path.join(export_path, file_name))
    FBApplication().FileSave(save_path, save_options)
    new_take.FBDelete()
    clear_selection()


def import_actor(import_path, *args, **kwargs):
    takes = list(take.LongName for take in FBSystem().Scene.Takes)

    current_take = FBSystem().CurrentTake
    start_time = current_take.LocalTimeSpan.GetStart().GetFrame()
    end_time = current_take.LocalTimeSpan.GetStop().GetFrame()

    options = FBFbxOptions(False)
    options.SetAll(FBElementAction.kFBElementActionDiscard, False)
    options.Actors = FBElementAction.kFBElementActionMerge
    options.Characters = FBElementAction.kFBElementActionMerge
    import_path = six_encode_gb2312(import_path)
    FBApplication().FileMerge(import_path, False, options)

    new_takes = FBSystem().Scene.Takes
    for take in new_takes:
        if take.LongName not in takes:
            take.FBDelete()

    FBSystem().CurrentTake = current_take
    current_take.LocalTimeSpan = FBTimeSpan(FBTime(0, 0, 0, start_time),
                                            FBTime(0, 0, 0, end_time))


def import_file_all(import_path, *args, **kwargs):
    current_take = FBSystem().CurrentTake
    start_time = current_take.LocalTimeSpan.GetStart().GetFrame()
    end_time = current_take.LocalTimeSpan.GetStop().GetFrame()

    options = FBFbxOptions(False)
    options.SetAll(FBElementAction.kFBElementActionAppend, True)
    import_path = six_encode_gb2312(import_path)
    FBApplication().FileMerge(import_path, False, options)

    FBSystem().CurrentTake = current_take
    current_take.LocalTimeSpan = FBTimeSpan(FBTime(0, 0, 0, start_time),
                                            FBTime(0, 0, 0, end_time))


# math
def clamp(value, min_value, max_value, *args, **kwargs):
    return max(min_value, min(max_value, value))


def get_position_by_percent(point1, point2, percent, *args, **kwargs):
    x1, y1, z1 = point1[0], point1[1], point1[2]
    x2, y2, z2 = point2[0], point2[1], point2[2]
    return FBVector3d((x2 - x1) * percent + x1, (y2 - y1) * percent + y1, (z2 - z1) * percent + z1)


def vector_minus(point1, point2, *args, **kwargs):
    return FBVector3d((point2[0] - point1[0]), (point2[1] - point1[1]), (point2[2] - point1[2]))


class Quaternion:
    def __init__(self, FBVector, *args, **kwargs):
        self.x = FBVector[0]
        self.y = FBVector[1]
        self.z = FBVector[2]
        self.w = FBVector[3]

    @staticmethod
    def Identity(args, **kwargs):
        return Quaternion([0, 0, 0, 1])

    def xyzw(self, *args, **kwargs):
        return FBVector4d(self.x, self.y, self.z, self.w)

    def xyz(self, *args, **kwargs):
        return FBVector3d(self.x, self.y, self.z)

    def __getitem__(self, key):
        if key == 0:
            return self.x
        elif key == 1:
            return self.y
        elif key == 2:
            return self.z
        elif key == 3:
            return self.w

    def __add__(self, quaternion):
        result = Quaternion(self.xyzw())
        result.x += quaternion.x
        result.y += quaternion.y
        result.z += quaternion.z
        result.w += quaternion.w
        return result

    def __sub__(self, quaternion):
        result = Quaternion(self.xyzw())
        result.x -= quaternion.x
        result.y -= quaternion.y
        result.z -= quaternion.z
        result.w -= quaternion.w
        return result

    def __mul__(self, quaternion):
        result = Quaternion(self.xyzw())
        result.x = self.w * quaternion.x + self.x * quaternion.w - self.y * quaternion.z + self.z * quaternion.y
        result.y = self.w * quaternion.y + self.y * quaternion.w + self.x * quaternion.z - self.z * quaternion.x
        result.z = self.w * quaternion.z + self.z * quaternion.w - self.x * quaternion.y + self.y * quaternion.x
        result.w = self.w * quaternion.w - self.x * quaternion.x - self.y * quaternion.y - self.z * quaternion.z
        return result

    def Divides(self, quaternion, *args, **kwargs):
        result = Quaternion(self.xyzw())
        return result * (quaternion.Inversed())

    def MagnitudeSqr(self, *args, **kwargs):
        return pow(self.x, 2) + pow(self.y, 2) + pow(self.z, 2) + pow(self.w, 2)

    def Magnituded(self, *args, **kwargs):
        return pow(self.MagnitudeSqr(), 0.5)

    def Star(self, *args, **kwargs):
        result = Quaternion(self.xyzw())
        result.x = -self.x
        result.y = -self.y
        result.z = -self.z
        result.w = self.w
        return result

    def Inversed(self, *args, **kwargs):
        result = Quaternion(self.xyzw())
        result = result.Star()
        moder = self.MagnitudeSqr()
        if moder > 0.00001:
            result.x /= moder
            result.y /= moder
            result.z /= moder
            result.w /= moder
            return result
        else:
            return self.Identity()

    def __str__(self):
        return str(self.x) + " " + str(self.y) + " " + str(self.z) + " " + str(self.w)

    def Normalize(self, *args, **kwargs):
        moder = self.Magnituded()
        if moder > 0.00001:
            self.x /= moder
            self.y /= moder
            self.z /= moder
            self.w /= moder
        else:
            return self.Identity()

    def Normalized(self, *args, **kwargs):
        result = Quaternion(self.xyzw())
        moder = result.Magnituded()
        if moder > 0.00001:
            result.x /= moder
            result.y /= moder
            result.z /= moder
            result.w /= moder
            return result
        else:
            return self.Identity()

    @staticmethod
    def FromToRotation(fromDirection, toDirection, *args, **kwargs):
        if fromDirection == FBVector3d(0, 0, 0) or toDirection == FBVector3d(0, 0, 0):
            return Quaternion.Identity()
        fromDirection.Normalize()
        toDirection.Normalize()
        if FBVector3d.IsEqual(fromDirection, toDirection):
            return Quaternion.Identity()
        aixs = FBVector3d.CrossProduct(fromDirection, toDirection)
        sinA = aixs.Length()
        cosA = FBVector3d.DotProduct(fromDirection, toDirection)
        sinHalfA = pow(((1 - cosA) * 0.5), 0.5)
        cosHalfA = sinA / (2.0 * sinHalfA)
        aixs.Normalize()
        aixs = aixs * sinHalfA
        return Quaternion(FBVector4d(aixs[0], aixs[1], aixs[2], cosHalfA))

    @staticmethod
    def FromEular(eular, *args, **kwargs):
        Deg2Rad = math.pi / 180.0
        Cx = math.cos(eular[0] / 2.0 * Deg2Rad)
        Cy = math.cos(eular[1] / 2.0 * Deg2Rad)
        Cz = math.cos(eular[2] / 2.0 * Deg2Rad)

        Sx = math.sin(eular[0] / 2.0 * Deg2Rad)
        Sy = math.sin(eular[1] / 2.0 * Deg2Rad)
        Sz = math.sin(eular[2] / 2.0 * Deg2Rad)

        qX = Quaternion(FBVector4d(Sx, 0.0, 0.0, Cx))
        qY = Quaternion(FBVector4d(0.0, Sy, 0.0, Cy))
        qZ = Quaternion(FBVector4d(0.0, 0.0, Sz, Cz))
        result = qX * qY * qZ  # MotionBulider RotateOrder xyz
        return result

    def RotateDirection(self, direction, *args, **kwargs):
        result = Quaternion(self.xyzw())
        result.Normalize()
        qDirection = Quaternion(FBVector4d(direction[0], direction[1], direction[2], 0))
        resultDirection = result.Inversed() * qDirection * result
        return FBVector3d(resultDirection.x, resultDirection.y, resultDirection.z)

    def Euler(self, *args, **kwargs):
        euler = FBVector3d(0.0, 0.0, 0.0)
        q = Quaternion(self.xyzw())
        q.Normalize()
        euler[0] = math.atan2(2 * (q.w * q.x + q.y * q.z), 1 - 2 * (q.x * q.x + q.y * q.y))
        euler[1] = math.asin(clamp(2 * (q.w * q.y - q.x * q.z), -1, 1))
        euler[2] = math.atan2(2 * (q.w * q.z + q.x * q.y), 1 - 2 * (q.y * q.y + q.z * q.z))
        Rad2Deg = 180.0 / math.pi
        euler *= Rad2Deg
        return euler
