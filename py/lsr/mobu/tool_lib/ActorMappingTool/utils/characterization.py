from pyfbsdk import FBModelSkeleton, FBVector3d, FBModelTransformationType, \
    FBCharacter, FBSystem

from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_base import clear_selection
from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_base import get_all_markers
from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_base import find_nodes_by_class


class ViconCharacterization(object):

    characterize_dict = {
        "Hips": "Hips",
        "Spine": "Spine",
        "Spine1": "Spine1",
        "Spine2": "Spine2",
        "Spine3": "Spine3",
        "Neck": "Neck",
        "Neck1": "Neck1",
        "Head": "Head",

        "RightShoulder": "RightShoulder",
        "RightArm": "RightArm",
        "RightForeArm": "RightForeArm",
        "RightHand": "RightHand",
        "RightHandMiddle1": "RightHandMiddle1",
        "RightHandMiddle2": "RightHandMiddle2",
        "RightHandMiddle3": "RightHandMiddle3",
        "RightHandMiddle4": "RightHandMiddle4",
        "RightHandRing": "RightInHandRing",
        "RightHandRing1": "RightHandRing1",
        "RightHandRing2": "RightHandRing2",
        "RightHandRing3": "RightHandRing3",
        "RightHandRing4": "RightHandRing4",
        "RightHandIndex": "RightInHandIndex",
        "RightHandIndex1": "RightHandIndex1",
        "RightHandIndex2": "RightHandIndex2",
        "RightHandIndex3": "RightHandIndex3",
        "RightHandIndex4": "RightHandIndex4",
        "RightHandThumb1": "RightHandThumb1",
        "RightHandThumb2": "RightHandThumb2",
        "RightHandThumb3": "RightHandThumb3",
        "RightHandThumb4": "RightHandThumb4",
        "RightHandPinky": "RightInHandPinky",
        "RightHandPinky1": "RightHandPinky1",
        "RightHandPinky2": "RightHandPinky2",
        "RightHandPinky3": "RightHandPinky3",
        "RightHandPinky4": "RightHandPinky4",

        "RightUpLeg": "RightUpLeg",
        "RightLeg": "RightLeg",
        "RightFoot": "RightFoot",

        "LeftShoulder": "LeftShoulder",
        "LeftArm": "LeftArm",
        "LeftForeArm": "LeftForeArm",
        "LeftHand": "LeftHand",
        "LeftHandMiddle1": "LeftHandMiddle1",
        "LeftHandMiddle2": "LeftHandMiddle2",
        "LeftHandMiddle3": "LeftHandMiddle3",
        "LeftHandMiddle4": "LeftHandMiddle4",
        "LeftHandRing": "LeftInHandRing",
        "LeftHandRing1": "LeftHandRing1",
        "LeftHandRing2": "LeftHandRing2",
        "LeftHandRing3": "LeftHandRing3",
        "LeftHandRing4": "LeftHandRing4",
        "LeftHandIndex": "LeftInHandIndex",
        "LeftHandIndex1": "LeftHandIndex1",
        "LeftHandIndex2": "LeftHandIndex2",
        "LeftHandIndex3": "LeftHandIndex3",
        "LeftHandIndex4": "LeftHandIndex4",
        "LeftHandThumb1": "LeftHandThumb1",
        "LeftHandThumb2": "LeftHandThumb2",
        "LeftHandThumb3": "LeftHandThumb3",
        "LeftHandThumb4": "LeftHandThumb4",
        "LeftHandPinky": "LeftInHandPinky",
        "LeftHandPinky1": "LeftHandPinky1",
        "LeftHandPinky2": "LeftHandPinky2",
        "LeftHandPinky3": "LeftHandPinky3",
        "LeftHandPinky4": "LeftHandPinky4",

        "LeftUpLeg": "LeftUpLeg",
        "LeftLeg": "LeftLeg",
        "LeftFoot": "LeftFoot",
        "RightToeBase": "RightToeBase",
        "LeftToeBase": "LeftToeBase",

    }

    def __init__(self, character_name, actor, *args, **kwargs):
        self.actor = actor
        self.character_name = character_name
        self._init_obj()

        self._run()

    def _init_obj(self, *args, **kwargs):
        clear_selection()
        marker_sets = self.actor.PropertyList.Find("Marker Set")
        if not marker_sets:
            raise Exception("Illegal Actor Input: No Marker Set")

        marker_set = marker_sets[0]
        all_markers = get_all_markers(marker_set)
        top_grp = all_markers[0].Parent

        self.obj_dict = self._get_obj_hierarchy(top_grp)

    def _get_obj_hierarchy(self, top_grp, *args, **kwargs):
        obj_dict = dict()
        obj_dict[top_grp] = dict()
        self._extend_obj(top_grp, obj_dict[top_grp])
        return obj_dict

    def _extend_obj(self, node, obj_dict, *args, **kwargs):
        for child in node.Children:
            obj_dict[child] = dict()
            self._extend_obj(child, obj_dict[child])

    def set_t_pose(self, *args, **kwargs):
        self.set_joint_zero(self.obj_dict)

    def set_joint_zero(self, obj_dict, *args, **kwargs):
        for joint, child_joints_dict in obj_dict.items():
            if isinstance(joint, FBModelSkeleton):
                if joint.Name == 'Hips':
                    trans = FBVector3d()
                    joint.GetVector(trans,
                                    FBModelTransformationType.kModelTranslation,
                                    True)
                    new_trans = FBVector3d(0, trans[1], 0)
                    joint.SetVector(FBVector3d(90, 0, 180),
                                    FBModelTransformationType.kModelRotation,
                                    True)
                    joint.SetVector(new_trans,
                                    FBModelTransformationType.kModelTranslation,
                                    True)
                else:
                    joint.SetVector(FBVector3d(0, 0, 0),
                                    FBModelTransformationType.kModelRotation,
                                    False)
            self.set_joint_zero(child_joints_dict)

    def characterize(self, *args, **kwargs):
        exist_characters = find_nodes_by_class(FBCharacter)
        for character in exist_characters:
            character_long_name = character.LongName
            if ':' in character_long_name:
                if character.LongName == self.character_name + ":Character":
                    character.FBDelete()
                    break
        new_character = FBCharacter(self.character_name + ":Character")

        self.set_joint_link(new_character, self.obj_dict)
        new_character.SetCharacterizeOn(True)

    def set_joint_link(self, character, obj_dict, *args, **kwargs):
        joint_names_map = list(self.characterize_dict.keys())
        for joint, child_joints_dict in obj_dict.items():
            if isinstance(joint, FBModelSkeleton):
                if joint.Name in joint_names_map:
                    slot_name = self.characterize_dict[joint.Name]
                    slot_property = character.PropertyList.Find(slot_name+'Link')
                    slot_property.append(joint)
            self.set_joint_link(character, child_joints_dict)
        FBSystem().Scene.Evaluate()

    def _run(self, *args, **kwargs):
        self.set_t_pose()
        self.characterize()
