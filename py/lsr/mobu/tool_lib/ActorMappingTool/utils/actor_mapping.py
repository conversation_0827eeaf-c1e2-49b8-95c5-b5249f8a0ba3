from pyfbsdk import FBMarkerSet, FBSkeletonNodeId, FBVector3d, FBModelMarker
from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_base import *
from lsr.mobu.nodezoo.node import Node
from lsr.mobu.tool_lib.ActorMappingTool.marker_config import vicon_marker
from lsr.mobu.tool_lib.ActorMappingTool.utils.characterization import ViconCharacterization


class MarkerManager(object):
    """ marker manager class """

    def __init__(self, **kwargs):
        self.kwargs = kwargs
        self.marker_type = self.kwargs.get('marker_type', 'vicon')
        self.config_path = self.kwargs.get('config_path', '')

        self.markers_index = dict()
        self.character_markers = dict()
        self.character_actor = dict()

        # get ready
        self._prepare_attr()

    def _prepare_attr(self, *args, **kwargs):
        self._activate_marker()
        self._get_characters_marker()

    def _activate_marker(self, *args, **kwargs):
        """
        get markers by marker type

        :return: None
        """
        markers_index = dict()
        if self.marker_type == 'vicon':
            markers_index = vicon_marker
        elif self.marker_type == 'optitrack':
            pass

        self.markers_index = markers_index

    def _get_characters_marker(self, *args, **kwargs):
        """
        get character root

        :return: None
        """
        markers = find_nodes_by_class(FBModelMarker)

        if self.marker_type == 'vicon':
            self._deal_vicon_marker(markers)

    def _deal_vicon_marker(self, markers, *args, **kwargs):
        """
        deal vicon marker

        :param list markers: markers list

        :return: None

        """
        character_markers = dict()
        all_marker_map_names = list()
        for map_name_list in self.markers_index.values():
            all_marker_map_names.extend(map_name_list)

        for marker in markers:
            if any(map_name in marker.Name for map_name in all_marker_map_names):
                if marker.Parent and marker.Parent.Parent is None:  # marker's parent is a root
                    if marker.Parent.Name not in character_markers:
                        character_markers[marker.Parent.Name] = list()
                    character_markers[marker.Parent.Name].append(marker)

        self.character_markers = character_markers

    def create_actor(self, char_names=list(), *args, **kwargs):
        """
        create actor by character markers

        """
        if char_names:
            for char_name in char_names:
                self._create_char_actor(actor_name=char_name)

    def _create_char_actor(self, actor_name='', *args, **kwargs):
        """
        set marker set by actor name

        :param str actor_name: actor name

        :return: None
        """
        actor_full_name = actor_name + ':Actor'
        marker_set_full_name = actor_name + ':MarkerSet'

        actor_exist = find_nodes_by_class(FBActor)
        for actor in actor_exist:
            if actor.LongName == actor_full_name:
                actor.FBDelete()

        marker_set_exist = find_nodes_by_class(FBMarkerSet)
        for marker_set in marker_set_exist:
            if marker_set.LongName == marker_set_full_name:
                marker_set.FBDelete()

        actor = FBActor(actor_full_name)
        marker_set = FBMarkerSet(marker_set_full_name)
        actor.PropertyList.Find("Marker Set").append(marker_set)
        self.character_actor[actor_name] = actor
        for index_name, item_name_list in self.markers_index.items():
            self._marker_set_add_marker(actor_name, marker_set, index_name, item_name_list)

    def _marker_set_add_marker(self, actor_name, marker_set, index_name, marker_map_list, *args, **kwargs):
        """
        set marker set
        :param str actor_name: actor name
        :param FBMarkerSet marker_set: marker set
        :param str index_name: index name
        :param list marker_map_list: marker map list

        """
        idx = getattr(FBSkeletonNodeId, "kFBSkeleton{key}Index".format(key=index_name))
        if len(marker_map_list) > 5:
            marker_map_list = marker_map_list[:5]
        marker_list = self.character_markers[actor_name]
        for marker in marker_list:
            if any(map_name in marker.Name for map_name in marker_map_list):
                marker_set.AddMarker(idx, marker)

    def set_actor(self, *args, **kwargs):
        """
        set actor

        """
        current_take = FBSystem().CurrentTake
        takes = FBSystem().Scene.Takes
        new_take = FBTake('Temp take')
        takes.append(new_take)
        FBSystem().CurrentTake = new_take

        for char_name, actor in self.character_actor.items():

            ViconActorSetting(char_name, actor, self.character_markers[char_name], self.markers_index)
            ViconCharacterization(char_name, actor)

        new_take.FBDelete()
        FBSystem().CurrentTake = current_take


class ActorSet(object):

    def __init__(self, actor_name, actor, markers, marker_index, *args, **kwargs):
        self.actor_name = actor_name
        self.actor = actor
        self.markers = markers
        self.marker_index = marker_index

    def get_marker_translate(self, marker_index_name, *args, **kwargs):
        """
        get marker translate
        :param str marker_index_name: marker index name

        :return: FBVector3d
        :rtype: FBVector3d

        """
        for marker in self.markers:
            if marker_index_name == marker.Name:
                return Node(marker.LongName).get_translation(space='world')

    def get_marker_vector_center(self, marker_index_01, marker_index_02, *args, **kwargs):
        """
        get marker vector center by 2 markers
        :param str marker_index_01: marker index name
        :param str marker_index_02: marker index name

        :return: FBVector3d
        :rtype: FBVector3d

        """
        vector_01 = self.get_marker_translate(marker_index_01)
        vector_02 = self.get_marker_translate(marker_index_02)
        return get_center_by_2_points(vector_01, vector_02)

    @staticmethod
    def get_actor_index(index_name, *args, **kwargs):
        """
        get actor index by index name

        :param str index_name: index name

        :return: actor index
        :rtype: FBSkeletonNodeId

        """
        return getattr(FBSkeletonNodeId, "kFBSkeleton{key}Index".format(key=index_name))

    def set_part_position(self, index_name, value, *args, **kwargs):
        """
        set actor part position
        :param str index_name: actor part name
        :param FBVector3d value: actor part position

        """
        if index_name == "Hips":
            self.actor.SetActorTranslation(value)
        else:
            setattr(self.actor, index_name + "Position", value)

    def set_part_rotation(self, index_name, value, *args, **kwargs):
        """
        set actor part rotation
        :param str index_name: actor part name
        :param FBVector3d value: actor part position

        """
        idx = self.get_actor_index(index_name)
        self.actor.SetDefinitionRotationVector(idx, value, False)

    def set_part_scaling(self, index_name, value, *args, **kwargs):
        """
        set actor part scaling
        :param str index_name: actor part name
        :param FBVector3d value: actor part position

        """
        idx = self.get_actor_index(index_name)
        self.actor.SetDefinitionScaleVector(idx, value)

    def get_part_position(self, index_name, *args, **kwargs):
        """
        get actor part position
        :param str index_name: actor part name

        :return: actor part position
        :rtype: FBVector3d
        """
        FBSystem().Scene.Evaluate()
        value = FBVector3d()
        idx = self.get_actor_index(index_name)
        self.actor.GetDefinitionTranslationVector(idx, value)
        return value

    def get_part_rotation(self, index_name, *args, **kwargs):
        """
        get actor part rotation
        :param str index_name: actor part name

        :return: actor part rotation
        :rtype: FBVector3d
        """
        value = FBVector3d()
        idx = self.get_actor_index(index_name)
        self.actor.GetDefinitionRotationVector(idx, value)
        return value

    def get_part_scale(self, index_name, *args, **kwargs):
        """
        get actor part scale
        :param str index_name: actor part name

        :return: actor part scale
        :rtype: FBVector3d
        """
        value = FBVector3d()
        idx = self.get_actor_index(index_name)
        self.actor.GetDefinitionScaleVector(idx, value)
        return value


class ViconActorSetting(ActorSet):
    """ vicon actor setting class """

    def __init__(self, actor_name, actor, markers, marker_index, *args, **kwargs):
        super(ViconActorSetting, self).__init__(actor_name, actor, markers, marker_index)
        self._set_actor()

    @staticmethod
    def _pelvis_percent(point1, point2, *args, **kwargs):
        return get_points_len(point1, point2) / 24

    def _get_pelvis_position(self, *args, **kwargs):
        """
        get vicon actor pelvis position

        :return: FBVector3d
        :rtype: FBVector3d

        """
        pelvis_index = self.marker_index['Hips']
        pelvis_existed = list()
        for index_name in pelvis_index:
            for marker in self.markers:
                if index_name in marker.Name:
                    pelvis_existed.append(index_name)

        if float(len(pelvis_existed)) % 2.0 != 0:
            pelvis_existed.pop(-1)

        vector_list = list()
        for num in range(int(len(pelvis_existed) / 2)):
            vector_list.append(self.get_marker_vector_center(pelvis_existed[num * 2], pelvis_existed[num * 2 + 1]))

        pelvis_position = vector_list[-1]
        vector_list.pop(-1)
        while len(vector_list) > 0:
            pelvis_position = get_center_by_2_points(pelvis_position, vector_list[-1])
            vector_list.pop(-1)

        pelvis_percent = self._pelvis_percent(self.get_marker_translate(pelvis_index[2]),
                                              self.get_marker_translate(pelvis_index[4]))

        # pelvis offset
        offset_x = -0.456118 * pelvis_percent
        offset_y = -7.02037 * pelvis_percent
        offset_z = -11 * pelvis_percent
        offset_vector = (offset_x, offset_y, offset_z)

        return vector_plus(pelvis_position, offset_vector)

    def _get_marker_between_by_percent(self, marker_idx_01, marker_idx_02, percent, *args, **kwargs):
        marker_position_01 = self.get_marker_translate(marker_idx_01)
        marker_position_02 = self.get_marker_translate(marker_idx_02)
        return get_position_by_percent(marker_position_01, marker_position_02, percent)

    def _set_actor(self, *args, **kwargs):
        old_hip_position_y = self.get_part_position("LeftHip")[1]

        # set pelvis
        self._set_pelvis()
        FBSystem().Scene.Evaluate()

        # get scale_y
        new_hip_position_y = self.get_part_position("LeftHip")[1]
        # try:
        #     scale_y = new_hip_position_y / old_hip_position_y
        # except ZeroDivisionError:
        #     scale_y = new_hip_position_y / 0.0000001
        scale_y = new_hip_position_y / old_hip_position_y

        # set all parts
        self._set_hips(scale_y)
        self._set_knees(scale_y)
        self._set_ankle()
        self._set_waist()
        self._set_chest()
        self._set_neck()
        self._set_head()
        self._set_collar()
        self._set_shoulder()
        self._set_elbow()
        self._set_wrist()

    def _set_pelvis(self, *args, **kwargs):
        pelvis_position = self._get_pelvis_position()
        self.set_part_position("Hips", pelvis_position)
        self.set_part_rotation("Hips", FBVector3d(0, 0, 0))

    # hips
    def _get_single_hip_modify_rotation(self, hip_position, knee_position, fwt_idx, bwt_idx, hel_idx, mt1_idx, *args, **kwargs):
        marker_position_01 = self.get_marker_vector_center(fwt_idx, bwt_idx)
        marker_position_02 = self._get_marker_between_by_percent(hel_idx, mt1_idx, 0.4)
        actor_vector = vector_minus(hip_position, knee_position)
        marker_vector = vector_minus(marker_position_01, marker_position_02)
        rotation = Quaternion.FromToRotation(actor_vector, marker_vector).Euler()
        return rotation

    def _set_hips(self, scale_y, *args, **kwargs):
        self.set_part_rotation('LeftHip', FBVector3d(0, 0, 0))
        self.set_part_rotation('RightHip', FBVector3d(0, 0, 0))
        # rotation
        left_hip_position = self.get_part_position('LeftHip')
        right_hip_position = self.get_part_position('RightHip')
        left_knee_position = self.get_part_position('LeftKnee')
        right_knee_position = self.get_part_position('RightKnee')
        left_hip_rotation = self._get_single_hip_modify_rotation(left_hip_position, left_knee_position, 'LFWT', 'LBWT', 'LHEL', 'LMT1')
        right_hip_rotation = self._get_single_hip_modify_rotation(right_hip_position, right_knee_position, 'RFWT', 'RBWT', 'RHEL', 'RMT1')
        self.set_part_rotation('LeftHip', left_hip_rotation)
        self.set_part_rotation('RightHip', right_hip_rotation)
        # scale
        self.set_part_scaling('LeftHip', FBVector3d(1, scale_y, 1))
        self.set_part_scaling('RightHip', FBVector3d(1, scale_y, 1))

    # knees
    def _set_knees(self, scale_y, *args, **kwargs):
        # knee do not need to set rotation
        # scale
        self.set_part_scaling('LeftKnee', FBVector3d(1, scale_y, 1))

    # ankles
    def _get_single_ankle_modify_rotation(self, ankle_position, foot_position, mt5_idx, hel_idx, mt1_idx, *args, **kwargs):
        # ankle's position is not correct, so we use foot's position to calculate
        ankle_position = FBVector3d(ankle_position[0], foot_position[1], ankle_position[2])
        marker_position_01 = self.get_marker_translate(hel_idx)
        marker_position_02 = self.get_marker_vector_center(mt5_idx, mt1_idx)
        actor_vector = vector_minus(ankle_position, foot_position)
        marker_vector = vector_minus(marker_position_01, marker_position_02)
        rotation = Quaternion.FromToRotation(actor_vector, marker_vector).Euler()
        return rotation

    def _set_ankle(self, *args, **kwargs):
        self.set_part_rotation('LeftAnkle', FBVector3d(0, 0, 0))
        self.set_part_rotation('RightAnkle', FBVector3d(0, 0, 0))
        # rotation
        left_ankle_position = self.get_part_position('LeftAnkle')
        right_ankle_position = self.get_part_position('RightAnkle')
        left_foot_position = self.get_part_position('LeftFoot')
        right_foot_position = self.get_part_position('RightFoot')
        left_hip_rotation = self._get_single_ankle_modify_rotation(left_ankle_position, left_foot_position, 'LMT5', 'LHEL', 'LMT1')
        right_hip_rotation = self._get_single_ankle_modify_rotation(right_ankle_position, right_foot_position, 'RMT5', 'RHEL', 'RMT1')
        self.set_part_rotation('LeftAnkle', FBVector3d(0, left_hip_rotation[1], 0))
        self.set_part_rotation('RightAnkle', FBVector3d(0, right_hip_rotation[1], 0))

    # waist
    def _get_waist_modify_rotation(self, *args, **kwargs):
        waist_position = self.get_part_position('Waist')
        chest_position = self.get_part_position('Chest')
        marker_position1 = self.get_part_position('Hips')

        marker_position2 = self._get_marker_between_by_percent('T10', 'STRN', 0.2)
        actor_vector = vector_minus(chest_position, waist_position)
        marker_vector = vector_minus(marker_position2, marker_position1)
        rotation = Quaternion.FromToRotation(actor_vector, marker_vector).Euler()
        return rotation

    def _get_waist_modify_scale(self, *args, **kwargs):
        position_01 = self.get_marker_vector_center('RMWT', 'LMWT')
        position_02 = self.get_marker_translate('T10')
        marker_len = get_points_len(position_01, position_02)
        actor_len = get_points_len(self.get_part_position('Waist'), self.get_part_position('Chest'))
        scale_y = marker_len / actor_len / 2.0
        return scale_y

    def _set_waist(self, *args, **kwargs):
        # rotation
        self.set_part_rotation('Waist', FBVector3d(0, 0, 0))
        waist_rotation = self._get_waist_modify_rotation()
        self.set_part_rotation('Waist', waist_rotation)
        # scale
        scale_y = self._get_waist_modify_scale()
        self.set_part_scaling('Waist', FBVector3d(1, scale_y, 1))

    # chest
    def _get_chest_modify_rotation(self, *args, **kwargs):
        chest_position = self.get_part_position('Chest')
        neck_position = self.get_part_position('Neck')
        marker_position1 = self.get_marker_translate('T10')
        marker_position2 = self.get_marker_translate('C7')
        actor_vector = vector_minus(chest_position, neck_position)
        marker_vector = vector_minus(marker_position1, marker_position2)
        rotation = Quaternion.FromToRotation(actor_vector, marker_vector).Euler()
        return rotation

    def _get_chest_modify_scale(self, *args, **kwargs):
        chest_position = self.get_part_position('Chest')
        neck_position = self.get_part_position('Neck')
        marker_center01, marker_center02 = \
            self.get_marker_vector_center('LMWT', 'RMWT'), \
            self.get_marker_vector_center('LTSH', 'RTSH')
        marker_len = get_points_len(marker_center01, marker_center02)
        actor_len = get_points_len(chest_position, neck_position)
        scale_y = marker_len / actor_len * 0.525
        return scale_y

    def _set_chest(self, *args, **kwargs):
        self.set_part_rotation('Chest', FBVector3d(0, 0, 0))
        # rotation
        chest_rotation = self._get_chest_modify_rotation()
        self.set_part_rotation('Chest', chest_rotation)
        # scale
        scale_y = self._get_chest_modify_scale()
        self.set_part_scaling('Chest', FBVector3d(1, scale_y, 1))

    # neck
    def _get_neck_modify_rotation(self, *args, **kwargs):
        neck_position = self.get_part_position('Neck')
        head_position = self.get_part_position('Head')
        marker_position_01 = neck_position
        center_01 = self.get_marker_vector_center('LFHD', 'RFHD')
        center_02 = self.get_marker_vector_center('RBHD', 'LBHD')
        marker_position_02 = get_position_by_percent(center_01, center_02, 0.35)
        actor_vector = vector_minus(neck_position, head_position)
        marker_vector = vector_minus(marker_position_01, marker_position_02)
        rotation = Quaternion.FromToRotation(actor_vector, marker_vector).Euler()
        return rotation

    def _get_neck_modify_scale(self, *args, **kwargs):
        neck_position = self.get_part_position('Neck')
        head_position = self.get_part_position('Head')
        c7_position = self.get_marker_translate('C7')
        ariel_position = self.get_marker_translate('ARIEL')
        marker_len = get_points_len(c7_position, ariel_position)
        real_len = ((marker_len**2)/2)**0.5
        actor_len = get_points_len(neck_position, head_position)
        scale_y = real_len / actor_len / 1.67
        return scale_y

    def _set_neck(self, *args, **kwargs):
        # rotation
        neck_rotation = self._get_neck_modify_rotation()
        self.set_part_rotation('Neck', neck_rotation)
        # scale
        scale_y = self._get_neck_modify_scale()
        self.set_part_scaling('Neck', FBVector3d(1, scale_y, 1))

    # head
    def _set_head(self, *args, **kwargs):
        # rotation is not need to set
        # scale
        # head scale be same as neck
        scale_y = self._get_neck_modify_scale() * 1.08
        self.set_part_scaling('Head', FBVector3d(scale_y, scale_y, scale_y))

    # collar
    def _get_single_collar_modify_rotation(self, collar_position, shoulder_position, upa_idx, *args, **kwargs):
        marker_position_01 = collar_position
        marker_position_02 = self.get_marker_translate(upa_idx)
        actor_vector = vector_minus(collar_position, shoulder_position)
        marker_vector = vector_minus(marker_position_01, marker_position_02)
        rotation = Quaternion.FromToRotation(actor_vector, marker_vector).Euler()
        return rotation

    def _set_collar(self, *args, **kwargs):
        # rotation
        left_collar_position = self.get_part_position('LeftCollar')
        right_collar_position = self.get_part_position('RightCollar')
        left_shoulder_position = self.get_part_position('LeftShoulder')
        right_shoulder_position = self.get_part_position('RightShoulder')
        left_shoulder_rotation = self._get_single_collar_modify_rotation(left_collar_position, left_shoulder_position, "LUPA")
        right_shoulder_rotation = self._get_single_collar_modify_rotation(right_collar_position, right_shoulder_position, "RUPA")
        self.set_part_rotation('LeftCollar', left_shoulder_rotation)
        self.set_part_rotation('RightCollar', right_shoulder_rotation)
        # scale pass

    # shoulder
    def _get_single_shoulder_modify_rotation(self, shoulder_position, elbow_position, shoulder_rotation, bsh_idx, tsh_idx, elb_idx, *args, **kwargs):
        marker_position_01 = self.get_marker_vector_center(bsh_idx, tsh_idx)
        marker_position_02 = self.get_marker_translate(elb_idx)
        actor_vector = vector_minus(shoulder_position, elbow_position)
        marker_vector = vector_minus(marker_position_01, marker_position_02)
        rotation = Quaternion.FromToRotation(actor_vector, marker_vector).Euler()
        return rotation

    def _get_signle_shoulder_modify_scale(self, shoulder_position, elbow_position, elb_idx, bel_idx, bsh_idx, *args, **kwargs):
        marker_position_01 = self.get_marker_vector_center(elb_idx, bel_idx)
        marker_position_02 = self.get_marker_translate(bsh_idx)
        marker_len = get_points_len(marker_position_01, marker_position_02)
        _len = get_points_len(shoulder_position, elbow_position)
        scale_x = marker_len / _len * 0.85
        return scale_x

    def _set_shoulder(self, *args, **kwargs):
        # rotation
        # zero rotation before set
        self.set_part_rotation('LeftShoulder', FBVector3d(0, 0, 0))
        self.set_part_rotation('RightShoulder', FBVector3d(0, 0, 0))
        left_shoulder_position = self.get_part_position('LeftShoulder')
        right_shoulder_position = self.get_part_position('RightShoulder')
        left_shoulder_rotation = self.get_part_rotation('LeftShoulder')
        right_shoulder_rotation = self.get_part_rotation('RightShoulder')
        left_elbow_position = self.get_part_position('LeftElbow')
        right_elbow_position = self.get_part_position('RightElbow')
        left_shoulder_rotation = self._get_single_shoulder_modify_rotation(
            left_shoulder_position, left_elbow_position, left_shoulder_rotation, 'LBSH', 'LTSH', 'LELB')
        right_shoulder_rotation = self._get_single_shoulder_modify_rotation(
            right_shoulder_position, right_elbow_position, right_shoulder_rotation, 'RBSH', 'LTSH', 'RELB')
        self.set_part_rotation('LeftShoulder', left_shoulder_rotation)
        self.set_part_rotation('RightShoulder', right_shoulder_rotation)
        # scale
        scale_x = self._get_signle_shoulder_modify_scale(left_shoulder_position, left_elbow_position, 'LELB', 'LBEL', 'LBSH')
        self.set_part_scaling('LeftShoulder', FBVector3d(scale_x, 1, 1))
        self.set_part_scaling('RightShoulder', FBVector3d(scale_x, 1, 1))

    # elbow

    def _get_single_elbow_modify_rotation(self, elbow_position, wrist_position, iwr_idx, owr_idx, *args, **kwargs):
        marker_position_01 = elbow_position
        marker_position_02 = self.get_marker_vector_center(iwr_idx, owr_idx)
        actor_vector = vector_minus(elbow_position, wrist_position)
        marker_vector = vector_minus(marker_position_01, marker_position_02)
        rotation = Quaternion.FromToRotation(actor_vector, marker_vector).Euler()
        return rotation

    def _set_elbow(self, *args, **kwargs):
        self.set_part_rotation('LeftElbow', FBVector3d(0, 0, 0))
        self.set_part_rotation('RightElbow', FBVector3d(0, 0, 0))
        # rotation
        left_elbow_position = self.get_part_position('LeftElbow')
        right_elbow_position = self.get_part_position('RightElbow')
        left_wrist_position = self.get_part_position('LeftWrist')
        right_wrist_position = self.get_part_position('RightWrist')
        left_elbow_rotation = self._get_single_elbow_modify_rotation(left_elbow_position, left_wrist_position, 'LIWR', 'LOWR')
        right_elbow_rotation = self._get_single_elbow_modify_rotation(right_elbow_position, right_wrist_position, 'RIWR', 'ROWR')
        self.set_part_rotation('LeftElbow', left_elbow_rotation)
        self.set_part_rotation('RightElbow', right_elbow_rotation)
        # scale
        scale_x = self._get_signle_shoulder_modify_scale(left_elbow_position, left_wrist_position, 'LELB', 'LBEL', 'LBSH')
        self.set_part_scaling('LeftElbow', FBVector3d(scale_x, 1, 1))
        self.set_part_scaling('RightElbow', FBVector3d(scale_x, 1, 1))

    # wrist
    def _get_single_wrist_modify_rotation(self, wrist_position, middle_a_position, owr_idx, iwr_idx, ohand_idx, ihand_idx, *args, **kwargs):
        marker_position_01 = self.get_marker_vector_center(owr_idx, iwr_idx)
        marker_position_02 = self.get_marker_vector_center(ohand_idx, ihand_idx)
        actor_vector = vector_minus(wrist_position, middle_a_position)
        marker_vector = vector_minus(marker_position_01, marker_position_02)
        rotation = Quaternion.FromToRotation(actor_vector, marker_vector).Euler()
        return rotation

    def _set_wrist(self, *args, **kwargs):
        self.set_part_rotation('LeftWrist', FBVector3d(0, 0, 0))
        self.set_part_rotation('RightWrist', FBVector3d(0, 0, 0))
        # rotation
        left_wrist_position = self.get_part_position('LeftWrist')
        right_wrist_position = self.get_part_position('RightWrist')
        left_middle_a_position = self.get_part_position('LeftMiddleA')
        right_middle_a_position = self.get_part_position('RightMiddleA')
        left_wrist_rotation = self._get_single_wrist_modify_rotation(left_wrist_position, left_middle_a_position, 'LIWR', 'LOWR', 'LOHAND', 'LIHAND')
        right_wrist_rotation = self._get_single_wrist_modify_rotation(right_wrist_position, right_middle_a_position, 'RIWR', 'ROWR', 'ROHAND', 'RIHAND')
        self.set_part_rotation('LeftWrist', left_wrist_rotation)
        self.set_part_rotation('RightWrist', right_wrist_rotation)
        # scale pass
