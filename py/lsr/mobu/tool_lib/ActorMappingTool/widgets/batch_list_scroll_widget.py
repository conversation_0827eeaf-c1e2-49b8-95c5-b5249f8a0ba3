import os.path
from functools import partial

from Qt import QtWidgets, QtCore
from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_base import six_encode_gb2312
from lsr.qt.core.widgets.drag_drop_widget import DragDropLineEdit


class BatchList(QtWidgets.QWidget):

    def __init__(self, parent=None, *args, **kwargs):
        super(BatchList, self).__init__(parent)
        self.items = list()

        self._init_layout()
        self._preset_widget()
        self._do_signal_connection()

    def _init_layout(self, *args, **kwargs):
        # widgets
        # scroll widget
        title_label = QtWidgets.QLabel("Actor Map", self)
        self.scroll_area = QtWidgets.QScrollArea(self)
        self.scroll_widget = QtWidgets.QWidget(self)
        self.vertical_spacer = QtWidgets.QSpacerItem(20, 40,
                                                     QtWidgets.QSizePolicy.Minimum,
                                                     QtWidgets.QSizePolicy.Expanding)
        # buttons widget
        self.add_btn = QtWidgets.QPushButton("Add Actor", self)

        # layout
        # scroll layout
        self.scroll_area.setWidget(self.scroll_widget)
        self.scroll_lay = QtWidgets.QVBoxLayout(self.scroll_widget)
        self.scroll_lay.addItem(self.vertical_spacer)
        # main layout
        main_lay = QtWidgets.QVBoxLayout(self)
        main_lay.addWidget(title_label)
        main_lay.addWidget(self.scroll_area)
        main_lay.addWidget(self.add_btn)

    def _preset_widget(self, *args, **kwargs):
        self.scroll_widget.setStyleSheet('background-color: #24282B;')
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)

        self.scroll_widget.setGeometry(QtCore.QRect(0, 0, 711, 374))
        self.scroll_lay.setSpacing(0)
        self.scroll_lay.setContentsMargins(0, 0, 0, 0)

    def _do_signal_connection(self, *args, **kwargs):
        self.add_btn.clicked.connect(self.add_item)

    def add_item(self, index=0, actor_path='', rig_path='', *args, **kwargs):
        new_item = BatchItem(self, index, actor_path, rig_path)
        new_item.self_destroyed.connect(self.remove_item)
        self.items.append(new_item)

        count = self.scroll_lay.count()
        self.scroll_lay.insertWidget(count - 1, new_item)

    def remove_item(self, widget, *args, **kwargs):
        self.items.remove(widget)
        widget.setParent(None)
        widget.deleteLater()

    def load_settings(self, settings, *args, **kwargs):
        old_items = self.items
        for item in old_items:
            self.remove_item(item)

        settings.beginGroup('actor_rig_map')
        data_list = settings.value('map_data', list())
        for index, item_data in enumerate(data_list):
            self.add_item(index, item_data['actor_path'], item_data['rig_path'])

        settings.endGroup()

    def save_settings(self, settings, *args, **kwargs):
        settings.beginGroup('actor_rig_map')
        data_list = list()
        for item in self.items:
            item_data = dict()
            item_data['actor_path'] = item.actor_path
            item_data['rig_path'] = item.rig_path
            data_list.append(item_data)
        settings.setValue('map_data', data_list)
        settings.endGroup()

    def paintEvent(self, event, *args, **kwargs):
        super(BatchList, self).paintEvent(event)

        for index, item in enumerate(self.items):
            item.index = index


class BatchItem(QtWidgets.QWidget):
    self_destroyed = QtCore.Signal(QtWidgets.QWidget)

    def __init__(self, parent=None, index=0, actor_path='', rig_path='', *args, **kwargs):
        super(BatchItem, self).__init__(parent)
        self.index = index
        self.actor_path = actor_path
        self.rig_path = rig_path
        self.setFixedHeight(60)

        self._init_layout()
        self._do_signal_connection()

        self._preset_widget()

    def _init_layout(self, *args, **kwargs):
        # widgets
        self.actor_label = QtWidgets.QLabel(self)
        self.actor_line_lab = QtWidgets.QLabel("Actor: ", self)
        self.actor_file_line_edit = DragDropLineEdit(self)
        self.actor_brown_btn = QtWidgets.QPushButton("Brown", self)
        self.mb_rig_line_lab = QtWidgets.QLabel("MB Rig: ", self)
        self.mb_rig_file_line_edit = DragDropLineEdit(self)
        self.mb_rig_brown_btn = QtWidgets.QPushButton("Brown", self)
        self.self_remove_btn = QtWidgets.QPushButton("X", self)

        separator = QtWidgets.QFrame(self)
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)

        actor_lay = QtWidgets.QHBoxLayout()
        actor_lay.addWidget(self.actor_line_lab)
        actor_lay.addWidget(self.actor_file_line_edit)
        actor_lay.addWidget(self.actor_brown_btn)

        mb_rig_lay = QtWidgets.QHBoxLayout()
        mb_rig_lay.addWidget(self.mb_rig_line_lab)
        mb_rig_lay.addWidget(self.mb_rig_file_line_edit)
        mb_rig_lay.addWidget(self.mb_rig_brown_btn)

        line_lay = QtWidgets.QVBoxLayout()
        line_lay.addLayout(actor_lay)
        line_lay.addLayout(mb_rig_lay)

        edit_lay = QtWidgets.QHBoxLayout()
        edit_lay.addLayout(line_lay)
        edit_lay.addWidget(self.self_remove_btn)

        content_layout = QtWidgets.QVBoxLayout(self)
        content_layout.addWidget(self.actor_label)
        content_layout.addLayout(edit_lay)
        content_layout.addWidget(separator)

    def _do_signal_connection(self, *args, **kwargs):
        self.actor_brown_btn.clicked.connect(self.set_actor_line)
        self.mb_rig_brown_btn.clicked.connect(self.set_rig_line)
        self.self_remove_btn.clicked.connect(partial(self.self_destroyed.emit, self))

    def _preset_widget(self, *args, **kwargs):
        self.setFixedHeight(120)
        self.actor_file_line_edit.setText(self.actor_path)
        self.mb_rig_file_line_edit.setText(self.rig_path)

    def set_actor_line(self, *args, **kwargs):
        data_path = QtWidgets.QFileDialog.getOpenFileName(self, "Select an FBX file", "", "FBX Files (*.fbx)")[0]
        if data_path:
            data_path = os.path.normpath(data_path)
            self.actor_file_line_edit.setText(data_path)

    def set_rig_line(self, *args, **kwargs):
        data_path = QtWidgets.QFileDialog.getOpenFileName(self, "Select an FBX file", "", "FBX Files (*.fbx)")[0]
        if data_path:
            data_path = os.path.normpath(data_path)
            self.mb_rig_file_line_edit.setText(data_path)

    def paintEvent(self, event, *args, **kwargs):
        super(BatchItem, self).paintEvent(event)
        self.actor_label.setText("Actor {index:02}".format(index=self.index + 1))
        self.actor_path = six_encode_gb2312(self.actor_file_line_edit.text())
        self.rig_path = six_encode_gb2312(self.mb_rig_file_line_edit.text())
