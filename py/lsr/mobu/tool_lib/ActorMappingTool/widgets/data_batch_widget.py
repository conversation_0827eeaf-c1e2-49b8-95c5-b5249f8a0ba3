
import os
from functools import partial
from Qt import QtWidgets, QtCore

from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_base import open_file, six_encode_gb2312
from lsr.mobu.tool_lib.ActorMappingTool.widgets.batch_list_scroll_widget import BatchList
from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_retarget import run_retarget
from lsr.qt.core.widgets.drag_drop_list import DragDropListWidget


class DataBatchWidget(QtWidgets.QGroupBox):

    open_file_signal = QtCore.Signal(str)
    merge_run_signal = QtCore.Signal()
    add_actor_temp_signal = QtCore.Signal()
    add_data_file_signal = QtCore.Signal()

    splitter_size = [300, 700]

    def __init__(self, parent=None, *args, **kwargs):
        super(DataBatchWidget, self).__init__(parent=parent)
        self.setTitle("Data Merging")

        self.focus_list_widget = None
        self.open_item = None

        self._init_layout()
        self._preset_widget_state()
        self._do_signal_connection()

    def _init_layout(self, *args, **kwargs):
        # actor temp widgets
        # data widgets
        self.data_file_widget = DataFileWidget(self)
        self.batch_widget = BatchList(self)
        self.split_batch_cbox = QtWidgets.QCheckBox("Batch Split", self)
        self.all_batch_cbox = QtWidgets.QCheckBox("Batch All", self)
        self.merge_btn = QtWidgets.QPushButton("Merge", self)

        # layout
        self.splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)
        self.splitter.addWidget(self.data_file_widget)
        self.splitter.addWidget(self.batch_widget)

        cbox_layout = QtWidgets.QHBoxLayout()
        cbox_layout.addWidget(self.split_batch_cbox)
        cbox_layout.addWidget(self.all_batch_cbox)

        base_lay = QtWidgets.QVBoxLayout(self)
        base_lay.addWidget(self.splitter)
        base_lay.addLayout(cbox_layout)
        base_lay.addWidget(self.merge_btn)

    def _preset_widget_state(self, *args, **kwargs):
        self.splitter.setSizes(self.splitter_size)

    def _do_signal_connection(self, *args, **kwargs):
        self.merge_btn.clicked.connect(self.slot_merge_data)

    def slot_open_file(self, widget=None, index=None, *args, **kwargs):
        if widget and index:
            item = widget.item(index.row())
            open_file(item.text())
        else:
            if self.open_item:
                open_file(self.open_item.text())

    def get_data_files(self, *args, **kwargs):
        datas = list(
            six_encode_gb2312(self.data_file_widget.data_list_widget.item(i).text())
            for i in range(self.data_file_widget.data_list_widget.count())
        )
        return datas

    def slot_merge_data(self, *args, **kwargs):
        datas = self.get_data_files()
        actor_files = list(
            item.actor_path
            for item in self.batch_widget.items)
        rig_files = list(
            item.rig_path
            for item in self.batch_widget.items)

        for data in datas:
            open_file(data)
            run_retarget(data, actor_files, rig_files,
                         method='vicon',
                         split_export=self.split_batch_cbox.isChecked(),
                         all_export=self.all_batch_cbox.isChecked())

        return QtWidgets.QMessageBox.information(self.parent(), "Success", "Merge Success!")

    def load_settings(self, settings, *args, **kwargs):
        settings.beginGroup('batch_widget')
        split_state = settings.value('split_state', 1)
        all_state = settings.value('all_state', 0)

        if split_state == '1':
            self.split_batch_cbox.setChecked(True)
        else:
            self.split_batch_cbox.setChecked(False)
        if all_state == '1':
            self.all_batch_cbox.setChecked(True)
        else:
            self.all_batch_cbox.setChecked(False)

        self.split_batch_cbox.setChecked(split_state)
        self.all_batch_cbox.setChecked(all_state)
        settings.endGroup()

        self.data_file_widget.load_settings(settings)
        self.batch_widget.load_settings(settings)

    def save_settings(self, settings, *args, **kwargs):
        settings.beginGroup('batch_widget')
        split_state = self.split_batch_cbox.isChecked()
        all_state = self.all_batch_cbox.isChecked()
        if split_state:
            split_state = 1
        else:
            split_state = 0
        if all_state:
            all_state = 1
        else:
            all_state = 0
        settings.setValue('split_state', split_state)
        settings.setValue('all_state', all_state)
        settings.endGroup()

        self.data_file_widget.save_settings(settings)
        self.batch_widget.save_settings(settings)


class DataFileWidget(QtWidgets.QWidget):

    def __init__(self, parent=None, *args, **kwargs):
        super(DataFileWidget, self).__init__(parent=parent)
        self.current_item = None
        self._init_layout()
        self._do_signal_connection()

    def _init_layout(self, *args, **kwargs):
        # widgets
        self.data_label = QtWidgets.QLabel("Data File", self)
        self.data_list_widget = DragDropListWidget(self)
        self.data_add_data_btn = QtWidgets.QPushButton("Add Data File", self)
        self.data_remove_data_btn = QtWidgets.QPushButton("Remove Selected", self)

        # context menu
        self.open_context_menu = QtWidgets.QMenu(self)
        self.open_action = QtWidgets.QAction("Open in scene", self.open_context_menu)
        self.open_context_menu.addAction(self.open_action)

        # layout
        h_btn_lay = QtWidgets.QHBoxLayout()
        h_btn_lay.addWidget(self.data_add_data_btn)
        h_btn_lay.addWidget(self.data_remove_data_btn)

        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setAlignment(QtCore.Qt.AlignTop)
        main_layout.addWidget(self.data_label)
        main_layout.addWidget(self.data_list_widget)
        main_layout.addLayout(h_btn_lay)

        # preset widget state
        self.data_list_widget.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.data_list_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)

    def _do_signal_connection(self, *args, **kwargs):
        self.open_action.triggered.connect(self.slot_open_file)
        self.data_list_widget.customContextMenuRequested.connect(partial(self.show_context_menu))
        self.data_add_data_btn.clicked.connect(self.slot_add_data_file_temp)
        self.data_remove_data_btn.clicked.connect(self.slot_remove_data_file_temp)
        self.data_list_widget.doubleClicked.connect(self.slot_open_file)

    def show_context_menu(self, position, *args, **kwargs):
        """ Show context menu when right click on folder view. """
        point = QtCore.QPoint(position.x(), position.y())
        self.current_item = self.data_list_widget.itemAt(position)
        self.open_context_menu.exec_(self.data_list_widget.mapToGlobal(point))
        self.current_item = None

    def slot_add_data_file_temp(self, *args, **kwargs):
        data_paths = QtWidgets.QFileDialog.getOpenFileNames(self, "Select an FBX file", "", "FBX Files (*.fbx)")[0]
        if data_paths:
            exists = [six_encode_gb2312(self.data_list_widget.item(index).text())
                      for index in range(self.data_list_widget.count())]
            for data_path in data_paths:
                data_path = six_encode_gb2312(data_path)
                if data_path not in exists:
                    self.data_list_widget.addItem(os.path.normpath(data_path))

    def slot_remove_data_file_temp(self, *args, **kwargs):
        selected_data = list(item for item in self.data_list_widget.selectedItems())
        for item in selected_data:
            self.data_list_widget.takeItem(self.data_list_widget.row(item))

    def slot_open_file(self, index=None, *args, **kwargs):
        if index:
            item = self.data_list_widget.item(index.row())
            open_file(six_encode_gb2312(item.text()))
        else:
            if self.current_item:
                open_file(six_encode_gb2312(self.current_item.text()))

    def load_settings(self, settings, *args, **kwargs):
        settings.beginGroup('batch_data_files')
        items = settings.value('data_files', [])
        for text in items:
            self.data_list_widget.addItem(text)
        settings.endGroup()

    def save_settings(self, settings, *args, **kwargs):
        items = list(six_encode_gb2312(self.data_list_widget.item(index).text())
                     for index in range(self.data_list_widget.count()) )
        settings.beginGroup('batch_data_files')
        settings.setValue('data_files', items)
        settings.endGroup()
