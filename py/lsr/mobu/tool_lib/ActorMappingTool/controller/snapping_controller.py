
from Qt import QtWidgets, QtCore
from functools import partial

from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_base import snap_actor, export_actor, get_no_marker_set_actor, \
    load_current_actors, import_actor, six_encode_gb2312


class SnappingWidget(QtWidgets.QGroupBox):

    def __init__(self, parent=None, *args, **kwargs):
        super(SnappingWidget, self).__init__(parent)
        self.setTitle("Actor Snapping and Exporting")

        self._init_layout()
        self._do_signal_connection()

    def _init_layout(self, *args, **kwargs):
        # widgets
        self.actor_list_widget = QtWidgets.QTreeWidget(self)
        self.snap_btn = QtWidgets.QPushButton("Snap (Active)", self)
        self.de_active_btn = QtWidgets.QPushButton("DeActive", self)
        self.delete_actor_btn = QtWidgets.QPushButton("Delete Selected", self)
        self.save_btn = QtWidgets.QPushButton("Save As...", self)
        self.import_data = QtWidgets.QPushButton("Import...", self)
        # layout

        btn_g_layout = QtWidgets.QGridLayout()
        btn_g_layout.addWidget(self.snap_btn, 0, 0)
        btn_g_layout.addWidget(self.de_active_btn, 1, 0)
        btn_g_layout.addWidget(self.import_data, 0, 1)
        btn_g_layout.addWidget(self.save_btn, 1, 1)
        btn_g_layout.addWidget(self.delete_actor_btn, 0, 2)

        snap_export_v_lay = QtWidgets.QVBoxLayout(self)
        snap_export_v_lay.setAlignment(QtCore.Qt.AlignTop)
        snap_export_v_lay.addWidget(self.actor_list_widget)
        snap_export_v_lay.addLayout(btn_g_layout)

        # preset widget
        self.actor_list_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.actor_list_widget.setColumnCount(2)
        self.actor_list_widget.setHeaderLabels(["Actor", "Activated"])
        self.actor_list_widget.setRootIsDecorated(False)
        header = self.actor_list_widget.header()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QtWidgets.QHeaderView.Stretch)

    def _do_signal_connection(self, *args, **kwargs):
        self.snap_btn.clicked.connect(partial(self.slot_snap_actor))
        self.de_active_btn.clicked.connect(partial(self.slot_de_active_actor))
        self.delete_actor_btn.clicked.connect(partial(self.slot_delete_actor))
        self.save_btn.clicked.connect(partial(self.slot_export_actor))
        self.import_data.clicked.connect(partial(self.slot_import_actor))
        self.actor_list_widget.itemSelectionChanged.connect(partial(self.slot_selection_changed))

    def slot_snap_actor(self, *args, **kwargs):
        selected_actor = list(self.name_actor_dict[six_encode_gb2312(item.text(0))] for item in self.actor_list_widget.selectedItems())
        if selected_actor:
            if self.check_marker_set(selected_actor):
                for actor in selected_actor:
                    snap_actor(actor)
                self.slot_refresh_actor()
        else:
            return QtWidgets.QMessageBox.warning(self.parent(), "No Actor Selected!", "Please select actors!")

    def slot_de_active_actor(self, *args, **kwargs):
        selected_actor = list(self.name_actor_dict[six_encode_gb2312(item.text(0))] for item in self.actor_list_widget.selectedItems())
        if selected_actor:
            for actor in selected_actor:
                actor.Active = False
            self.slot_refresh_actor()
        else:
            return QtWidgets.QMessageBox.warning(self.parent(), "No Actor Selected!", "Please select actors!")

    def slot_delete_actor(self, *args, **kwargs):
        selected_actor = list(self.name_actor_dict[six_encode_gb2312(item.text(0))] for item in self.actor_list_widget.selectedItems())
        if selected_actor:
            for actor in selected_actor:
                marker_sets = actor.PropertyList.Find("Marker Set")
                if marker_sets:
                    for marker_set in marker_sets:
                        marker_set.FBDelete()
                actor.FBDelete()
            self.slot_refresh_actor()
            return QtWidgets.QMessageBox.information(self.parent(), "Success", "Delete Success!")
        else:
            return QtWidgets.QMessageBox.warning(self.parent(), "No Actor Selected!", "Please select actors!")

    def slot_export_actor(self, *args, **kwargs):
        selected_actor = list(self.name_actor_dict[six_encode_gb2312(item.text(0))] for item in self.actor_list_widget.selectedItems())
        if selected_actor:
            if self.check_marker_set(selected_actor):
                export_path = QtWidgets.QFileDialog.getExistingDirectory(self.parent(), "Select Export Path",
                                                                         "../widgets/")
                if export_path:
                    for actor in selected_actor:
                        export_actor(actor, export_path)
                    return QtWidgets.QMessageBox.information(self.parent(), "Success", "Export Success!")
        else:
            return QtWidgets.QMessageBox.warning(self.parent(), "No Actor Selected!", "Please select actors!")

    def slot_selection_changed(self, *args, **kwargs):
        for actor in self.name_actor_dict.values():
            actor.Selected = False
        selected_actor = list(self.name_actor_dict[six_encode_gb2312(item.text(0))] for item in self.actor_list_widget.selectedItems())
        if selected_actor:
            for actor in selected_actor:
                actor.Selected = True

    def check_marker_set(self, actors, *args, **kwargs):
        """ Check if actor has marker set. """
        no_marker_set_actor = get_no_marker_set_actor(actors)
        if no_marker_set_actor:
            QtWidgets.QMessageBox.warning(self.parent(), "Marker Set Not Found!",
                                          "Actor: '" + no_marker_set_actor + "' Marker Set Not Found!")
            return False
        return True

    def slot_refresh_actor(self, *args, **kwargs):
        selected_actor_name = list(six_encode_gb2312(item.text(0)) for item in self.actor_list_widget.selectedItems())

        self.name_actor_dict = load_current_actors()
        self.actor_list_widget.clear()

        for actor_name, actor in self.name_actor_dict.items():
            item = QtWidgets.QTreeWidgetItem([actor_name, 'Active' if actor.Active else 'DeActive'])
            self.actor_list_widget.addTopLevelItem(item)

        if selected_actor_name:
            for actor_name in selected_actor_name:
                if actor_name in self.name_actor_dict:
                    self.actor_list_widget.setItemSelected(self.actor_list_widget.findItems(actor_name, QtCore.Qt.MatchExactly)[0], True)

    def slot_import_actor(self, *args, **kwargs):
        import_paths = QtWidgets.QFileDialog.getOpenFileNames(self.parent(), "Select Export Path", filter="FBX Files (*.fbx)")[0]
        if import_paths:
            for import_path in import_paths:
                import_actor(import_path)
