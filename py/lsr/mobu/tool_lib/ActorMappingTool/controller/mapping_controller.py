
from Qt import QtWidgets, QtCore
from functools import partial

from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_mapping import MarkerManager
from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_base import six_encode_gb2312


class MappingWidget(QtWidgets.QGroupBox):
    def __init__(self, parent=None, *args, **kwargs):
        super(MappingWidget, self).__init__(parent)
        self.setTitle("Actor Mapping")
        self.setStyleSheet('QGroupBox {background-color: #24282B;}'
                           'QRadioButton {background-color: #24282B;}')

        self._init_layout()
        self._do_signal_connection()

    def _init_layout(self, *args, **kwargs):
        # widgets
        self.mapping_method_btn_box = QtWidgets.QButtonGroup(self)
        self.vicon_btn = QtWidgets.QRadioButton("vicon", self)
        self.optitrack_btn = QtWidgets.QRadioButton("optitrack")
        self.chinqmu_btn = QtWidgets.QRadioButton("chinqmu")
        self.motion_analysis_btn = QtWidgets.QRadioButton("motion analysis")
        self.mapping_btn = QtWidgets.QPushButton("Auto Mapping", self)
        # layout
        btn_lay = QtWidgets.QHBoxLayout()
        btn_lay.addWidget(self.vicon_btn)
        btn_lay.addWidget(self.optitrack_btn)
        btn_lay.addWidget(self.chinqmu_btn)
        btn_lay.addWidget(self.motion_analysis_btn)

        self.mapping_method_btn_box.addButton(self.vicon_btn)
        self.mapping_method_btn_box.addButton(self.optitrack_btn)
        self.mapping_method_btn_box.addButton(self.chinqmu_btn)
        self.mapping_method_btn_box.addButton(self.motion_analysis_btn)

        actor_v_lay = QtWidgets.QVBoxLayout(self)
        actor_v_lay.setAlignment(QtCore.Qt.AlignTop)
        actor_v_lay.addLayout(btn_lay)
        actor_v_lay.addWidget(self.mapping_btn)

        # preset widget
        self.vicon_btn.setChecked(True)

    def _do_signal_connection(self, *args, **kwargs):
        self.mapping_btn.clicked.connect(partial(self.slot_map_actors))

    def slot_map_actors(self, *args, **kwargs):
        checked_btn = self.mapping_method_btn_box.checkedButton()
        if checked_btn:
            manager = None
            method = six_encode_gb2312(checked_btn.text())
            if method == 'vicon':
                manager = MarkerManager(marker_type=method)

            if manager:
                all_actors = list(manager.character_markers.keys())
                create_actor_names = self._call_map_needed_actor_dialog(all_actors)
                if create_actor_names:
                    manager.create_actor(create_actor_names)
                    manager.set_actor()
                    return QtWidgets.QMessageBox.information(self.parent(), "Success", "Mapping Success!")

    def _call_map_needed_actor_dialog(self, actor_name_list=[], *args, **kwargs):
        if actor_name_list:
            name_dialog = QtWidgets.QDialog(self)

            name_list_widget = QtWidgets.QListWidget(name_dialog)
            ok_button = QtWidgets.QPushButton("OK")
            cancel_button = QtWidgets.QPushButton("Cancel")

            ok_button.clicked.connect(name_dialog.accept)
            cancel_button.clicked.connect(name_dialog.reject)

            button_layout = QtWidgets.QHBoxLayout()
            button_layout.addWidget(ok_button)
            button_layout.addWidget(cancel_button)

            dialog_lay = QtWidgets.QVBoxLayout(name_dialog)
            dialog_lay.addWidget(name_list_widget)
            dialog_lay.addLayout(button_layout)

            name_list_widget.setSelectionMode(QtWidgets.QListWidget.ExtendedSelection)

            name_list_widget.addItems(actor_name_list)

            if name_dialog.exec_():
                return list(six_encode_gb2312(item.text()) for item in name_list_widget.selectedItems())

    def paintEvent(self, event, *args, **kwargs):
        """ Rewrite paint event for ui"""
        super(MappingWidget, self).paintEvent(event)
        # TODO show more method until ready
        self.optitrack_btn.setEnabled(False)
        self.chinqmu_btn.setEnabled(False)
        self.motion_analysis_btn.setEnabled(False)
