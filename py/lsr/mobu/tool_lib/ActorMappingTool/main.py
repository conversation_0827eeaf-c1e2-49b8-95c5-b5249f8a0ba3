from Qt import QtWidgets

from lsr.qt.core.base_main_window import get_window_class
from lsr.mobu.tool_lib.ActorMappingTool.controller.base_controller import ActorMappingBase
from lsr.mobu.tool_lib.ActorMappingTool.utils.actor_base import get_mb_version


base_class = get_window_class(app_name='LSR_ActorMappingTool')
mb_version = get_mb_version()


class ActorMappingTool(base_class):
    def __init__(self, parent=None, *args, **kwargs):
        super(ActorMappingTool, self).__init__(parent=parent, banner_widget=True, has_art=True, top=True,
                                               email_address='<EMAIL>')

    def setup_ui(self, *args, **kwargs):
        if mb_version < 22:
            self.central_widget = QtWidgets.QLabel('Can only use in MotionBuilder version 2022 or higher.', self)
        else:
            self.central_widget = ActorMappingBase(self)
        self.setCentralWidget(self.central_widget)

    def hideEvent(self, event, *args, **kwargs):
        """ Save settings before hiding. """
        self.closeEvent(event)

    def load_settings(self, *args, **kwargs):
        settings = super(ActorMappingTool, self).load_settings()

        if isinstance(self.central_widget, ActorMappingBase):
            self.central_widget.load_settings(settings)

    def save_settings(self, *args, **kwargs):
        settings = super(ActorMappingTool, self).save_settings()

        if isinstance(self.central_widget, ActorMappingBase):
            self.central_widget.save_settings(settings)

        settings.sync()
