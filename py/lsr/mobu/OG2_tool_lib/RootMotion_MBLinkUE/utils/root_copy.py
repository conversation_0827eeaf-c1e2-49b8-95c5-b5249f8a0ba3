import os
import pyfbsdk as fb
from lsr.mobu.nodezoo.node import Character
from lsr.mobu.utils import anim_util
import lsr.mobu.rig.file_options as file_options

src_folder = 'E:/local_test_folder/20250703/crouch'
dst_folder = 'E:/local_test_folder/20250703/crouch_ok'
out_folder = 'E:/local_test_folder/20250703/crouch_root_fix'

src_fbx_list = []
dst_fbx_list = []
for root, dirs, files in os.walk(src_folder):
    for filename in files:
        if filename.lower().endswith('.fbx'):
            src_fbx_list.append(os.path.join(root, filename))
            dst_fbx_list.append(os.path.join(dst_folder, filename))

for src_fbx, dst_fbx in zip(src_fbx_list, dst_fbx_list):
    if not os.path.exists(dst_fbx):
        continue
    fb.FBApplication().FileOpen(src_fbx, False)
    take_pos_rot = dict()

    fb.FBApplication().FileOpen(src_fbx, False)
    tar_cha_node = Character.current_character()
    for take in fb.FBSystem().Scene.Takes:
        fb.FBSystem().CurrentTake = take
        fb.FBSystem().Scene.Evaluate()
        pos_key_info, rot_key_info = anim_util.get_transform_key_by_node(tar_cha_node.root_bone)
        base_name = os.path.splitext(os.path.basename(src_fbx))[0]
        take_pos_rot[base_name] = [pos_key_info, rot_key_info]

    fb.FBApplication().FileOpen(dst_fbx, False)
    cha_node = Character.current_character()
    # plot char anim
    cha_node.plot_controller_animation()
    for take in fb.FBSystem().Scene.Takes:
        fb.FBSystem().CurrentTake = take
        fb.FBSystem().Scene.Evaluate()
        if take.LongName in take_pos_rot:
            pos_key_info, rot_key_info = take_pos_rot[take.LongName][0], take_pos_rot[take.LongName][1]
            anim_util.apply_keyframes(cha_node.root_bone, pos_key_info, rot_key_info)

    save_options = file_options.SaveAllOption(False)
    if not os.path.exists(out_folder):
        os.makedirs(out_folder)
    out_fbx = os.path.join(out_folder, os.path.basename(dst_fbx))
    fb.FBApplication().FileSave(out_fbx, save_options)