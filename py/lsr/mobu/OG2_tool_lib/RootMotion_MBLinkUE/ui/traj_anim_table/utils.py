# -*- coding: utf-8 -*-
import json
import requests
import pprint

import pyfbsdk as fb
from lsr.mobu.OG2_tool_lib.RootMotion_MBLinkUE.ui.traj_anim_table.data.config import TrajAnimTableConfig


def _make_request_data(data):
    in_time_step = 1.0 / fb.FBPlayerControl().GetTransportFpsValue()
    TrajAnimTableConfig.IN_TIME_STEP = in_time_step
    request = {
    # 函数参数列表
    'Parameters':
        {
            # 这个一定要有，就是你想要的是什么输入对应的轨迹，比如向前移动+向左移动就是左转，静止不动+向前移动就是静止起步这样，
            # 其实也可以加3段以上的输入，就能出来蛇形什么的
            'InInputLog':data,
            # 返回轨迹的时间间隔，可以不配置，默认0.1
            'InTimeStep': TrajAnimTableConfig.IN_TIME_STEP,
        },
    # UE调用配置，可以保留这样不动
    "GenerateTransaction": False
    }
    print('============================build_request_data============================')
    pprint.pprint(request)
    return request

def _dump_json(request):
    request_json = json.dumps(request)
    return request_json

def _get_traj_from_remote(request_json):
    url = TrajAnimTableConfig.url
    print('========================')
    print('==开始尝试连接UE服务器...\n')
    print(f'==请求地址: {url}\n')
    print(f'==请求数据: {request_json}\n')
    try:
        r = requests.put(url, data=request_json)
    except requests.exceptions.ConnectionError:
        print('==连接UE服务器失败')
        fb.FBMessageBox("连接错误", '无法连接到UE服务器，请检查UE仓库是否为最新版本，并尝试重启UE。', "OK")
        return None

    if r.status_code == requests.codes.ok:
        print('==连接UE服务器成功')
        print('==开始解析UE服务器返回的数据\n')
        pprint.pprint(json.loads(r.text))
        print(f'==返回数据: {r.text}\n')
        print('==解析完成')
        return r.text
    else:
        print(r.status_code)
        print(r.text)
        return None

def _translate_ue_traj(ue_traj_data):
    ue_traj_data = json.loads(ue_traj_data)
    ue_traj_matrix = ue_traj_data["ReturnedValues"][0]["OutTrajectory"]
    mobu_traj_data = []
    for ue_traj in ue_traj_matrix:
        matrix = fb.FBMatrix()
        for i, plane_name in enumerate(["XPlane", "YPlane", "ZPlane", "WPlane"]):
            for j, axis_name in enumerate(["X", "Y", "Z", "W"]):
                matrix[i * 4 + j] = ue_traj[plane_name][axis_name]
        mobu_traj_data.append(matrix)

    return mobu_traj_data

def get_traj(data):
    request = _make_request_data(data)
    request_json = _dump_json(request)
    ue_traj_data = _get_traj_from_remote(request_json)
    mobu_traj_data = _translate_ue_traj(ue_traj_data)
    return mobu_traj_data

