# - *- coding: utf-8 -*-

from Qt import QtWidgets, QtCore

class MovementSettingsView(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super(MovementSettingsView, self).__init__(parent)
        self._init_ui()

    def _init_ui(self, *args, **kwargs):
        layout = QtWidgets.QVBoxLayout(self)
        self.setLayout(layout)

        # 速度
        self.speeds_label = QtWidgets.QLabel('Speeds(速度):', self)
        self.speeds_front_sp = QtWidgets.QDoubleSpinBox(self)
        self.speeds_side_sp = QtWidgets.QDoubleSpinBox(self)
        self.speeds_back_sp = QtWidgets.QDoubleSpinBox(self)

        self.speeds_front_sp.setPrefix('X(前进): ')
        self.speeds_side_sp.setPrefix('Y(侧移): ')
        self.speeds_back_sp.setPrefix('Z(后退): ')
        self.speeds_front_sp.setRange(0, 99999)
        self.speeds_side_sp.setRange(0, 99999)
        self.speeds_back_sp.setRange(0, 99999)
        self.speeds_front_sp.setSingleStep(1)
        self.speeds_side_sp.setSingleStep(1)
        self.speeds_back_sp.setSingleStep(1)

        self.speeds_layout = QtWidgets.QHBoxLayout()
        self.speeds_layout.addWidget(self.speeds_front_sp)
        self.speeds_layout.addWidget(self.speeds_side_sp)
        self.speeds_layout.addWidget(self.speeds_back_sp)
        layout.addWidget(self.speeds_label)
        layout.addLayout(self.speeds_layout)

        # 蹲伏速度
        self.crouch_speeds_label = QtWidgets.QLabel('CrouchSpeeds(蹲伏速度):', self)
        self.crouch_speeds_front_sp = QtWidgets.QDoubleSpinBox(self)
        self.crouch_speeds_side_sp = QtWidgets.QDoubleSpinBox(self)
        self.crouch_speeds_back_sp = QtWidgets.QDoubleSpinBox(self)

        self.crouch_speeds_front_sp.setPrefix('X(前进): ')
        self.crouch_speeds_side_sp.setPrefix('Y(侧移): ')
        self.crouch_speeds_back_sp.setPrefix('Z(后退): ')
        self.crouch_speeds_front_sp.setRange(0, 99999)
        self.crouch_speeds_side_sp.setRange(0, 99999)
        self.crouch_speeds_back_sp.setRange(0, 99999)
        self.crouch_speeds_front_sp.setSingleStep(1)
        self.crouch_speeds_side_sp.setSingleStep(1)
        self.crouch_speeds_back_sp.setSingleStep(1)

        self.crouch_speeds_layout = QtWidgets.QHBoxLayout()
        self.crouch_speeds_layout.addWidget(self.crouch_speeds_front_sp)
        self.crouch_speeds_layout.addWidget(self.crouch_speeds_side_sp)
        self.crouch_speeds_layout.addWidget(self.crouch_speeds_back_sp)
        layout.addWidget(self.crouch_speeds_label)
        layout.addLayout(self.crouch_speeds_layout)

        layout.addSpacing(15)
        sep_line = QtWidgets.QFrame(self)
        sep_line.setFrameShape(QtWidgets.QFrame.HLine)
        sep_line.setFrameShadow(QtWidgets.QFrame.Sunken)
        layout.addWidget(sep_line)

        # 加速度设置
        self.acceleration_type_label = QtWidgets.QLabel('AccelerationType(加速度类型):', self)
        self.acceleration_type_combo = QtWidgets.QComboBox(self)
        self.acceleration_type_combo.addItems(['N/A', 'Fixed', 'Dynamic'])
        layout.addWidget(self.acceleration_type_label)
        layout.addWidget(self.acceleration_type_combo)

        self.max_acceleration_label = QtWidgets.QLabel('MaxAcceleration(最大加速度):', self)
        self.max_acceleration_sp = QtWidgets.QDoubleSpinBox(self)
        self.max_acceleration_sp.setRange(0, 99999)
        self.max_acceleration_sp.setSingleStep(1)
        layout.addWidget(self.max_acceleration_label)
        layout.addWidget(self.max_acceleration_sp)

        self.max_acceleration_speed_range_label = QtWidgets.QLabel('MaxAccelerationSpeedRange(加速度速度范围):', self)
        self.max_acceleration_speed_range_front_sp = QtWidgets.QDoubleSpinBox(self)
        self.max_acceleration_speed_range_side_sp = QtWidgets.QDoubleSpinBox(self)
        self.max_acceleration_speed_range_front_sp.setPrefix('X: ')
        self.max_acceleration_speed_range_side_sp.setPrefix('Y: ')
        self.max_acceleration_speed_range_front_sp.setRange(0, 99999)
        self.max_acceleration_speed_range_side_sp.setRange(0, 99999)
        self.max_acceleration_speed_range_front_sp.setSingleStep(1)
        self.max_acceleration_speed_range_side_sp.setSingleStep(1)
        self.max_acceleration_speed_range_layout = QtWidgets.QHBoxLayout()
        self.max_acceleration_speed_range_layout.addWidget(self.max_acceleration_speed_range_front_sp)
        self.max_acceleration_speed_range_layout.addWidget(self.max_acceleration_speed_range_side_sp)
        layout.addWidget(self.max_acceleration_speed_range_label)
        layout.addLayout(self.max_acceleration_speed_range_layout)

        self.mapped_max_acceleration_range_label = QtWidgets.QLabel('MappedMaxAccelerationRange(映射加速度范围):', self)
        self.mapped_max_acceleration_range_front_sp = QtWidgets.QDoubleSpinBox(self)
        self.mapped_max_acceleration_range_side_sp = QtWidgets.QDoubleSpinBox(self)
        self.mapped_max_acceleration_range_front_sp.setPrefix('X: ')
        self.mapped_max_acceleration_range_side_sp.setPrefix('Y: ')
        self.mapped_max_acceleration_range_front_sp.setRange(0, 99999)
        self.mapped_max_acceleration_range_side_sp.setRange(0, 99999)
        self.mapped_max_acceleration_range_front_sp.setSingleStep(1)
        self.mapped_max_acceleration_range_side_sp.setSingleStep(1)
        self.mapped_max_acceleration_range_layout = QtWidgets.QHBoxLayout()
        self.mapped_max_acceleration_range_layout.addWidget(self.mapped_max_acceleration_range_front_sp)
        self.mapped_max_acceleration_range_layout.addWidget(self.mapped_max_acceleration_range_side_sp)
        layout.addWidget(self.mapped_max_acceleration_range_label)
        layout.addLayout(self.mapped_max_acceleration_range_layout)

        layout.addSpacing(15)
        sep_line = QtWidgets.QFrame(self)
        sep_line.setFrameShape(QtWidgets.QFrame.HLine)
        sep_line.setFrameShadow(QtWidgets.QFrame.Sunken)
        layout.addWidget(sep_line)

        # 制动设置
        self.use_separate_braking_label = QtWidgets.QLabel('bUseSeparateBrakingFriction(使用独立制动摩擦力):', self)
        self.use_separate_braking_cb = QtWidgets.QCheckBox(self)
        layout.addWidget(self.use_separate_braking_label)
        layout.addWidget(self.use_separate_braking_cb)

        self.braking_friction_factor_label = QtWidgets.QLabel('BrakingFrictionFactor(制动摩擦系数):', self)
        self.braking_friction_factor_sp = QtWidgets.QDoubleSpinBox(self)
        self.braking_friction_factor_sp.setRange(0, 99999)
        self.braking_friction_factor_sp.setSingleStep(1)
        layout.addWidget(self.braking_friction_factor_label)
        layout.addWidget(self.braking_friction_factor_sp)

        self.braking_friction_label = QtWidgets.QLabel('BrakingFriction(制动摩擦力):', self)
        self.braking_friction_sp = QtWidgets.QDoubleSpinBox(self)
        self.braking_friction_sp.setRange(0, 99999)
        self.braking_friction_sp.setSingleStep(1)
        layout.addWidget(self.braking_friction_label)
        layout.addWidget(self.braking_friction_sp)

        layout.addSpacing(15)
        sep_line = QtWidgets.QFrame(self)
        sep_line.setFrameShape(QtWidgets.QFrame.HLine)
        sep_line.setFrameShadow(QtWidgets.QFrame.Sunken)
        layout.addWidget(sep_line)

        self.braking_deceleration_with_input_label = QtWidgets.QLabel('BrakingDecelerationWithInput(制动减速度):', self)
        self.braking_deceleration_with_input_sp = QtWidgets.QDoubleSpinBox(self)
        self.braking_deceleration_with_input_sp.setRange(0, 99999)
        self.braking_deceleration_with_input_sp.setSingleStep(1)
        layout.addWidget(self.braking_deceleration_with_input_label)
        layout.addWidget(self.braking_deceleration_with_input_sp)
        # 不显示，不提供修改
        self.braking_deceleration_with_input_label.setVisible(False)
        self.braking_deceleration_with_input_sp.setVisible(False)

        self.braking_deceleration_label = QtWidgets.QLabel('BrakingDeceleration(制动减速度):', self)
        self.braking_deceleration_sp = QtWidgets.QDoubleSpinBox(self)
        self.braking_deceleration_sp.setRange(0, 99999)
        self.braking_deceleration_sp.setSingleStep(1)
        layout.addWidget(self.braking_deceleration_label)
        layout.addWidget(self.braking_deceleration_sp)

        layout.addSpacing(15)
        sep_line = QtWidgets.QFrame(self)
        sep_line.setFrameShape(QtWidgets.QFrame.HLine)
        sep_line.setFrameShadow(QtWidgets.QFrame.Sunken)
        layout.addWidget(sep_line)

        # 地面摩擦力设置
        self.ground_friction_type_label = QtWidgets.QLabel('GroundFrictionType(地面摩擦力类型):', self)
        self.ground_friction_type_combo = QtWidgets.QComboBox(self)
        self.ground_friction_type_combo.addItems(['N/A', 'Fixed', 'Dynamic'])
        layout.addWidget(self.ground_friction_type_label)
        layout.addWidget(self.ground_friction_type_combo)

        self.ground_friction_label = QtWidgets.QLabel('GroundFriction(地面摩擦力):', self)
        self.ground_friction_sp = QtWidgets.QDoubleSpinBox(self)
        self.ground_friction_sp.setRange(0, 99999)
        self.ground_friction_sp.setSingleStep(1)
        layout.addWidget(self.ground_friction_label)
        layout.addWidget(self.ground_friction_sp)

        self.ground_friction_speed_range_label = QtWidgets.QLabel('GroundFrictionSpeedRange(地面摩擦力速度范围):', self)
        self.ground_friction_speed_range_front_sp = QtWidgets.QDoubleSpinBox(self)
        self.ground_friction_speed_range_side_sp = QtWidgets.QDoubleSpinBox(self)
        self.ground_friction_speed_range_front_sp.setPrefix('X: ')
        self.ground_friction_speed_range_side_sp.setPrefix('Y: ')
        self.ground_friction_speed_range_front_sp.setRange(0, 99999)
        self.ground_friction_speed_range_side_sp.setRange(0, 99999)
        self.ground_friction_speed_range_front_sp.setSingleStep(1)
        self.ground_friction_speed_range_side_sp.setSingleStep(1)
        self.ground_friction_speed_range_layout = QtWidgets.QHBoxLayout()
        self.ground_friction_speed_range_layout.addWidget(self.ground_friction_speed_range_front_sp)
        self.ground_friction_speed_range_layout.addWidget(self.ground_friction_speed_range_side_sp)
        layout.addWidget(self.ground_friction_speed_range_label)
        layout.addLayout(self.ground_friction_speed_range_layout)

        self.mapped_ground_friction_range_label = QtWidgets.QLabel('MappedGroundFrictionRange(映射地面摩擦力范围):', self)
        self.mapped_ground_friction_range_front_sp = QtWidgets.QDoubleSpinBox(self)
        self.mapped_ground_friction_range_side_sp = QtWidgets.QDoubleSpinBox(self)
        self.mapped_ground_friction_range_front_sp.setPrefix('X: ')
        self.mapped_ground_friction_range_side_sp.setPrefix('Y: ')
        self.mapped_ground_friction_range_front_sp.setRange(0, 99999)
        self.mapped_ground_friction_range_side_sp.setRange(0, 99999)
        self.mapped_ground_friction_range_front_sp.setSingleStep(1)
        self.mapped_ground_friction_range_side_sp.setSingleStep(1)
        self.mapped_ground_friction_range_layout = QtWidgets.QHBoxLayout()
        self.mapped_ground_friction_range_layout.addWidget(self.mapped_ground_friction_range_front_sp)
        self.mapped_ground_friction_range_layout.addWidget(self.mapped_ground_friction_range_side_sp)
        layout.addWidget(self.mapped_ground_friction_range_label)
        layout.addLayout(self.mapped_ground_friction_range_layout)

        layout.addSpacing(15)
        sep_line = QtWidgets.QFrame(self)
        sep_line.setFrameShape(QtWidgets.QFrame.HLine)
        sep_line.setFrameShadow(QtWidgets.QFrame.Sunken)
        layout.addWidget(sep_line)

        # 旋转速率
        self.rotation_rate_label = QtWidgets.QLabel('RotationRate(旋转速率):', self)
        self.rotation_rate_pitch_sp = QtWidgets.QDoubleSpinBox(self)
        self.rotation_rate_yaw_sp = QtWidgets.QDoubleSpinBox(self)
        self.rotation_rate_roll_sp = QtWidgets.QDoubleSpinBox(self)
        self.rotation_rate_pitch_sp.setPrefix('Pitch: ')
        self.rotation_rate_yaw_sp.setPrefix('Yaw: ')
        self.rotation_rate_roll_sp.setPrefix('Roll: ')
        self.rotation_rate_pitch_sp.setRange(-99999, 99999)
        self.rotation_rate_yaw_sp.setRange(-99999, 99999)
        self.rotation_rate_roll_sp.setRange(-99999, 99999)
        self.rotation_rate_pitch_sp.setSingleStep(1)
        self.rotation_rate_yaw_sp.setSingleStep(1)
        self.rotation_rate_roll_sp.setSingleStep(1)
        self.rotation_rate_layout = QtWidgets.QHBoxLayout()
        self.rotation_rate_layout.addWidget(self.rotation_rate_pitch_sp)
        self.rotation_rate_layout.addWidget(self.rotation_rate_yaw_sp)
        self.rotation_rate_layout.addWidget(self.rotation_rate_roll_sp)
        layout.addWidget(self.rotation_rate_label)
        layout.addLayout(self.rotation_rate_layout)

    def update_movement_settings(self, movement_settings={}, *args, **kwargs):
        # 速度参数
        self.speeds_front_sp.setValue(movement_settings.get('Speeds', {}).get('X', 0))
        self.speeds_side_sp.setValue(movement_settings.get('Speeds', {}).get('Y', 0))
        self.speeds_back_sp.setValue(movement_settings.get('Speeds', {}).get('Z', 0))

        # 蹲伏速度参数
        self.crouch_speeds_front_sp.setValue(movement_settings.get('CrouchSpeeds', {}).get('X', 0))
        self.crouch_speeds_side_sp.setValue(movement_settings.get('CrouchSpeeds', {}).get('Y', 0))
        self.crouch_speeds_back_sp.setValue(movement_settings.get('CrouchSpeeds', {}).get('Z', 0))

        # 加速度设置
        self.acceleration_type_combo.setCurrentText(movement_settings.get('AccelerationType', 'N/A'))
        self.max_acceleration_sp.setValue(movement_settings.get('MaxAcceleration', 0))
        self.max_acceleration_speed_range_front_sp.setValue(movement_settings.get('MaxAccelerationSpeedRange', {}).get('X', 0))
        self.max_acceleration_speed_range_side_sp.setValue(movement_settings.get('MaxAccelerationSpeedRange', {}).get('Y', 0))
        self.mapped_max_acceleration_range_front_sp.setValue(movement_settings.get('MappedMaxAccelerationRange', {}).get('X', 0))
        self.mapped_max_acceleration_range_side_sp.setValue(movement_settings.get('MappedMaxAccelerationRange', {}).get('Y', 0))

        # 制动设置
        self.braking_friction_factor_sp.setValue(movement_settings.get('BrakingFrictionFactor', 0))
        self.braking_friction_sp.setValue(movement_settings.get('BrakingFriction', 0))
        self.use_separate_braking_cb.setChecked(movement_settings.get('bUseSeparateBrakingFriction', False))
        self.braking_deceleration_with_input_sp.setValue(movement_settings.get('BrakingDecelerationWithInput', 0))
        self.braking_deceleration_sp.setValue(movement_settings.get('BrakingDeceleration', 0))

        # 地面摩擦力设置
        self.ground_friction_type_combo.setCurrentText(movement_settings.get('GroundFrictionType', 'N/A'))
        self.ground_friction_sp.setValue(movement_settings.get('GroundFriction', 0))
        self.ground_friction_speed_range_front_sp.setValue(movement_settings.get('GroundFrictionSpeedRange', {}).get('X', 0))
        self.ground_friction_speed_range_side_sp.setValue(movement_settings.get('GroundFrictionSpeedRange', {}).get('Y', 0))
        self.mapped_ground_friction_range_front_sp.setValue(movement_settings.get('MappedGroundFrictionRange', {}).get('X', 0))
        self.mapped_ground_friction_range_side_sp.setValue(movement_settings.get('MappedGroundFrictionRange', {}).get('Y', 0))

        # 旋转速率
        self.rotation_rate_pitch_sp.setValue(movement_settings.get('RotationRate', {}).get('Pitch', 0))
        self.rotation_rate_yaw_sp.setValue(movement_settings.get('RotationRate', {}).get('Yaw', 0))
        self.rotation_rate_roll_sp.setValue(movement_settings.get('RotationRate', {}).get('Roll', 0))

    def get_movement_settings(self, *args, **kwargs):
        movement_settings = {}
        movement_settings['Speeds'] = {
            'X': self.speeds_front_sp.value(),
            'Y': self.speeds_side_sp.value(),
            'Z': self.speeds_back_sp.value()
        }
        movement_settings['CrouchSpeeds'] = {
            'X': self.crouch_speeds_front_sp.value(),
            'Y': self.crouch_speeds_side_sp.value(),
            'Z': self.crouch_speeds_back_sp.value()
        }
        movement_settings['AccelerationType'] = self.acceleration_type_combo.currentText()
        movement_settings['MaxAcceleration'] = self.max_acceleration_sp.value()
        movement_settings['MaxAccelerationSpeedRange'] = {
            'X': self.max_acceleration_speed_range_front_sp.value(),
            'Y': self.max_acceleration_speed_range_side_sp.value()
        }
        movement_settings['MappedMaxAccelerationRange'] = {
            'X': self.mapped_max_acceleration_range_front_sp.value(),
            'Y': self.mapped_max_acceleration_range_side_sp.value()
        }
        movement_settings['BrakingFrictionFactor'] = self.braking_friction_factor_sp.value()
        movement_settings['BrakingFriction'] = self.braking_friction_sp.value()
        movement_settings['bUseSeparateBrakingFriction'] = self.use_separate_braking_cb.isChecked()
        movement_settings['BrakingDecelerationWithInput'] = self.braking_deceleration_with_input_sp.value()
        movement_settings['BrakingDeceleration'] = self.braking_deceleration_sp.value()
        movement_settings['GroundFrictionType'] = self.ground_friction_type_combo.currentText()
        movement_settings['GroundFriction'] = self.ground_friction_sp.value()
        movement_settings['GroundFrictionSpeedRange'] = {
            'X': self.ground_friction_speed_range_front_sp.value(),
            'Y': self.ground_friction_speed_range_side_sp.value()
        }
        movement_settings['MappedGroundFrictionRange'] = {
            'X': self.mapped_ground_friction_range_front_sp.value(),
            'Y': self.mapped_ground_friction_range_side_sp.value()
        }
        movement_settings['RotationRate'] = {
            'Pitch': self.rotation_rate_pitch_sp.value(),
            'Yaw': self.rotation_rate_yaw_sp.value(),
            'Roll': self.rotation_rate_roll_sp.value()
        }
        return movement_settings

    def paintEvent(self, event, *args, **kwargs):
        super(MovementSettingsView, self).paintEvent(event)
        if self.acceleration_type_combo.currentText() == 'Fixed':
            self.max_acceleration_sp.setEnabled(True)
            self.max_acceleration_speed_range_side_sp.setEnabled(False)
            self.max_acceleration_speed_range_front_sp.setEnabled(False)
            self.mapped_max_acceleration_range_side_sp.setEnabled(False)
            self.mapped_max_acceleration_range_front_sp.setEnabled(False)
        elif self.acceleration_type_combo.currentText() == 'Dynamic':
            self.max_acceleration_sp.setEnabled(False)
            self.max_acceleration_speed_range_side_sp.setEnabled(True)
            self.max_acceleration_speed_range_front_sp.setEnabled(True)
            self.mapped_max_acceleration_range_side_sp.setEnabled(True)
            self.mapped_max_acceleration_range_front_sp.setEnabled(True)
        else:
            self.max_acceleration_sp.setEnabled(False)
            self.max_acceleration_speed_range_side_sp.setEnabled(False)
            self.max_acceleration_speed_range_front_sp.setEnabled(False)
            self.mapped_max_acceleration_range_side_sp.setEnabled(False)
            self.mapped_max_acceleration_range_front_sp.setEnabled(False)

        if self.use_separate_braking_cb.isChecked():
            self.braking_friction_sp.setEnabled(True)
            self.braking_friction_factor_sp.setEnabled(False)
        else:
            self.braking_friction_sp.setEnabled(False)
            self.braking_friction_factor_sp.setEnabled(True)

        if self.ground_friction_type_combo.currentText() == 'Fixed':
            self.ground_friction_sp.setEnabled(True)
            self.ground_friction_speed_range_side_sp.setEnabled(False)
            self.ground_friction_speed_range_front_sp.setEnabled(False)
            self.mapped_ground_friction_range_side_sp.setEnabled(False)
            self.mapped_ground_friction_range_front_sp.setEnabled(False)
        elif self.ground_friction_type_combo.currentText() == 'Dynamic':
            self.ground_friction_sp.setEnabled(False)
            self.ground_friction_speed_range_side_sp.setEnabled(True)
            self.ground_friction_speed_range_front_sp.setEnabled(True)
            self.mapped_ground_friction_range_side_sp.setEnabled(True)
            self.mapped_ground_friction_range_front_sp.setEnabled(True)
        else:
            self.ground_friction_sp.setEnabled(False)
            self.ground_friction_speed_range_side_sp.setEnabled(False)
            self.ground_friction_speed_range_front_sp.setEnabled(False)
            self.mapped_ground_friction_range_side_sp.setEnabled(False)
            self.mapped_ground_friction_range_front_sp.setEnabled(False)
