# -*- coding: utf-8 -*-

from functools import partial
from Qt import QtWidgets, QtCore
from lsr.mobu.OG2_tool_lib.RootMotion_MBLinkUE.ui.traj_anim_table.traj_settings.view import MovementSettingsView
from lsr.mobu.OG2_tool_lib.RootMotion_MBLinkUE.ui.traj_anim_table.traj_settings.utils import GetMovementParameterRequest
from lsr.mobu.OG2_tool_lib.RootMotion_MBLinkUE.ui.traj_anim_table.traj_settings.utils import SetMovementParameterRequest
from lsr.mobu.OG2_tool_lib.RootMotion_MBLinkUE.ui.traj_anim_table.traj_settings.utils import SaveAndCheckoutRequest


class MovementSettingsController(QtWidgets.QWidget):
    """
    Controller for the movement settings view.
    """

    anim_tab_selection_changed_signal = QtCore.Signal(dict)
    set_unreal_settings_signal = QtCore.Signal()

    def __init__(self, parent=None, *args, **kwargs):
        """
        Initialize the MovementSettingsController.

        Args:
            parent (QtWidgets.QWidget, optional): The parent widget. Defaults to None.
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        super(MovementSettingsController, self).__init__(parent)
        self._init_layout()
        self._init_signal()

    def _init_layout(self, *args, **kwargs):
        """
        Initialize the layout of the MovementSettingsController.

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        layout = QtWidgets.QVBoxLayout(self)
        self.setLayout(layout)

        self.show_content_cb = QtWidgets.QCheckBox('显示配置', self)

        self.view = MovementSettingsView(self)
        self.tips_lab = QtWidgets.QLabel('修改完参数后点击同步才会生效↓', self)
        self.update_settings_btn = QtWidgets.QPushButton('同步到引擎', self)
        self.tips_save_and_sync = QtWidgets.QLabel('同步配置后需要点击按钮才能保存，并检出配置文件', self)
        self.save_and_sync_btn = QtWidgets.QPushButton('保存并检出引擎中的配置文件', self)
        layout.addWidget(self.show_content_cb)
        layout.addWidget(self.view)
        layout.addWidget(self.tips_lab)
        layout.addWidget(self.update_settings_btn)
        layout.addWidget(self.tips_save_and_sync)
        layout.addWidget(self.save_and_sync_btn)

    def _init_signal(self, *args, **kwargs):
        """
        Initialize the signals of the MovementSettingsController.

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        self.anim_tab_selection_changed_signal.connect(partial(self._update_movement_settings))
        self.update_settings_btn.clicked.connect(partial(self.set_unreal_settings_signal.emit))
        self.save_and_sync_btn.clicked.connect(partial(self.save_and_sync_settings))

    def save_and_sync_settings(self, *args, **kwargs):
        SaveAndCheckoutRequest().test_save_and_checkout_motion_model_dataasset()

    def set_unreal_settings(self, anim_tab_data={}, *args, **kwargs):
        if not anim_tab_data:
            return
        try:
            movement_params = self.view.get_movement_settings()
            SetMovementParameterRequest(anim_tab_data.get('MovementPattern', 'Veer'),
                                        anim_tab_data.get('MovementSpeed', 'Run')).set_response_from_ig(movement_params)
        except Exception as e:
            QtWidgets.QMessageBox.warning(self, '警告', '请先确定引擎是否启动，以及工程是否为最新版本。')


    def _update_movement_settings(self, anim_tab_data={}):
        if not anim_tab_data:
            return
        try:
            movement_params = GetMovementParameterRequest(anim_tab_data.get('MovementPattern', 'Veer'),
                                                         anim_tab_data.get('MovementSpeed', 'Run')).get_response_from_ig()
            self.view.update_movement_settings(movement_params)
        except Exception as e:
            QtWidgets.QMessageBox.warning(self, '警告', '请先确定引擎是否启动，以及工程是否为最新版本。')

    def paintEvent(self, event, *args, **kwargs):
        super(MovementSettingsController, self).paintEvent(event)
        if not self.show_content_cb.isChecked():
            self.view.hide()
            self.tips_lab.hide()
            self.update_settings_btn.hide()
            self.tips_save_and_sync.hide()
            self.save_and_sync_btn.hide()
        else:
            self.view.show()
            self.tips_lab.show()
            self.update_settings_btn.show()
            self.tips_save_and_sync.show()
            self.save_and_sync_btn.show()

    def save_settings(self, settings, *args, **kwargs):
        cb_state = self.show_content_cb.isChecked()
        settings.setValue('show_content_cb', cb_state)

    def load_settings(self, settings, *args, **kwargs):
        cb_state = True if settings.value('show_content_cb', 'true') == 'true' else False
        self.show_content_cb.setChecked(cb_state)
