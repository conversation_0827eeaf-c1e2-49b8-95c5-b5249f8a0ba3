""" PointConstraint node class. """

import pyfbsdk as fb

from six import string_types
from lsr.mobu.nodezoo.node import Node
from lsr.mobu.nodezoo.node import Constraint
from lsr.mobu.utils.FindObjects import get_object_by_name


class PointConstraint(Constraint):
    """
    Wrap PointConstraint node class.
    """

    __CUSTOMTYPE__ = 'PointConstraint'

    @classmethod
    def create(cls, *args, **kwargs):
        """Wrapper create function."""
        if len(args) == 2:
            parent = args[0]
            child = args[1]
            return cls.create_position_constraint(parent, child, **kwargs)
        elif len(args) > 2:
            parent = args[0]
            child = args[-1]
            other_parents = args[1:-1]
            constraint_node = cls.create_position_constraint(parent, child, **kwargs)
            for other_parent in other_parents:
                constraint_node.add_parent(other_parent)
            return constraint_node
        else:
            constraint_node = cls(fb.FBConstraintManager().TypeCreateConstraint('Position'))
            return constraint_node

    @classmethod
    def create_position_constraint(cls, parent, child, maintain_offset=False, lock=True, *args, **kwargs):
        """
        create position constraint

        Args:
            parent (str or Node): name of parent object
            child (str or Node): name of child object
            maintain_offset (bool): if true, maintain current offset while activating constraint
            lock (bool): if true, enable lock while activating constraint
            *args:
            **kwargs:

        Returns:
            None
        """
        name = kwargs.get('name', None)

        if isinstance(parent, string_types):
            parent = Node(get_object_by_name(parent))

        elif isinstance(parent, fb.FBModel):
            parent = Node(parent)

        if isinstance(child, string_types):
            child = Node(get_object_by_name(child))

        elif isinstance(child, fb.FBModel):
            child = Node(child)

        if not isinstance(parent, Node) or not isinstance(child, Node):
            raise ValueError('parent and child must be string or Node')

        constraint = cls(fb.FBConstraintManager().TypeCreateConstraint('Position'))

        if name:
            constraint.name = name
        else:
            constraint.name = 'Position_{}'.format(child.name)

        constraint.add_reference(0, child.fb_node)
        constraint.add_reference(1, parent.fb_node)

        if maintain_offset:
            constraint.snap()
        constraint.active = True
        constraint.lock = lock
        constraint.weight = 100.0

        return constraint
