# "luminous" colour theme for mintty
#
# The aim of this theme is to provide vibrant colours where the normal and bold
# levels each have approximately equal luminance. The variants of each colour
# have slightly different hues to help distinguish them.
#
# To level the colours, their luminance was calculated as follows:
# - Gamma-expand the sRGB components by dividing each by 255 and applying the
#   sRGB transfer function (which approximately means applying exponent 2.2).
# - Combine the components with formula Y = 0.2126 R + 0.7152 G + 0.0722 B.
# - Gamma-compress with the reverse transfer function (approximately: apply
#   exponent 1/2.2).
#
# https://en.wikipedia.org/wiki/SRGB
#
# The colours were then adjusted to achieve equal luminance. However, that does
# not actually yield colours with equal apparent brightness. This is due to the
# "Helmholtz-Kohlrausch effect", which means that some saturated colours appear
# brighter than others to the human eye:
#
# https://en.wikipedia.org/wiki/He<PERSON>holtz-<PERSON>rausch_effect
#
# See also the "helmholtz" and "kohlrausch" themes that attempt to compensate
# for that.

Black=0,0,0
Red=228,0,19
Green=22,132,0
Yellow=135,112,0
Blue=0,102,255
Magenta=190,0,228
Cyan=0,128,117
White=191,191,191

BoldBlack=96,96,96
BoldRed=255,127,127
BoldGreen=0,192,0
BoldYellow=171,171,0
BoldBlue=85,170,255
BoldMagenta=255,102,255
BoldCyan=0,184,184
BoldWhite=255,255,255
