# help.pt.txt - pt GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
Você decide que valor usar aqui; este valor nunca será exportado para
terceiros. Precisamos dele implementar a rede de confiança, que não tem
nada a ver com a rede de certificados (implicitamente criada).
.

.gpg.edit_ownertrust.set_ultimate.okay
Para construir a Teia-de-Confiança ('Web-of-Trust'), o GnuPG precisa de
saber quais são as chaves em que deposita confiança absoluta - normalmente
estas são as chaves a que tem acesso à chave privada.  Responda "sim" para
que esta chave seja de confiança absoluta.

.

.gpg.untrusted_key.override
Se você quiser usar esta chave, não de confiança, assim mesmo, responda "sim".
.

.gpg.pklist.user_id.enter
Digite o ID de utilizador do destinatário para quem quer enviar a
mensagem.
.

.#gpg.keygen.algo
# fixme: Please translate and remove the hash mark from the key line.
Select the algorithm to use.

DSA (aka DSS) is the Digital Signature Algorithm and can only be used
for signatures.

Elgamal is an encrypt-only algorithm.

RSA may be used for signatures or encryption.

The first (primary) key must always be a key which is capable of signing.
.

.gpg.keygen.algo.rsa_se
Em geral não é uma boa ideia utilizar a mesma chave para assinar e para
cifrar.  Este algoritmo só deve ser utilizado em alguns domínios.
Por favor consulte primeiro o seu perito em segurança.
.

.gpg.keygen.size
Insira o tamanho da chave
.

.gpg.keygen.size.huge.okay
Responda "sim" ou "não"
.

.gpg.keygen.size.large.okay
Responda "sim" ou "não"
.

.gpg.keygen.valid
Digite o valor necessário conforme pedido.
É possível digitar uma data ISO (AAAA-MM-DD) mas você não terá uma boa
reacção a erros - o sistema tentará interpretar o valor dado como um intervalo.
.

.gpg.keygen.valid.okay
Responda "sim" ou "não"
.

.gpg.keygen.name
Digite o nome do possuidor da chave
.

.gpg.keygen.email
por favor digite um endereço de email (opcional mas recomendado)
.

.gpg.keygen.comment
Por favor digite um comentário (opcional)
.

.gpg.keygen.userid.cmd
N  para mudar o nome.
C  para mudar o comentário.
E  para mudar o endereço de email
O  para continuar a geração da chave.
S  para interromper a geração da chave.
.

.gpg.keygen.sub.okay
Responda "sim" (ou apenas "s") se quiser gerar a subchave.
.

.gpg.sign_uid.okay
Responda "sim" ou "não"
.

.gpg.sign_uid.class
Quando assina uma chave de identificação de um utilizador, deve primeiro
verificar que a chave pertence realmente à pessoa em questão. É útil para
terceiros saberem com que cuidado é que efectuou esta verificação.

"0" significa que não deseja declarar a forma com verificou a chave

"1" significa que acredita que a chave pertence à pessoa em questão, mas
    não conseguiu ou não tentou verificar. Este grau é útil para quando
    assina a chave de uma utilizador pseudo-anónimo.

"2" significa que efectuou uma verificação normal da chave. Por exemplo,
    isto pode significar que verificou a impressão digital da chave e
    verificou o identificador de utilizador da chave contra uma identificação
    fotográfica.

"3" significa que efectuou uma verificação exaustiva da chave. Por exemplo,
    isto pode significar que efectuou a verificação pessoalmente, e que 
    utilizou um documento, com fotografia, difícil de falsificar 
    (como por exemplo um passaporte) que o nome do dono da chave é o
    mesmo do que o identificador da chave, e que, finalmente, verificou
    (através de troca de e-mail) que o endereço de email da chave pertence
    ao done da chave.

Atenção: os exemplos dados para os níveis 2 e 3 são *apenas* exemplos.
Compete-lhe a si decidir o que considera, ao assinar chaves, uma verificação
"normal" e uma verificação "exaustiva".

Se não sabe qual é a resposta correcta, responda "0".
.

.gpg.change_passwd.empty.okay
Responda "sim" ou "não"
.

.gpg.keyedit.save.okay
Responda "sim" ou "não"
.

.gpg.keyedit.cancel.okay
Responda "sim" ou "não"
.

.#gpg.keyedit.sign_all.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" if you want to sign ALL the user IDs
.

.gpg.keyedit.remove.uid.okay
Responda "sim" se quiser realmente remover este ID de utilizador.
Todos os certificados também serão perdidos!
.

.gpg.keyedit.remove.subkey.okay
Responda "sim" se quiser remover a subchave
.

.gpg.keyedit.delsig.valid
Esta é uma assinatura válida na chave; normalmente não é desejável
remover esta assinatura porque ela pode ser importante para estabelecer
uma conexão de confiança à chave ou a outra chave certificada por esta.
.

.gpg.keyedit.delsig.unknown
Esta assinatura não pode ser verificada porque você não tem a chave
correspondente. Você deve adiar sua remoção até saber que chave foi usada
porque a chave desta assinatura pode estabelecer uma conexão de confiança
através de outra chave já certificada.
.

.gpg.keyedit.delsig.invalid
A assinatura não é válida. Faz sentido removê-la do seu porta-chaves.
.

.gpg.keyedit.delsig.selfsig
Esta é uma assinatura que liga o ID de utilizador à chave. Geralmente
não é uma boa idéia remover tal assinatura. É possível que o GnuPG
não consiga mais usar esta chave. Faça isto apenas se por alguma
razão esta auto-assinatura não for válida e há uma segunda disponível.
.

.gpg.keyedit.updpref.okay
Muda as preferências de todos os identificadores de utilizadores
(ou apenas dos seleccionados) para a lista actual de preferências.
O 'timestamp' de todas as auto-assinaturas afectuadas será avançado
em um segundo.

.

.gpg.passphrase.enter
Por favor digite a frase secreta 

.

.gpg.passphrase.repeat
Por favor repita a frase secreta, para ter certeza do que digitou.
.

.gpg.detached_signature.filename
Dê o nome para o ficheiro ao qual a assinatura se aplica
.

.gpg.openfile.overwrite.okay
Responda "sim" se quiser escrever por cima do ficheiro
.

.gpg.openfile.askoutname
Por favor digite um novo nome de ficheiro. Se você apenas carregar em RETURN
o ficheiro por omissão (que é mostrado entre parênteses) será utilizado.
.

.gpg.ask_revocation_reason.code
Deve especificar uma razão para a emissão do certificado. Dependendo no
contexto, pode escolher as seguintes opções desta lista:
  "A chave foi comprometida"
     Utilize esta opção se tem razões para acreditar que indivíduos não
     autorizados obtiveram acesso à sua chave secreta.
  "A chave foi substituida"
     Utilize esta opção se substituiu esta chave com uma mais recente.
  "A chave já não é utilizada"
     Utilize esta opção se já não utiliza a chave.
  "O identificador do utilizador já não é válido"
     Utilize esta opção para comunicar que o identificador do utilizador
     não deve ser mais utilizado; normalmente utilizada para indicar
     que um endereço de email é inválido.

.

.gpg.ask_revocation_reason.text
Se desejar, pode inserir uma texto descrevendo a razão pela qual criou
este certificado de revogação. Por favor mantenha este texto conciso.
Uma linha vazia termina o texto.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
