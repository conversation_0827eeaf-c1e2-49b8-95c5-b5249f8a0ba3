*os_amiga.txt*  For Vim version 8.2.  Last change: 2010 Aug 14


		  VIM REFERENCE MANUAL    by <PERSON>


							*Amiga*
This file contains the particularities for the Amiga version of Vim.
There is also a section specifically for |MorphOS| below.

NOTE: The Amiga code is still included, but has not been maintained or tested.

Installation on the Amiga:
- Assign "VIM:" to the directory where the Vim "doc" directory is.  Vim will
  look for the file "VIM:doc/help.txt" (for the help command).
  Setting the environment variable $VIM also works.  And the other way around:
  when $VIM used and it is not defined, "VIM:" is used.
- With DOS 1.3 or earlier: Put "arp.library" in "libs:".  Vim must have been
  compiled with the |+ARP| feature enabled.  Make sure that newcli and run are
  in "C:" (for executing external commands).
- Put a shell that accepts a command with "-c" (e.g. "Csh" from Fish disk
  624) in "c:" or in any other directory that is in your search path (for
  executing external commands).

If you have sufficient memory you can avoid startup delays by making <PERSON><PERSON> and
csh resident with the command "rez csh vim".  You will have to put
"rezlib.library" in your "libs:" directory.  Under 2.0 you will need rez
version 0.5.

If you do not use digraphs, you can save some memory by recompiling without
the |+digraphs| feature.  If you want to use Vim with other terminals you can
recompile with the TERMCAP option.  Vim compiles with Manx 5.x and SAS 6.x.
See the makefiles and feature.h.

If you notice Vim crashes on some files when syntax highlighting is on, or
when using a search pattern with nested wildcards, it might be that the stack
is too small.  Try increasing the stack size.  In a shell use the Stack
command before launching Vim.  On the Workbench, select the Vim icon, use the
workbench "Info" menu and change the Stack field in the form.

If you want to use different colors set the termcap codes:
	t_mr (for inverted text)
	t_md (for bold text)
	t_me (for normal text after t_mr and t_md)
	t_so (for standout mode)
	t_se (for normal text after t_so)
	t_us (for underlined text)
	t_ue (for normal text after t_us)
	t_ZH (for italic text)
	t_ZR (for normal text after t_ZH)

Standard ANSI escape sequences are used.  The codes are:
30 grey char   40 grey cell   >0 grey background    0 all attributes off
31 black char  41 black cell  >1 black background   1 boldface
32 white char  42 white cell  >2 white background   2 faint
33 blue char   43 blue cell   >3 blue background    3 italic
34 grey char   44 grey cell   >4 grey background    4 underscore
35 black char  45 black cell  >5 black background   7 reverse video
36 white char  46 white cell  >6 white background   8 invisible
37 blue char   47 blue cell   >7 blue background

The codes with '>' must be the last.  The cell and background color should be
the same.  The codes can be combined by separating them with a semicolon.  For
example to get white text on a blue background: >
  :set t_me=^V<Esc>[0;32;43;>3m
  :set t_se=^V<Esc>[0;32;43;>3m
  :set t_ue=^V<Esc>[0;32;43;>3m
  :set t_ZR=^V<Esc>[0;32;43;>3m
  :set t_md=^V<Esc>[1;32;43;>3m
  :set t_mr=^V<Esc>[7;32;43;>3m
  :set t_so=^V<Esc>[0;31;43;>3m
  :set t_us=^V<Esc>[4;32;43;>3m
  :set t_ZH=^V<Esc>[3;32;43;>3m

When using multiple commands with a filter command, e.g. >
  :r! echo this; echo that
Only the output of the last command is used.  To fix this you have to group the
commands.  This depends on the shell you use (that is why it is not done
automatically in Vim).  Examples: >
  :r! (echo this; echo that)
  :r! {echo this; echo that}

Commands that accept a single file name allow for embedded spaces in the file
name.  However, when using commands that accept several file names, embedded
spaces need to be escaped with a backslash.

------------------------------------------------------------------------------
Vim for MorphOS							*MorphOS*

[this section mostly by Ali Akcaagac]

For the latest info about the MorphOS version:
	http://www.akcaagac.com/index_vim.html


Problems ~

There are a couple of problems which are not MorphOS related but more Vim and
UN*X related.  When starting up Vim in ram: it complains with a nag requester
from MorphOS please simply ignore it.  Another problem is when running Vim as
is some plugins will cause a few problems which you can ignore as well.
Hopefully someone will be fixing it over the time.

To pass all these problems for now you can either run:

	vim <file to be edited>

or if you want to run Vim plain and enjoy the motion of Helpfiles etc. it then
would be better to enter:

	vim --noplugins <of course you can add a file>


Installation ~

1) Please copy the binary 'VIM' file to c:
2) Get the Vim runtime package from:

	ftp://ftp.vim.org/pub/vim/amiga/vim62rt.tgz

   and unpack it in your 'Apps' directory of the MorphOS installation.  For me
   this would create following directory hierarchy:

	MorphOS:Apps/Vim/Vim62/...

3) Add the following lines to your s:shell-startup (Important!).

	;Begin VIM
	Set VIM=MorphOS:Apps/Vim/Vim62
	Assign HOME: ""
	;End VIM

4) Copy the '.vimrc' file to s:

5) There is also a file named 'color-sequence' included in this archive.  This
   will set the MorphOS Shell to show ANSI colors.  Please copy the file to s:
   and change the s:shell-startup to:

	;Begin VIM
	Set VIM=MorphOS:Apps/Vim/Vim62
	Assign HOME: ""
	Execute S:Color-Sequence
	Cls
	;End VIM


 vim:tw=78:ts=8:noet:ft=help:norl:
