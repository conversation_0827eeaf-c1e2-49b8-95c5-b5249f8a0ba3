<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<meta charset="UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta name="generator" content="Asciidoctor 2.0.17"/>
<title>My First Contribution to the Git Project</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"/>
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.square li ul,ul.circle li ul,ul.disc li ul{list-style:inherit}
ul.square{list-style-type:square}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child,body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;outline:none;-webkit-tap-highlight-color:transparent}
details>summary::-webkit-details-marker{display:none}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.exampleblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child{margin-bottom:0}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>:first-child{margin-top:0}
.sidebarblock>:last-child{margin-bottom:0}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos,pre.pygments .linenos{border-right:1px solid;opacity:.35;padding-right:.5em;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
pre.pygments span.linenos{display:inline-block;margin-right:.75em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all>*>tr,table.stripes-odd>*>tr:nth-of-type(odd),table.stripes-even>*>tr:nth-of-type(even),table.stripes-hover>*>tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
li>p:empty:only-child::before{content:"";display:inline-block}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
</head>
<body class="article">
<div id="header">
<h1>My First Contribution to the Git Project</h1>
</div>
<div id="content">
<div class="sect1">
<h2 id="summary"><a class="anchor" href="#summary"></a>Summary</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This is a tutorial demonstrating the end-to-end workflow of creating a change to
the Git tree, sending it for review, and making changes based on comments.</p>
</div>
<div class="sect2">
<h3 id="prerequisites"><a class="anchor" href="#prerequisites"></a>Prerequisites</h3>
<div class="paragraph">
<p>This tutorial assumes you&#8217;re already fairly familiar with using Git to manage
source code.  The Git workflow steps will largely remain unexplained.</p>
</div>
</div>
<div class="sect2">
<h3 id="related-reading"><a class="anchor" href="#related-reading"></a>Related Reading</h3>
<div class="paragraph">
<p>This tutorial aims to summarize the following documents, but the reader may find
useful additional context:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>Documentation/SubmittingPatches</code></p>
</li>
<li>
<p><code>Documentation/howto/new-command.txt</code></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="getting-help"><a class="anchor" href="#getting-help"></a>Getting Help</h3>
<div class="paragraph">
<p>If you get stuck, you can seek help in the following places.</p>
</div>
<div class="sect3">
<h4 id="_gitvger_kernel_org"><a class="anchor" href="#_gitvger_kernel_org"></a><a href="mailto:*******************">*******************</a></h4>
<div class="paragraph">
<p>This is the main Git project mailing list where code reviews, version
announcements, design discussions, and more take place. Those interested in
contributing are welcome to post questions here. The Git list requires
plain-text-only emails and prefers inline and bottom-posting when replying to
mail; you will be CC&#8217;d in all replies to you. Optionally, you can subscribe to
the list by sending an email to <a href="mailto:<EMAIL>"><EMAIL></a> with "subscribe git"
in the body. The <a href="https://lore.kernel.org/git">archive</a> of this mailing list is
available to view in a browser.</p>
</div>
</div>
<div class="sect3">
<h4 id="_git_mentoringgooglegroups_com"><a class="anchor" href="#_git_mentoringgooglegroups_com"></a><a href="https://groups.google.com/forum/#!forum/git-mentoring"><EMAIL></a></h4>
<div class="paragraph">
<p>This mailing list is targeted to new contributors and was created as a place to
post questions and receive answers outside of the public eye of the main list.
Veteran contributors who are especially interested in helping mentor newcomers
are present on the list. In order to avoid search indexers, group membership is
required to view messages; anyone can join and no approval is required.</p>
</div>
</div>
<div class="sect3">
<h4 id="_git_devel_on_libera_chat"><a class="anchor" href="#_git_devel_on_libera_chat"></a><a href="https://web.libera.chat/#git-devel">#git-devel</a> on Libera Chat</h4>
<div class="paragraph">
<p>This IRC channel is for conversations between Git contributors. If someone is
currently online and knows the answer to your question, you can receive help
in real time. Otherwise, you can read the
<a href="https://colabti.org/irclogger/irclogger_logs/git-devel">scrollback</a> to see
whether someone answered you. IRC does not allow offline private messaging, so
if you try to private message someone and then log out of IRC, they cannot
respond to you. It&#8217;s better to ask your questions in the channel so that you
can be answered if you disconnect and so that others can learn from the
conversation.</p>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="getting-started"><a class="anchor" href="#getting-started"></a>Getting Started</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="cloning"><a class="anchor" href="#cloning"></a>Clone the Git Repository</h3>
<div class="paragraph">
<p>Git is mirrored in a number of locations. Clone the repository from one of them;
<a href="https://git-scm.com/downloads" class="bare">https://git-scm.com/downloads</a> suggests one of the best places to clone from is
the mirror on GitHub.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git clone https://github.com/git/git git
$ cd git</pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="dependencies"><a class="anchor" href="#dependencies"></a>Installing Dependencies</h3>
<div class="paragraph">
<p>To build Git from source, you need to have a handful of dependencies installed
on your system. For a hint of what&#8217;s needed, you can take a look at
<code>INSTALL</code>, paying close attention to the section about Git&#8217;s dependencies on
external programs and libraries. That document mentions a way to "test-drive"
our freshly built Git without installing; that&#8217;s the method we&#8217;ll be using in
this tutorial.</p>
</div>
<div class="paragraph">
<p>Make sure that your environment has everything you need by building your brand
new clone of Git from the above step:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ make</pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
The Git build is parallelizable. <code>-j#</code> is not included above but you can
use it as you prefer, here and elsewhere.
</td>
</tr>
</table>
</div>
</div>
<div class="sect2">
<h3 id="identify-problem"><a class="anchor" href="#identify-problem"></a>Identify Problem to Solve</h3>
<div class="paragraph">
<p>In this tutorial, we will add a new command, <code>git psuh</code>, short for &#8220;Pony Saying
&#8216;Um, Hello&#8221;&#8217; - a feature which has gone unimplemented despite a high frequency
of invocation during users' typical daily workflow.</p>
</div>
<div class="paragraph">
<p>(We&#8217;ve seen some other effort in this space with the implementation of popular
commands such as <code>sl</code>.)</p>
</div>
</div>
<div class="sect2">
<h3 id="setup-workspace"><a class="anchor" href="#setup-workspace"></a>Set Up Your Workspace</h3>
<div class="paragraph">
<p>Let&#8217;s start by making a development branch to work on our changes. Per
<code>Documentation/SubmittingPatches</code>, since a brand new command is a new feature,
it&#8217;s fine to base your work on <code>master</code>. However, in the future for bugfixes,
etc., you should check that document and base it on the appropriate branch.</p>
</div>
<div class="paragraph">
<p>For the purposes of this document, we will base all our work on the <code>master</code>
branch of the upstream project. Create the <code>psuh</code> branch you will use for
development like so:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git checkout -b psuh origin/master</pre>
</div>
</div>
<div class="paragraph">
<p>We&#8217;ll make a number of commits here in order to demonstrate how to send a topic
with multiple patches up for review simultaneously.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="code-it-up"><a class="anchor" href="#code-it-up"></a>Code It Up!</h2>
<div class="sectionbody">
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
A reference implementation can be found at
<a href="https://github.com/nasamuffin/git/tree/psuh" class="bare">https://github.com/nasamuffin/git/tree/psuh</a>.
</td>
</tr>
</table>
</div>
<div class="sect2">
<h3 id="add-new-command"><a class="anchor" href="#add-new-command"></a>Adding a New Command</h3>
<div class="paragraph">
<p>Lots of the subcommands are written as builtins, which means they are
implemented in C and compiled into the main <code>git</code> executable. Implementing the
very simple <code>psuh</code> command as a built-in will demonstrate the structure of the
codebase, the internal API, and the process of working together as a contributor
with the reviewers and maintainer to integrate this change into the system.</p>
</div>
<div class="paragraph">
<p>Built-in subcommands are typically implemented in a function named "cmd_"
followed by the name of the subcommand, in a source file named after the
subcommand and contained within <code>builtin/</code>. So it makes sense to implement your
command in <code>builtin/psuh.c</code>. Create that file, and within it, write the entry
point for your command in a function matching the style and signature:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>int cmd_psuh(int argc, const char **argv, const char *prefix)</pre>
</div>
</div>
<div class="paragraph">
<p>We&#8217;ll also need to add the declaration of psuh; open up <code>builtin.h</code>, find the
declaration for <code>cmd_pull</code>, and add a new line for <code>psuh</code> immediately before it,
in order to keep the declarations alphabetically sorted:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>int cmd_psuh(int argc, const char **argv, const char *prefix);</pre>
</div>
</div>
<div class="paragraph">
<p>Be sure to <code>#include "builtin.h"</code> in your <code>psuh.c</code>.</p>
</div>
<div class="paragraph">
<p>Go ahead and add some throwaway printf to that function. This is a decent
starting point as we can now add build rules and register the command.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
Your throwaway text, as well as much of the text you will be adding over
the course of this tutorial, is user-facing. That means it needs to be
localizable. Take a look at <code>po/README</code> under "Marking strings for translation".
Throughout the tutorial, we will mark strings for translation as necessary; you
should also do so when writing your user-facing commands in the future.
</td>
</tr>
</table>
</div>
<div class="listingblock">
<div class="content">
<pre>int cmd_psuh(int argc, const char **argv, const char *prefix)
{
        printf(_("Pony saying hello goes here.\n"));
        return 0;
}</pre>
</div>
</div>
<div class="paragraph">
<p>Let&#8217;s try to build it.  Open <code>Makefile</code>, find where <code>builtin/pull.o</code> is added
to <code>BUILTIN_OBJS</code>, and add <code>builtin/psuh.o</code> in the same way next to it in
alphabetical order. Once you&#8217;ve done so, move to the top-level directory and
build simply with <code>make</code>. Also add the <code>DEVELOPER=1</code> variable to turn on
some additional warnings:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ echo DEVELOPER=1 &gt;config.mak
$ make</pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
When you are developing the Git project, it&#8217;s preferred that you use the
<code>DEVELOPER</code> flag; if there&#8217;s some reason it doesn&#8217;t work for you, you can turn
it off, but it&#8217;s a good idea to mention the problem to the mailing list.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>Great, now your new command builds happily on its own. But nobody invokes it.
Let&#8217;s change that.</p>
</div>
<div class="paragraph">
<p>The list of commands lives in <code>git.c</code>. We can register a new command by adding
a <code>cmd_struct</code> to the <code>commands[]</code> array. <code>struct cmd_struct</code> takes a string
with the command name, a function pointer to the command implementation, and a
setup option flag. For now, let&#8217;s keep mimicking <code>push</code>. Find the line where
<code>cmd_push</code> is registered, copy it, and modify it for <code>cmd_psuh</code>, placing the new
line in alphabetical order (immediately before <code>cmd_pull</code>).</p>
</div>
<div class="paragraph">
<p>The options are documented in <code>builtin.h</code> under "Adding a new built-in." Since
we hope to print some data about the user&#8217;s current workspace context later,
we need a Git directory, so choose <code>RUN_SETUP</code> as your only option.</p>
</div>
<div class="paragraph">
<p>Go ahead and build again. You should see a clean build, so let&#8217;s kick the tires
and see if it works. There&#8217;s a binary you can use to test with in the
<code>bin-wrappers</code> directory.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ ./bin-wrappers/git psuh</pre>
</div>
</div>
<div class="paragraph">
<p>Check it out! You&#8217;ve got a command! Nice work! Let&#8217;s commit this.</p>
</div>
<div class="paragraph">
<p><code>git status</code> reveals modified <code>Makefile</code>, <code>builtin.h</code>, and <code>git.c</code> as well as
untracked <code>builtin/psuh.c</code> and <code>git-psuh</code>. First, let&#8217;s take care of the binary,
which should be ignored. Open <code>.gitignore</code> in your editor, find <code>/git-pull</code>, and
add an entry for your new command in alphabetical order:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>...
/git-prune-packed
/git-psuh
/git-pull
/git-push
/git-quiltimport
/git-range-diff
...</pre>
</div>
</div>
<div class="paragraph">
<p>Checking <code>git status</code> again should show that <code>git-psuh</code> has been removed from
the untracked list and <code>.gitignore</code> has been added to the modified list. Now we
can stage and commit:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git add Makefile builtin.h builtin/psuh.c git.c .gitignore
$ git commit -s</pre>
</div>
</div>
<div class="paragraph">
<p>You will be presented with your editor in order to write a commit message. Start
the commit with a 50-column or less subject line, including the name of the
component you&#8217;re working on, followed by a blank line (always required) and then
the body of your commit message, which should provide the bulk of the context.
Remember to be explicit and provide the "Why" of your change, especially if it
couldn&#8217;t easily be understood from your diff. When editing your commit message,
don&#8217;t remove the <code>Signed-off-by</code> trailer which was added by <code>-s</code> above.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>psuh: add a built-in by popular demand

Internal metrics indicate this is a command many users expect to be
present. So here's an implementation to help drive customer
satisfaction and engagement: a pony which doubtfully greets the user,
or, a Pony Saying "Um, Hello" (PSUH).

This commit message is intentionally formatted to 72 columns per line,
starts with a single line as "commit message subject" that is written as
if to command the codebase to do something (add this, teach a command
that). The body of the message is designed to add information about the
commit that is not readily deduced from reading the associated diff,
such as answering the question "why?".

Signed-off-by: A U Thor &lt;<EMAIL>&gt;</pre>
</div>
</div>
<div class="paragraph">
<p>Go ahead and inspect your new commit with <code>git show</code>. "psuh:" indicates you
have modified mainly the <code>psuh</code> command. The subject line gives readers an idea
of what you&#8217;ve changed. The sign-off line (<code>-s</code>) indicates that you agree to
the Developer&#8217;s Certificate of Origin 1.1 (see the
<code>Documentation/SubmittingPatches</code> [[dco]] header).</p>
</div>
<div class="paragraph">
<p>For the remainder of the tutorial, the subject line only will be listed for the
sake of brevity. However, fully-fleshed example commit messages are available
on the reference implementation linked at the top of this document.</p>
</div>
</div>
<div class="sect2">
<h3 id="implementation"><a class="anchor" href="#implementation"></a>Implementation</h3>
<div class="paragraph">
<p>It&#8217;s probably useful to do at least something besides printing out a string.
Let&#8217;s start by having a look at everything we get.</p>
</div>
<div class="paragraph">
<p>Modify your <code>cmd_psuh</code> implementation to dump the args you&#8217;re passed, keeping
existing <code>printf()</code> calls in place:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>        int i;

        ...

        printf(Q_("Your args (there is %d):\n",
                  "Your args (there are %d):\n",
                  argc),
               argc);
        for (i = 0; i &lt; argc; i++)
                printf("%d: %s\n", i, argv[i]);

        printf(_("Your current working directory:\n&lt;top-level&gt;%s%s\n"),
               prefix ? "/" : "", prefix ? prefix : "");</pre>
</div>
</div>
<div class="paragraph">
<p>Build and try it. As you may expect, there&#8217;s pretty much just whatever we give
on the command line, including the name of our command. (If <code>prefix</code> is empty
for you, try <code>cd Documentation/ &amp;&amp; ../bin-wrappers/git psuh</code>). That&#8217;s not so
helpful. So what other context can we get?</p>
</div>
<div class="paragraph">
<p>Add a line to <code>#include "config.h"</code>. Then, add the following bits to the
function body:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>        const char *cfg_name;

...

        git_config(git_default_config, NULL);
        if (git_config_get_string_tmp("user.name", &amp;cfg_name) &gt; 0)
                printf(_("No name is found in config\n"));
        else
                printf(_("Your name: %s\n"), cfg_name);</pre>
</div>
</div>
<div class="paragraph">
<p><code>git_config()</code> will grab the configuration from config files known to Git and
apply standard precedence rules. <code>git_config_get_string_tmp()</code> will look up
a specific key ("user.name") and give you the value. There are a number of
single-key lookup functions like this one; you can see them all (and more info
about how to use <code>git_config()</code>) in <code>Documentation/technical/api-config.txt</code>.</p>
</div>
<div class="paragraph">
<p>You should see that the name printed matches the one you see when you run:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git config --get user.name</pre>
</div>
</div>
<div class="paragraph">
<p>Great! Now we know how to check for values in the Git config. Let&#8217;s commit this
too, so we don&#8217;t lose our progress.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git add builtin/psuh.c
$ git commit -sm "psuh: show parameters &amp; config opts"</pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
Again, the above is for sake of brevity in this tutorial. In a real change
you should not use <code>-m</code> but instead use the editor to write a meaningful
message.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>Still, it&#8217;d be nice to know what the user&#8217;s working context is like. Let&#8217;s see
if we can print the name of the user&#8217;s current branch. We can mimic the
<code>git status</code> implementation; the printer is located in <code>wt-status.c</code> and we can
see that the branch is held in a <code>struct wt_status</code>.</p>
</div>
<div class="paragraph">
<p><code>wt_status_print()</code> gets invoked by <code>cmd_status()</code> in <code>builtin/commit.c</code>.
Looking at that implementation we see the status config being populated like so:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>status_init_config(&amp;s, git_status_config);</pre>
</div>
</div>
<div class="paragraph">
<p>But as we drill down, we can find that <code>status_init_config()</code> wraps a call
to <code>git_config()</code>. Let&#8217;s modify the code we wrote in the previous commit.</p>
</div>
<div class="paragraph">
<p>Be sure to include the header to allow you to use <code>struct wt_status</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>#include "wt-status.h"</pre>
</div>
</div>
<div class="paragraph">
<p>Then modify your <code>cmd_psuh</code> implementation to declare your <code>struct wt_status</code>,
prepare it, and print its contents:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>        struct wt_status status;

...

        wt_status_prepare(the_repository, &amp;status);
        git_config(git_default_config, &amp;status);

...

        printf(_("Your current branch: %s\n"), status.branch);</pre>
</div>
</div>
<div class="paragraph">
<p>Run it again. Check it out - here&#8217;s the (verbose) name of your current branch!</p>
</div>
<div class="paragraph">
<p>Let&#8217;s commit this as well.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git add builtin/psuh.c
$ git commit -sm "psuh: print the current branch"</pre>
</div>
</div>
<div class="paragraph">
<p>Now let&#8217;s see if we can get some info about a specific commit.</p>
</div>
<div class="paragraph">
<p>Luckily, there are some helpers for us here. <code>commit.h</code> has a function called
<code>lookup_commit_reference_by_name</code> to which we can simply provide a hardcoded
string; <code>pretty.h</code> has an extremely handy <code>pp_commit_easy()</code> call which doesn&#8217;t
require a full format object to be passed.</p>
</div>
<div class="paragraph">
<p>Add the following includes:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>#include "commit.h"
#include "pretty.h"</pre>
</div>
</div>
<div class="paragraph">
<p>Then, add the following lines within your implementation of <code>cmd_psuh()</code> near
the declarations and the logic, respectively.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>        struct commit *c = NULL;
        struct strbuf commitline = STRBUF_INIT;

...

        c = lookup_commit_reference_by_name("origin/master");

        if (c != NULL) {
                pp_commit_easy(CMIT_FMT_ONELINE, c, &amp;commitline);
                printf(_("Current commit: %s\n"), commitline.buf);
        }</pre>
</div>
</div>
<div class="paragraph">
<p>The <code>struct strbuf</code> provides some safety belts to your basic <code>char*</code>, one of
which is a length member to prevent buffer overruns. It needs to be initialized
nicely with <code>STRBUF_INIT</code>. Keep it in mind when you need to pass around <code>char*</code>.</p>
</div>
<div class="paragraph">
<p><code>lookup_commit_reference_by_name</code> resolves the name you pass it, so you can play
with the value there and see what kind of things you can come up with.</p>
</div>
<div class="paragraph">
<p><code>pp_commit_easy</code> is a convenience wrapper in <code>pretty.h</code> that takes a single
format enum shorthand, rather than an entire format struct. It then
pretty-prints the commit according to that shorthand. These are similar to the
formats available with <code>--pretty=FOO</code> in many Git commands.</p>
</div>
<div class="paragraph">
<p>Build it and run, and if you&#8217;re using the same name in the example, you should
see the subject line of the most recent commit in <code>origin/master</code> that you know
about. Neat! Let&#8217;s commit that as well.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git add builtin/psuh.c
$ git commit -sm "psuh: display the top of origin/master"</pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="add-documentation"><a class="anchor" href="#add-documentation"></a>Adding Documentation</h3>
<div class="paragraph">
<p>Awesome! You&#8217;ve got a fantastic new command that you&#8217;re ready to share with the
community. But hang on just a minute - this isn&#8217;t very user-friendly. Run the
following:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ ./bin-wrappers/git help psuh</pre>
</div>
</div>
<div class="paragraph">
<p>Your new command is undocumented! Let&#8217;s fix that.</p>
</div>
<div class="paragraph">
<p>Take a look at <code>Documentation/git-*.txt</code>. These are the manpages for the
subcommands that Git knows about. You can open these up and take a look to get
acquainted with the format, but then go ahead and make a new file
<code>Documentation/git-psuh.txt</code>. Like with most of the documentation in the Git
project, help pages are written with AsciiDoc (see CodingGuidelines, "Writing
Documentation" section). Use the following template to fill out your own
manpage:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>git-psuh(1)
===========

NAME
----
git-psuh - Delight users' typo with a shy horse


SYNOPSIS
--------
[verse]
'git-psuh [&lt;arg&gt;...]'

DESCRIPTION
-----------
...

OPTIONS[[OPTIONS]]
------------------
...

OUTPUT
------
...

GIT
---
Part of the linkgit:git[1] suite</pre>
</div>
</div>
<div class="paragraph">
<p>The most important pieces of this to note are the file header, underlined by =,
the NAME section, and the SYNOPSIS, which would normally contain the grammar if
your command took arguments. Try to use well-established manpage headers so your
documentation is consistent with other Git and UNIX manpages; this makes life
easier for your user, who can skip to the section they know contains the
information they need.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
Before trying to build the docs, make sure you have the package <code>asciidoc</code>
installed.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>Now that you&#8217;ve written your manpage, you&#8217;ll need to build it explicitly. We
convert your AsciiDoc to troff which is man-readable like so:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ make all doc
$ man Documentation/git-psuh.1</pre>
</div>
</div>
<div class="paragraph">
<p>or</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ make -C Documentation/ git-psuh.1
$ man Documentation/git-psuh.1</pre>
</div>
</div>
<div class="paragraph">
<p>While this isn&#8217;t as satisfying as running through <code>git help</code>, you can at least
check that your help page looks right.</p>
</div>
<div class="paragraph">
<p>You can also check that the documentation coverage is good (that is, the project
sees that your command has been implemented as well as documented) by running
<code>make check-docs</code> from the top-level.</p>
</div>
<div class="paragraph">
<p>Go ahead and commit your new documentation change.</p>
</div>
</div>
<div class="sect2">
<h3 id="add-usage"><a class="anchor" href="#add-usage"></a>Adding Usage Text</h3>
<div class="paragraph">
<p>Try and run <code>./bin-wrappers/git psuh -h</code>. Your command should crash at the end.
That&#8217;s because <code>-h</code> is a special case which your command should handle by
printing usage.</p>
</div>
<div class="paragraph">
<p>Take a look at <code>Documentation/technical/api-parse-options.txt</code>. This is a handy
tool for pulling out options you need to be able to handle, and it takes a
usage string.</p>
</div>
<div class="paragraph">
<p>In order to use it, we&#8217;ll need to prepare a NULL-terminated array of usage
strings and a <code>builtin_psuh_options</code> array.</p>
</div>
<div class="paragraph">
<p>Add a line to <code>#include "parse-options.h"</code>.</p>
</div>
<div class="paragraph">
<p>At global scope, add your array of usage strings:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>static const char * const psuh_usage[] = {
        N_("git psuh [&lt;arg&gt;...]"),
        NULL,
};</pre>
</div>
</div>
<div class="paragraph">
<p>Then, within your <code>cmd_psuh()</code> implementation, we can declare and populate our
<code>option</code> struct. Ours is pretty boring but you can add more to it if you want to
explore <code>parse_options()</code> in more detail:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>        struct option options[] = {
                OPT_END()
        };</pre>
</div>
</div>
<div class="paragraph">
<p>Finally, before you print your args and prefix, add the call to
<code>parse-options()</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>        argc = parse_options(argc, argv, prefix, options, psuh_usage, 0);</pre>
</div>
</div>
<div class="paragraph">
<p>This call will modify your <code>argv</code> parameter. It will strip the options you
specified in <code>options</code> from <code>argv</code> and the locations pointed to from <code>options</code>
entries will be updated. Be sure to replace your <code>argc</code> with the result from
<code>parse_options()</code>, or you will be confused if you try to parse <code>argv</code> later.</p>
</div>
<div class="paragraph">
<p>It&#8217;s worth noting the special argument <code>--</code>. As you may be aware, many Unix
commands use <code>--</code> to indicate "end of named parameters" - all parameters after
the <code>--</code> are interpreted merely as positional arguments. (This can be handy if
you want to pass as a parameter something which would usually be interpreted as
a flag.) <code>parse_options()</code> will terminate parsing when it reaches <code>--</code> and give
you the rest of the options afterwards, untouched.</p>
</div>
<div class="paragraph">
<p>Now that you have a usage hint, you can teach Git how to show it in the general
command list shown by <code>git help git</code> or <code>git help -a</code>, which is generated from
<code>command-list.txt</code>. Find the line for <em>git-pull</em> so you can add your <em>git-psuh</em>
line above it in alphabetical order. Now, we can add some attributes about the
command which impacts where it shows up in the aforementioned help commands. The
top of <code>command-list.txt</code> shares some information about what each attribute
means; in those help pages, the commands are sorted according to these
attributes. <code>git psuh</code> is user-facing, or porcelain - so we will mark it as
"mainporcelain". For "mainporcelain" commands, the comments at the top of
<code>command-list.txt</code> indicate we can also optionally add an attribute from another
list; since <code>git psuh</code> shows some information about the user&#8217;s workspace but
doesn&#8217;t modify anything, let&#8217;s mark it as "info". Make sure to keep your
attributes in the same style as the rest of <code>command-list.txt</code> using spaces to
align and delineate them:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>git-prune-packed                        plumbingmanipulators
git-psuh                                mainporcelain           info
git-pull                                mainporcelain           remote
git-push                                mainporcelain           remote</pre>
</div>
</div>
<div class="paragraph">
<p>Build again. Now, when you run with <code>-h</code>, you should see your usage printed and
your command terminated before anything else interesting happens. Great!</p>
</div>
<div class="paragraph">
<p>Go ahead and commit this one, too.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="testing"><a class="anchor" href="#testing"></a>Testing</h2>
<div class="sectionbody">
<div class="paragraph">
<p>It&#8217;s important to test your code - even for a little toy command like this one.
Moreover, your patch won&#8217;t be accepted into the Git tree without tests. Your
tests should:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Illustrate the current behavior of the feature</p>
</li>
<li>
<p>Prove the current behavior matches the expected behavior</p>
</li>
<li>
<p>Ensure the externally-visible behavior isn&#8217;t broken in later changes</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>So let&#8217;s write some tests.</p>
</div>
<div class="paragraph">
<p>Related reading: <code>t/README</code></p>
</div>
<div class="sect2">
<h3 id="overview-test-structure"><a class="anchor" href="#overview-test-structure"></a>Overview of Testing Structure</h3>
<div class="paragraph">
<p>The tests in Git live in <code>t/</code> and are named with a 4-digit decimal number using
the schema shown in the Naming Tests section of <code>t/README</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="write-new-test"><a class="anchor" href="#write-new-test"></a>Writing Your Test</h3>
<div class="paragraph">
<p>Since this a toy command, let&#8217;s go ahead and name the test with t9999. However,
as many of the family/subcmd combinations are full, best practice seems to be
to find a command close enough to the one you&#8217;ve added and share its naming
space.</p>
</div>
<div class="paragraph">
<p>Create a new file <code>t/t9999-psuh-tutorial.sh</code>. Begin with the header as so (see
"Writing Tests" and "Source <em>test-lib.sh</em>" in <code>t/README</code>):</p>
</div>
<div class="listingblock">
<div class="content">
<pre>#!/bin/sh

test_description='git-psuh test

This test runs git-psuh and makes sure it does not crash.'

. ./test-lib.sh</pre>
</div>
</div>
<div class="paragraph">
<p>Tests are framed inside of a <code>test_expect_success</code> in order to output TAP
formatted results. Let&#8217;s make sure that <code>git psuh</code> doesn&#8217;t exit poorly and does
mention the right animal somewhere:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>test_expect_success 'runs correctly with no args and good output' '
        git psuh &gt;actual &amp;&amp;
        grep Pony actual
'</pre>
</div>
</div>
<div class="paragraph">
<p>Indicate that you&#8217;ve run everything you wanted by adding the following at the
bottom of your script:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>test_done</pre>
</div>
</div>
<div class="paragraph">
<p>Make sure you mark your test script executable:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ chmod +x t/t9999-psuh-tutorial.sh</pre>
</div>
</div>
<div class="paragraph">
<p>You can get an idea of whether you created your new test script successfully
by running <code>make -C t test-lint</code>, which will check for things like test number
uniqueness, executable bit, and so on.</p>
</div>
</div>
<div class="sect2">
<h3 id="local-test"><a class="anchor" href="#local-test"></a>Running Locally</h3>
<div class="paragraph">
<p>Let&#8217;s try and run locally:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ make
$ cd t/ &amp;&amp; prove t9999-psuh-tutorial.sh</pre>
</div>
</div>
<div class="paragraph">
<p>You can run the full test suite and ensure <code>git-psuh</code> didn&#8217;t break anything:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ cd t/
$ prove -j$(nproc) --shuffle t[0-9]*.sh</pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
You can also do this with <code>make test</code> or use any testing harness which can
speak TAP. <code>prove</code> can run concurrently. <code>shuffle</code> randomizes the order the
tests are run in, which makes them resilient against unwanted inter-test
dependencies. <code>prove</code> also makes the output nicer.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>Go ahead and commit this change, as well.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="ready-to-share"><a class="anchor" href="#ready-to-share"></a>Getting Ready to Share</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You may have noticed already that the Git project performs its code reviews via
emailed patches, which are then applied by the maintainer when they are ready
and approved by the community. The Git project does not accept patches from
pull requests, and the patches emailed for review need to be formatted a
specific way. At this point the tutorial diverges, in order to demonstrate two
different methods of formatting your patchset and getting it reviewed.</p>
</div>
<div class="paragraph">
<p>The first method to be covered is GitGitGadget, which is useful for those
already familiar with GitHub&#8217;s common pull request workflow. This method
requires a GitHub account.</p>
</div>
<div class="paragraph">
<p>The second method to be covered is <code>git send-email</code>, which can give slightly
more fine-grained control over the emails to be sent. This method requires some
setup which can change depending on your system and will not be covered in this
tutorial.</p>
</div>
<div class="paragraph">
<p>Regardless of which method you choose, your engagement with reviewers will be
the same; the review process will be covered after the sections on GitGitGadget
and <code>git send-email</code>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto-ggg"><a class="anchor" href="#howto-ggg"></a>Sending Patches via GitGitGadget</h2>
<div class="sectionbody">
<div class="paragraph">
<p>One option for sending patches is to follow a typical pull request workflow and
send your patches out via GitGitGadget. GitGitGadget is a tool created by
Johannes Schindelin to make life as a Git contributor easier for those used to
the GitHub PR workflow. It allows contributors to open pull requests against its
mirror of the Git project, and does some magic to turn the PR into a set of
emails and send them out for you. It also runs the Git continuous integration
suite for you. It&#8217;s documented at <a href="http://gitgitgadget.github.io" class="bare">http://gitgitgadget.github.io</a>.</p>
</div>
<div class="sect2">
<h3 id="create-fork"><a class="anchor" href="#create-fork"></a>Forking <code>git/git</code> on GitHub</h3>
<div class="paragraph">
<p>Before you can send your patch off to be reviewed using GitGitGadget, you will
need to fork the Git project and upload your changes. First thing - make sure
you have a GitHub account.</p>
</div>
<div class="paragraph">
<p>Head to the <a href="https://github.com/git/git">GitHub mirror</a> and look for the Fork
button. Place your fork wherever you deem appropriate and create it.</p>
</div>
</div>
<div class="sect2">
<h3 id="upload-to-fork"><a class="anchor" href="#upload-to-fork"></a>Uploading to Your Own Fork</h3>
<div class="paragraph">
<p>To upload your branch to your own fork, you&#8217;ll need to add the new fork as a
remote. You can use <code>git remote -v</code> to show the remotes you have added already.
From your new fork&#8217;s page on GitHub, you can press "Clone or download" to get
the URL; then you need to run the following to add, replacing your own URL and
remote name for the examples provided:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git remote <NAME_EMAIL>:remotename/git.git</pre>
</div>
</div>
<div class="paragraph">
<p>or to use the HTTPS URL:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git remote add remotename https://github.com/remotename/git/.git</pre>
</div>
</div>
<div class="paragraph">
<p>Run <code>git remote -v</code> again and you should see the new remote showing up.
<code>git fetch remotename</code> (with the real name of your remote replaced) in order to
get ready to push.</p>
</div>
<div class="paragraph">
<p>Next, double-check that you&#8217;ve been doing all your development in a new branch
by running <code>git branch</code>. If you didn&#8217;t, now is a good time to move your new
commits to their own branch.</p>
</div>
<div class="paragraph">
<p>As mentioned briefly at the beginning of this document, we are basing our work
on <code>master</code>, so go ahead and update as shown below, or using your preferred
workflow.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git checkout master
$ git pull -r
$ git rebase master psuh</pre>
</div>
</div>
<div class="paragraph">
<p>Finally, you&#8217;re ready to push your new topic branch! (Due to our branch and
command name choices, be careful when you type the command below.)</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git push remotename psuh</pre>
</div>
</div>
<div class="paragraph">
<p>Now you should be able to go and check out your newly created branch on GitHub.</p>
</div>
</div>
<div class="sect2">
<h3 id="send-pr-ggg"><a class="anchor" href="#send-pr-ggg"></a>Sending a PR to GitGitGadget</h3>
<div class="paragraph">
<p>In order to have your code tested and formatted for review, you need to start by
opening a Pull Request against <code>gitgitgadget/git</code>. Head to
<a href="https://github.com/gitgitgadget/git" class="bare">https://github.com/gitgitgadget/git</a> and open a PR either with the "New pull
request" button or the convenient "Compare &amp; pull request" button that may
appear with the name of your newly pushed branch.</p>
</div>
<div class="paragraph">
<p>Review the PR&#8217;s title and description, as it&#8217;s used by GitGitGadget as the cover
letter for your change. When you&#8217;re happy, submit your pull request.</p>
</div>
</div>
<div class="sect2">
<h3 id="run-ci-ggg"><a class="anchor" href="#run-ci-ggg"></a>Running CI and Getting Ready to Send</h3>
<div class="paragraph">
<p>If it&#8217;s your first time using GitGitGadget (which is likely, as you&#8217;re using
this tutorial) then someone will need to give you permission to use the tool.
As mentioned in the GitGitGadget documentation, you just need someone who
already uses it to comment on your PR with <code>/allow &lt;username&gt;</code>. GitGitGadget
will automatically run your PRs through the CI even without the permission given
but you will not be able to <code>/submit</code> your changes until someone allows you to
use the tool.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
You can typically find someone who can <code>/allow</code> you on GitGitGadget by
either examining recent pull requests where someone has been granted <code>/allow</code>
(<a href="https://github.com/gitgitgadget/git/pulls?utf8=%E2%9C%93&amp;q=is%3Apr+is%3Aopen+%22%2Fallow%22">Search:
is:pr is:open "/allow"</a>), in which case both the author and the person who
granted the <code>/allow</code> can now <code>/allow</code> you, or by inquiring on the
<a href="https://web.libera.chat/#git-devel">#git-devel</a> IRC channel on Libera Chat
linking your pull request and asking for someone to <code>/allow</code> you.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>If the CI fails, you can update your changes with <code>git rebase -i</code> and push your
branch again:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git push -f remotename psuh</pre>
</div>
</div>
<div class="paragraph">
<p>In fact, you should continue to make changes this way up until the point when
your patch is accepted into <code>next</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="send-mail-ggg"><a class="anchor" href="#send-mail-ggg"></a>Sending Your Patches</h3>
<div class="paragraph">
<p>Now that your CI is passing and someone has granted you permission to use
GitGitGadget with the <code>/allow</code> command, sending out for review is as simple as
commenting on your PR with <code>/submit</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="responding-ggg"><a class="anchor" href="#responding-ggg"></a>Updating With Comments</h3>
<div class="paragraph">
<p>Skip ahead to <a href="#reviewing">Responding to Reviews</a> for information on how to
reply to review comments you will receive on the mailing list.</p>
</div>
<div class="paragraph">
<p>Once you have your branch again in the shape you want following all review
comments, you can submit again:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git push -f remotename psuh</pre>
</div>
</div>
<div class="paragraph">
<p>Next, go look at your pull request against GitGitGadget; you should see the CI
has been kicked off again. Now while the CI is running is a good time for you
to modify your description at the top of the pull request thread; it will be
used again as the cover letter. You should use this space to describe what
has changed since your previous version, so that your reviewers have some idea
of what they&#8217;re looking at. When the CI is done running, you can comment once
more with <code>/submit</code> - GitGitGadget will automatically add a v2 mark to your
changes.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="howto-git-send-email"><a class="anchor" href="#howto-git-send-email"></a>Sending Patches with <code>git send-email</code></h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you don&#8217;t want to use GitGitGadget, you can also use Git itself to mail your
patches. Some benefits of using Git this way include finer grained control of
subject line (for example, being able to use the tag [RFC PATCH] in the subject)
and being able to send a &#8220;dry run&#8221; mail to yourself to ensure it all looks
good before going out to the list.</p>
</div>
<div class="sect2">
<h3 id="setup-git-send-email"><a class="anchor" href="#setup-git-send-email"></a>Prerequisite: Setting Up <code>git send-email</code></h3>
<div class="paragraph">
<p>Configuration for <code>send-email</code> can vary based on your operating system and email
provider, and so will not be covered in this tutorial, beyond stating that in
many distributions of Linux, <code>git-send-email</code> is not packaged alongside the
typical <code>git</code> install. You may need to install this additional package; there
are a number of resources online to help you do so. You will also need to
determine the right way to configure it to use your SMTP server; again, as this
configuration can change significantly based on your system and email setup, it
is out of scope for the context of this tutorial.</p>
</div>
</div>
<div class="sect2">
<h3 id="format-patch"><a class="anchor" href="#format-patch"></a>Preparing Initial Patchset</h3>
<div class="paragraph">
<p>Sending emails with Git is a two-part process; before you can prepare the emails
themselves, you&#8217;ll need to prepare the patches. Luckily, this is pretty simple:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git format-patch --cover-letter -o psuh/ --base=auto psuh@{u}..psuh</pre>
</div>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>The <code>--cover-letter</code> option tells <code>format-patch</code> to create a
cover letter template for you. You will need to fill in the
template before you&#8217;re ready to send - but for now, the template
will be next to your other patches.</p>
</li>
<li>
<p>The <code>-o psuh/</code> option tells <code>format-patch</code> to place the patch
files into a directory. This is useful because <code>git send-email</code>
can take a directory and send out all the patches from there.</p>
</li>
<li>
<p>The <code>--base=auto</code> option tells the command to record the "base
commit", on which the recipient is expected to apply the patch
series.  The <code>auto</code> value will cause <code>format-patch</code> to compute
the base commit automatically, which is the merge base of tip
commit of the remote-tracking branch and the specified revision
range.</p>
</li>
<li>
<p>The <code>psuh@{u}..psuh</code> option tells <code>format-patch</code> to generate
patches for the commits you created on the <code>psuh</code> branch since it
forked from its upstream (which is <code>origin/master</code> if you
followed the example in the "Set up your workspace" section).  If
you are already on the <code>psuh</code> branch, you can just say <code>@{u}</code>,
which means "commits on the current branch since it forked from
its upstream", which is the same thing.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>The command will make one patch file per commit. After you
run, you can go have a look at each of the patches with your favorite text
editor and make sure everything looks alright; however, it&#8217;s not recommended to
make code fixups via the patch file. It&#8217;s a better idea to make the change the
normal way using <code>git rebase -i</code> or by adding a new commit than by modifying a
patch.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
Optionally, you can also use the <code>--rfc</code> flag to prefix your patch subject
with &#8220;[RFC PATCH]&#8221; instead of &#8220;[PATCH]&#8221;. RFC stands for &#8220;request for
comments&#8221; and indicates that while your code isn&#8217;t quite ready for submission,
you&#8217;d like to begin the code review process. This can also be used when your
patch is a proposal, but you aren&#8217;t sure whether the community wants to solve
the problem with that approach or not - to conduct a sort of design review. You
may also see on the list patches marked &#8220;WIP&#8221; - this means they are incomplete
but want reviewers to look at what they have so far. You can add this flag with
<code>--subject-prefix=WIP</code>.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>Check and make sure that your patches and cover letter template exist in the
directory you specified - you&#8217;re nearly ready to send out your review!</p>
</div>
</div>
<div class="sect2">
<h3 id="cover-letter"><a class="anchor" href="#cover-letter"></a>Preparing Email</h3>
<div class="paragraph">
<p>In addition to an email per patch, the Git community also expects your patches
to come with a cover letter, typically with a subject line [PATCH 0/x] (where
x is the number of patches you&#8217;re sending). Since you invoked <code>format-patch</code>
with <code>--cover-letter</code>, you&#8217;ve already got a template ready. Open it up in your
favorite editor.</p>
</div>
<div class="paragraph">
<p>You should see a number of headers present already. Check that your <code>From:</code>
header is correct. Then modify your <code>Subject:</code> to something which succinctly
covers the purpose of your entire topic branch, for example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>Subject: [PATCH 0/7] adding the 'psuh' command</pre>
</div>
</div>
<div class="paragraph">
<p>Make sure you retain the &#8220;[PATCH 0/X]&#8221; part; that&#8217;s what indicates to the Git
community that this email is the beginning of a review, and many reviewers
filter their email for this type of flag.</p>
</div>
<div class="paragraph">
<p>You&#8217;ll need to add some extra parameters when you invoke <code>git send-email</code> to add
the cover letter.</p>
</div>
<div class="paragraph">
<p>Next you&#8217;ll have to fill out the body of your cover letter. This is an important
component of change submission as it explains to the community from a high level
what you&#8217;re trying to do, and why, in a way that&#8217;s more apparent than just
looking at your diff. Be sure to explain anything your diff doesn&#8217;t make clear
on its own.</p>
</div>
<div class="paragraph">
<p>Here&#8217;s an example body for <code>psuh</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>Our internal metrics indicate widespread interest in the command
git-psuh - that is, many users are trying to use it, but finding it is
unavailable, using some unknown workaround instead.

The following handful of patches add the psuh command and implement some
handy features on top of it.

This patchset is part of the MyFirstContribution tutorial and should not
be merged.</pre>
</div>
</div>
<div class="paragraph">
<p>The template created by <code>git format-patch --cover-letter</code> includes a diffstat.
This gives reviewers a summary of what they&#8217;re in for when reviewing your topic.
The one generated for <code>psuh</code> from the sample implementation looks like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre> Documentation/git-psuh.txt | 40 +++++++++++++++++++++
 Makefile                   |  1 +
 builtin.h                  |  1 +
 builtin/psuh.c             | 73 ++++++++++++++++++++++++++++++++++++++
 git.c                      |  1 +
 t/t9999-psuh-tutorial.sh   | 12 +++++++
 6 files changed, 128 insertions(+)
 create mode 100644 Documentation/git-psuh.txt
 create mode 100644 builtin/psuh.c
 create mode 100755 t/t9999-psuh-tutorial.sh</pre>
</div>
</div>
<div class="paragraph">
<p>Finally, the letter will include the version of Git used to generate the
patches. You can leave that string alone.</p>
</div>
</div>
<div class="sect2">
<h3 id="sending-git-send-email"><a class="anchor" href="#sending-git-send-email"></a>Sending Email</h3>
<div class="paragraph">
<p>At this point you should have a directory <code>psuh/</code> which is filled with your
patches and a cover letter. Time to mail it out! You can send it like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git send-email --to=<EMAIL> psuh/*.patch</pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
Check <code>git help send-email</code> for some other options which you may find
valuable, such as changing the Reply-to address or adding more CC and BCC lines.
</td>
</tr>
</table>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
When you are sending a real patch, it will go to <a href="mailto:*******************">*******************</a> - but
please don&#8217;t send your patchset from the tutorial to the real mailing list! For
now, you can send it to yourself, to make sure you understand how it will look.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>After you run the command above, you will be presented with an interactive
prompt for each patch that&#8217;s about to go out. This gives you one last chance to
edit or quit sending something (but again, don&#8217;t edit code this way). Once you
press <code>y</code> or <code>a</code> at these prompts your emails will be sent! Congratulations!</p>
</div>
<div class="paragraph">
<p>Awesome, now the community will drop everything and review your changes. (Just
kidding - be patient!)</p>
</div>
</div>
<div class="sect2">
<h3 id="v2-git-send-email"><a class="anchor" href="#v2-git-send-email"></a>Sending v2</h3>
<div class="paragraph">
<p>This section will focus on how to send a v2 of your patchset. To learn what
should go into v2, skip ahead to <a href="#reviewing">Responding to Reviews</a> for
information on how to handle comments from reviewers.</p>
</div>
<div class="paragraph">
<p>We&#8217;ll reuse our <code>psuh</code> topic branch for v2. Before we make any changes, we&#8217;ll
mark the tip of our v1 branch for easy reference:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git checkout psuh
$ git branch psuh-v1</pre>
</div>
</div>
<div class="paragraph">
<p>Refine your patch series by using <code>git rebase -i</code> to adjust commits based upon
reviewer comments. Once the patch series is ready for submission, generate your
patches again, but with some new flags:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git format-patch -v2 --cover-letter -o psuh/ --range-diff master..psuh-v1 master..</pre>
</div>
</div>
<div class="paragraph">
<p>The <code>--range-diff master..psuh-v1</code> parameter tells <code>format-patch</code> to include a
range-diff between <code>psuh-v1</code> and <code>psuh</code> in the cover letter (see
<a href="git-range-diff.html">git-range-diff(1)</a>). This helps tell reviewers about the differences
between your v1 and v2 patches.</p>
</div>
<div class="paragraph">
<p>The <code>-v2</code> parameter tells <code>format-patch</code> to output your patches
as version "2". For instance, you may notice that your v2 patches are
all named like <code>v2-000n-my-commit-subject.patch</code>. <code>-v2</code> will also format
your patches by prefixing them with "[PATCH v2]" instead of "[PATCH]",
and your range-diff will be prefaced with "Range-diff against v1".</p>
</div>
<div class="paragraph">
<p>Afer you run this command, <code>format-patch</code> will output the patches to the <code>psuh/</code>
directory, alongside the v1 patches. Using a single directory makes it easy to
refer to the old v1 patches while proofreading the v2 patches, but you will need
to be careful to send out only the v2 patches. We will use a pattern like
"psuh/v2-<strong>.patch" (not "psuh/</strong>.patch", which would match v1 and v2 patches).</p>
</div>
<div class="paragraph">
<p>Edit your cover letter again. Now is a good time to mention what&#8217;s different
between your last version and now, if it&#8217;s something significant. You do not
need the exact same body in your second cover letter; focus on explaining to
reviewers the changes you&#8217;ve made that may not be as visible.</p>
</div>
<div class="paragraph">
<p>You will also need to go and find the Message-Id of your previous cover letter.
You can either note it when you send the first series, from the output of <code>git
send-email</code>, or you can look it up on the
<a href="https://lore.kernel.org/git">mailing list</a>. Find your cover letter in the
archives, click on it, then click "permalink" or "raw" to reveal the Message-Id
header. It should match:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>Message-Id: &lt;<EMAIL>&gt;</pre>
</div>
</div>
<div class="paragraph">
<p>Your Message-Id is <code>&lt;<EMAIL>&gt;</code>. This example will be used
below as well; make sure to replace it with the correct Message-Id for your
<strong>previous cover letter</strong> - that is, if you&#8217;re sending v2, use the Message-Id
from v1; if you&#8217;re sending v3, use the Message-Id from v2.</p>
</div>
<div class="paragraph">
<p>While you&#8217;re looking at the email, you should also note who is CC&#8217;d, as it&#8217;s
common practice in the mailing list to keep all CCs on a thread. You can add
these CC lines directly to your cover letter with a line like so in the header
(before the Subject line):</p>
</div>
<div class="listingblock">
<div class="content">
<pre>CC: <EMAIL>, Othe R &lt;<EMAIL>&gt;</pre>
</div>
</div>
<div class="paragraph">
<p>Now send the emails again, paying close attention to which messages you pass in
to the command:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>$ git send-email --to=<EMAIL>
                 --in-reply-to="&lt;<EMAIL>&gt;"
                 psuh/v2-*.patch</pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="single-patch"><a class="anchor" href="#single-patch"></a>Bonus Chapter: One-Patch Changes</h3>
<div class="paragraph">
<p>In some cases, your very small change may consist of only one patch. When that
happens, you only need to send one email. Your commit message should already be
meaningful and explain at a high level the purpose (what is happening and why)
of your patch, but if you need to supply even more context, you can do so below
the <code>---</code> in your patch. Take the example below, which was generated with <code>git
format-patch</code> on a single commit, and then edited to add the content between
the <code>---</code> and the diffstat.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>From 1345bbb3f7ac74abde040c12e737204689a72723 Mon Sep 17 00:00:00 2001
From: A U Thor &lt;<EMAIL>&gt;
Date: Thu, 18 Apr 2019 15:11:02 -0700
Subject: [PATCH] README: change the grammar

I think it looks better this way. This part of the commit message will
end up in the commit-log.

Signed-off-by: A U Thor &lt;<EMAIL>&gt;
---
Let's have a wild discussion about grammar on the mailing list. This
part of my email will never end up in the commit log. Here is where I
can add additional context to the mailing list about my intent, outside
of the context of the commit log. This section was added after `git
format-patch` was run, by editing the patch file in a text editor.

 README.md | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/README.md b/README.md
index 88f126184c..38da593a60 100644
--- a/README.md
+++ b/README.md
@@ -3,7 +3,7 @@
 Git - fast, scalable, distributed revision control system
 =========================================================

-Git is a fast, scalable, distributed revision control system with an
+Git is a fast, scalable, and distributed revision control system with an
 unusually rich command set that provides both high-level operations
 and full access to internals.

--
2.21.0.392.gf8f6787159e-goog</pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="now-what"><a class="anchor" href="#now-what"></a>My Patch Got Emailed - Now What?</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="reviewing"><a class="anchor" href="#reviewing"></a>Responding to Reviews</h3>
<div class="paragraph">
<p>After a few days, you will hopefully receive a reply to your patchset with some
comments. Woohoo! Now you can get back to work.</p>
</div>
<div class="paragraph">
<p>It&#8217;s good manners to reply to each comment, notifying the reviewer that you have
made the change suggested, feel the original is better, or that the comment
inspired you to do something a new way which is superior to both the original
and the suggested change. This way reviewers don&#8217;t need to inspect your v2 to
figure out whether you implemented their comment or not.</p>
</div>
<div class="paragraph">
<p>Reviewers may ask you about what you wrote in the patchset, either in
the proposed commit log message or in the changes themselves.  You
should answer these questions in your response messages, but often the
reason why reviewers asked these questions to understand what you meant
to write is because your patchset needed clarification to be understood.</p>
</div>
<div class="paragraph">
<p>Do not be satisfied by just answering their questions in your response
and hear them say that they now understand what you wanted to say.
Update your patches to clarify the points reviewers had trouble with,
and prepare your v2; the words you used to explain your v1 to answer
reviewers' questions may be useful thing to use.  Your goal is to make
your v2 clear enough so that it becomes unnecessary for you to give the
same explanation to the next person who reads it.</p>
</div>
<div class="paragraph">
<p>If you are going to push back on a comment, be polite and explain why you feel
your original is better; be prepared that the reviewer may still disagree with
you, and the rest of the community may weigh in on one side or the other. As
with all code reviews, it&#8217;s important to keep an open mind to doing something a
different way than you originally planned; other reviewers have a different
perspective on the project than you do, and may be thinking of a valid side
effect which had not occurred to you. It is always okay to ask for clarification
if you aren&#8217;t sure why a change was suggested, or what the reviewer is asking
you to do.</p>
</div>
<div class="paragraph">
<p>Make sure your email client has a plaintext email mode and it is turned on; the
Git list rejects HTML email. Please also follow the mailing list etiquette
outlined in the
<a href="https://kernel.googlesource.com/pub/scm/git/git/+/todo/MaintNotes">Maintainer&#8217;s
Note</a>, which are similar to etiquette rules in most open source communities
surrounding bottom-posting and inline replies.</p>
</div>
<div class="paragraph">
<p>When you&#8217;re making changes to your code, it is cleanest - that is, the resulting
commits are easiest to look at - if you use <code>git rebase -i</code> (interactive
rebase). Take a look at this
<a href="https://www.oreilly.com/library/view/git-pocket-guide/9781449327507/ch10.html">overview</a>
from O&#8217;Reilly. The general idea is to modify each commit which requires changes;
this way, instead of having a patch A with a mistake, a patch B which was fine
and required no upstream reviews in v1, and a patch C which fixes patch A for
v2, you can just ship a v2 with a correct patch A and correct patch B. This is
changing history, but since it&#8217;s local history which you haven&#8217;t shared with
anyone, that is okay for now! (Later, it may not make sense to do this; take a
look at the section below this one for some context.)</p>
</div>
</div>
<div class="sect2">
<h3 id="after-approval"><a class="anchor" href="#after-approval"></a>After Review Approval</h3>
<div class="paragraph">
<p>The Git project has four integration branches: <code>seen</code>, <code>next</code>, <code>master</code>, and
<code>maint</code>. Your change will be placed into <code>seen</code> fairly early on by the maintainer
while it is still in the review process; from there, when it is ready for wider
testing, it will be merged into <code>next</code>. Plenty of early testers use <code>next</code> and
may report issues. Eventually, changes in <code>next</code> will make it to <code>master</code>,
which is typically considered stable. Finally, when a new release is cut,
<code>maint</code> is used to base bugfixes onto. As mentioned at the beginning of this
document, you can read <code>Documents/SubmittingPatches</code> for some more info about
the use of the various integration branches.</p>
</div>
<div class="paragraph">
<p>Back to now: your code has been lauded by the upstream reviewers. It is perfect.
It is ready to be accepted. You don&#8217;t need to do anything else; the maintainer
will merge your topic branch to <code>next</code> and life is good.</p>
</div>
<div class="paragraph">
<p>However, if you discover it isn&#8217;t so perfect after this point, you may need to
take some special steps depending on where you are in the process.</p>
</div>
<div class="paragraph">
<p>If the maintainer has announced in the "What&#8217;s cooking in git.git" email that
your topic is marked for <code>next</code> - that is, that they plan to merge it to <code>next</code>
but have not yet done so - you should send an email asking the maintainer to
wait a little longer: "I&#8217;ve sent v4 of my series and you marked it for <code>next</code>,
but I need to change this and that - please wait for v5 before you merge it."</p>
</div>
<div class="paragraph">
<p>If the topic has already been merged to <code>next</code>, rather than modifying your
patches with <code>git rebase -i</code>, you should make further changes incrementally -
that is, with another commit, based on top of the maintainer&#8217;s topic branch as
detailed in <a href="https://github.com/gitster/git" class="bare">https://github.com/gitster/git</a>. Your work is still in the same topic
but is now incremental, rather than a wholesale rewrite of the topic branch.</p>
</div>
<div class="paragraph">
<p>The topic branches in the maintainer&#8217;s GitHub are mirrored in GitGitGadget, so
if you&#8217;re sending your reviews out that way, you should be sure to open your PR
against the appropriate GitGitGadget/Git branch.</p>
</div>
<div class="paragraph">
<p>If you&#8217;re using <code>git send-email</code>, you can use it the same way as before, but you
should generate your diffs from <code>&lt;topic&gt;..&lt;mybranch&gt;</code> and base your work on
<code>&lt;topic&gt;</code> instead of <code>master</code>.</p>
</div>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2022-05-09 13:28:27 UTC
</div>
</div>
</body>
</html>