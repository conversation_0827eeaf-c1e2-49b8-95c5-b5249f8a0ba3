"""
animation library ui top level.
"""
from functools import partial

# Studio package module imports
from Qt import QtWidgets, QtCore, QtGui

# Tool package module imports
from lsr.anim_lib.ui.preview.controller import AnimBrowserController
from lsr.anim_lib.ui.editing.controller import AnimLibEditController
from lsr.anim_lib.ui.folder.controller import AnimLibFolderController
from lsr.anim_lib.ui.menu.mvc import AnimLibMenuController
from lsr.anim_lib.ui.bottom_status.mvc import AnimLibStatusController
from lsr.anim_lib.data import constant
from lsr.anim_lib.manager.signal import SignalManager
from lsr.anim_lib.manager.thread import ThreadManager

# sub ui
from lsr.anim_lib.ui.sub_ui.apply_setting_dialog import ApplySettingDialog
from lsr.anim_lib.ui.sub_ui.git_folder_pull_dialog import Git<PERSON>olderC<PERSON><PERSON><PERSON>ow


def get_scale_factor(*args, **kwargs):
    standard_dpi = 96.0
    scale_factor_x = QtWidgets.QApplication.desktop().logicalDpiX() / standard_dpi
    scale_factor_y = QtWidgets.QApplication.desktop().logicalDpiY() / standard_dpi
    return scale_factor_x, scale_factor_y


class AnimLibMainUI(QtWidgets.QWidget):

    def __init__(self, parent):
        super(AnimLibMainUI, self).__init__(parent)
        constant.UI_SCALE_FACTOR = get_scale_factor()
        self._init_layout()
        self._customized_view()
        self._do_signal_slot_connection()
        self._init_sub_ui()

    def _init_layout(self, *args, **kwargs):
        self.center_section_preview = AnimBrowserController(self)
        self.top_section_menu = AnimLibMenuController(self)
        self.left_section_folder = AnimLibFolderController(self)
        self.right_section_edit = AnimLibEditController(self)
        self.bottom_section_status = AnimLibStatusController(self)

        self.splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal, )
        self.splitter.addWidget(self.left_section_folder)
        self.splitter.addWidget(self.center_section_preview)
        self.splitter.addWidget(self.right_section_edit)
        self.splitter.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        splitter_00 = QtWidgets.QSplitter(QtCore.Qt.Horizontal, )
        splitter_00.addWidget(self.bottom_section_status)

        # layout
        main_layout = QtWidgets.QVBoxLayout()
        main_layout.setContentsMargins(5, 0, 5, 0)
        main_layout.setSpacing(0)

        menu_layout = QtWidgets.QVBoxLayout()
        menu_layout.setContentsMargins(0, 0, 0, 0)
        menu_layout.addWidget(self.top_section_menu)

        work_layout = QtWidgets.QVBoxLayout()
        work_layout.setContentsMargins(0, 0, 0, 0)
        work_layout.addWidget(self.splitter)

        bottom_layout = QtWidgets.QHBoxLayout()
        bottom_layout.setContentsMargins(0, 0, 0, 0)
        bottom_layout.addWidget(self.bottom_section_status)

        main_layout.addLayout(menu_layout)
#        main_layout.addWidget(divier_00)
        main_layout.addLayout(work_layout)
        main_layout.addLayout(bottom_layout)

        self.setLayout(main_layout)

    def _do_signal_slot_connection(self, *args, **kwargs):
        SignalManager.lock_ui.connect(partial(self.lock_ui))
        SignalManager.unlock_ui.connect(partial(self.unlock_ui))
        self.splitter.splitterMoved.connect(partial(self.main_splitter_move))

    def main_splitter_move(self, *args, **kwargs):
        current_value = self.splitter.sizes()[2]
        self.right_section_edit.view.splitter.moveSplitter(current_value, 1)

    def _customized_view(self, *args, **kwargs):
        self.center_section_preview.resize(90000, 90000)  # make sure center widget take most of the space
        self.setMouseTracking(True)
        self.setFocus()

    def _init_sub_ui(self, *args, **kwargs):
        # cache not modal sub window
        self._sub_ui = dict()
        self._sub_ui["ApplySettingDialog"] = ApplySettingDialog(constant.INSTANCE_WINDOW_ROOT)
        self._sub_ui["GitFolderCreateWindow"] = GitFolderCreateWindow(constant.INSTANCE_WINDOW_ROOT)
        constant.INSTANCE_SUBUI_APPLY_SETTING = self._sub_ui["ApplySettingDialog"]
        constant.INSTANCE_SUBUI_GIT_CLONE_DAILOG = self._sub_ui["GitFolderCreateWindow"]

    def lock_ui(self, *args, **kwargs):
        self.setEnabled(False)
        parent_rect = self.rect()
        self.mask_window = QtWidgets.QWidget()
        self.mask_window.setStyleSheet("background-color: rgba(0, 0, 0, 40%);")
        self.mask_window.setGeometry(parent_rect)
        self.mask_window.setParent(self)
        parent_geo = self.mask_window.parent().geometry()
        self.mask_window.move(parent_geo.center().x() - self.mask_window.width() / 2.0, parent_geo.center().y() - self.mask_window.height() / 2.0)
        self.mask_window.show()

        for subui in [x for x in dir(constant) if x.startswith("INSTANCE_SUBUI")]:
            try:
                eval("constant.%s.setEnabled(False)"%subui)
            except Exception:
                pass

        # pause thread
        ThreadManager.pause()

    def unlock_ui(self, *args, **kwargs):
        self.setEnabled(True)
        self.mask_window.hide()

        for subui in [x for x in dir(constant) if x.startswith("INSTANCE_SUBUI")]:
            try:
                eval("constant.%s.setEnabled(True)"%subui)
            except Exception:
                pass

        # resume thread
        ThreadManager.resume()