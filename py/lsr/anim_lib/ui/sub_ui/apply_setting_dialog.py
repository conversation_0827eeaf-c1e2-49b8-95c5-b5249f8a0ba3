"""
Anim lib setting ui
"""
import os
import json
from collections import OrderedDict

# Studio package module imports
from Qt import QtWidgets, QtCore, QtGui
from functools import partial

# Tool package module imports
from lsr.anim_lib.ui.sub_ui.base import AnimLibBaseSubUI
from lsr.anim_lib.manager.signal import SignalManager
from lsr.anim_lib.data import constant
from lsr.anim_lib.utility.pyside import copy_maya_font_style, resolve_icon_path
from lsr.anim_lib.utility import pyside as uti_pyside


first_column_width = 100


class RadioLikeCheckBox(QtWidgets.QCheckBox):
    def mousePressEvent(self, event):
        if self.isChecked():
            event.ignore()
        else:
            SignalManager.setting_radio_option_changed.emit(self)
            super().mousePressEvent(event)


class AnimLibSettingSectionBase(QtWidgets.QWidget):
    _regular_checkbox = False

    def __init__(self, *args, **kwargs):
        super(AnimLibSettingSectionBase, self).__init__(*args, **kwargs)
        self._init_layout()
        self.customized_view()
        self.do_signal_slot_connection()
        self.toggle_groupbox_visibility()

    def _init_layout(self, *args, **kwargs):
        self.section_title_label = QtWidgets.QLabel("Section Title")
        self.section_enable_label = QtWidgets.QLabel("Section Label")
        if self._regular_checkbox:
            self.section_enable_cbox = QtWidgets.QCheckBox()
        else:
            self.section_enable_cbox = RadioLikeCheckBox()
        self.section_grpbox = QtWidgets.QGroupBox("")
        self.root_grpbox = QtWidgets.QGroupBox("")

        section_enable_layout = QtWidgets.QGridLayout()
        section_enable_layout.setContentsMargins(0, 0, 0, 0)
        section_enable_layout.addWidget(self.section_enable_label, 0, 0, QtCore.Qt.AlignLeft)
        section_enable_layout.addWidget(self.section_enable_cbox, 0, 39, QtCore.Qt.AlignRight)

        self.group_layout = QtWidgets.QGridLayout()
        self.section_grpbox.setLayout(self.group_layout)

        main_layout = QtWidgets.QVBoxLayout()
        main_layout.addLayout(section_enable_layout)
        main_layout.addWidget(self.section_grpbox)

        root_layout = QtWidgets.QHBoxLayout()
        root_layout.addWidget(self.root_grpbox)
        self.root_grpbox.setLayout(main_layout)
        self.setLayout(root_layout)

    def customized_view(self, *args, **kwargs):
        if not self.section_grpbox.findChildren(QtWidgets.QWidget):
            self.section_grpbox.setHidden(True)
        self.root_grpbox.setTitle(self.section_title_label.text())
        copy_maya_font_style(self, size=9)

    def do_signal_slot_connection(self, *args, **kwargs):
        if isinstance(self.section_enable_cbox, RadioLikeCheckBox):
            self.section_enable_cbox.stateChanged.connect(partial(self.toggle_groupbox_visibility))
        else:
            self.section_enable_cbox.clicked.connect(partial(self.toggle_groupbox_visibility))

    def toggle_groupbox_visibility(self, *args, **kwargs):
        if self.section_enable_cbox.isChecked():
            self.section_grpbox.setEnabled(True)
            self.section_enable_label.setEnabled(True)
        else:
            self.section_grpbox.setEnabled(False)
            self.section_enable_label.setEnabled(False)


class AnimLibSettingSectionFilter(AnimLibSettingSectionBase):
    _regular_checkbox = True

    def __init__(self, *args, **kwargs):
        super(AnimLibSettingSectionFilter, self).__init__(*args, **kwargs)

    def _init_layout(self, *args, **kwargs):
        super(AnimLibSettingSectionFilter, self)._init_layout()

        # widget
        self.section_title_label.setText("Apply Filter")
        self.section_enable_label.setText("Enable Apply Filter : ")
        self.filter_method_combo = QtWidgets.QComboBox()

        self.filter_method_combo.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        self.filter_method_combo.addItems(["Current Selection"])

        # layout
        self.group_layout.addWidget(QtWidgets.QLabel("Filter Method : "), 1, 0)
        self.group_layout.addWidget(self.filter_method_combo, 1, 38, QtCore.Qt.AlignRight)


class AnimLibSettingSectionCtrl(AnimLibSettingSectionBase):
    def __init__(self):
        super(AnimLibSettingSectionCtrl, self).__init__()

    def _init_layout(self, *args, **kwargs):
        super(AnimLibSettingSectionCtrl, self)._init_layout()

        # widget
        self.section_title_label.setText("Controller")
        self.section_enable_label.setText("Use Object Name : ")


class AnimLibSettingSectionHIK(AnimLibSettingSectionBase):
    def __init__(self):
        super(AnimLibSettingSectionHIK, self).__init__()

    def _init_layout(self, *args, **kwargs):
        super(AnimLibSettingSectionHIK, self)._init_layout()

        # widget
        self.section_title_label.setText("HumanIK")
        self.section_enable_label.setText("Use HIK Retarget : ")


class AnimLibSettingSectionConfigPath(object):
    _FILE_FILTER = "Json Files (*.json)"

    def __init__(
            self, pixmap, label, grid, row=0,
            default=None, placeholder=None, *args, **kwargs):
        super(AnimLibSettingSectionConfigPath, self).__init__(*args, **kwargs)
        self.recent_files = []

        self.pixmap = QtWidgets.QLabel()
        self.pixmap.setPixmap(pixmap)
        grid.addWidget(self.pixmap, row, 0, 1, 1)
        self.label = QtWidgets.QLabel()
        self.label.setText(label + ':')
        grid.addWidget(self.label, row, 1, 1, 1)
        grid.setAlignment(self.pixmap, QtCore.Qt.AlignCenter)
        grid.setAlignment(self.label, QtCore.Qt.AlignRight)

        self.combo_path = QtWidgets.QComboBox()
        self.combo_path.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        grid.addWidget(self.combo_path, row, 2, 1, 1)

        self.btn_browse = QtWidgets.QPushButton()
        icon_path = resolve_icon_path(sub_dir="/library/folder.png")
        self.btn_browse.setIcon(QtGui.QIcon(icon_path))
        self.btn_browse.setToolTip('Pick a file in File Explorer.')
        grid.addWidget(self.btn_browse, row, 3, 1, 1)
        self.btn_browse.clicked.connect(self.browse)

    @QtCore.Slot()
    def browse(self, *args, **kwargs):
        """ Opens a file browser to let the user pick a path. """
        path = os.path.split(self.combo_path.currentText())[0]
        if os.path.isfile(path):
            path = os.path.split(path)[0]

        result = QtWidgets.QFileDialog.getOpenFileName(
            self.combo_path, 'Pick a ' + self.label.text(),
            path, self._FILE_FILTER)[0]
        if result:
            self.add_recent_file(result)

    def add_recent_file(self, file_path, *args, **kwargs):
        file_path = os.path.normpath(file_path).replace("\\", "/")
        if file_path in self.recent_files:
            self.recent_files.remove(file_path)
        self.recent_files.insert(0, file_path)
        if len(self.recent_files) > 10:
            self.recent_files.pop()
        self.combo_path.clear()
        self.combo_path.addItems(self.recent_files)
        self.combo_path.setCurrentText(file_path)


class AnimLibSettingSectionFBXConstraint(AnimLibSettingSectionBase):
    def __init__(self, *args, **kwargs):
        super(AnimLibSettingSectionFBXConstraint, self).__init__(*args, **kwargs)
        self.setting_items = OrderedDict()
        self.recent_files = []

    def _init_layout(self, *args, **kwargs):
        super(AnimLibSettingSectionFBXConstraint, self)._init_layout()

        # widget
        self.section_title_label.setText("One To One (FBX)")
        self.section_enable_label.setText("Use One To One Constraint : ")
        icon_path = resolve_icon_path(sub_dir="/library/gear_write.png")
        self.line_path = AnimLibSettingSectionConfigPath(
            QtGui.QPixmap(icon_path),
            "Config File",
            self.group_layout,
            0,
            placeholder="Retarget config file path."
        )

    def load_curent_path(self, *args, **kwargs):
        current_path = kwargs.get("current_path", None)
        if current_path:
            file_path = os.path.normpath(current_path).replace("\\", "/")
            self.line_path.combo_path.setCurrentText(file_path)

    def load_recent_path(self, *args, **kwargs):
        recent_files = kwargs.get("recent_files", None)
        if recent_files:
            files = []
            for file in recent_files:
                files.append(os.path.normpath(file).replace("\\", "/"))
                self.line_path.add_recent_file(file)
            self.line_path.recent_files = files


class AnimLibLanguageWidget(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super(AnimLibLanguageWidget, self).__init__(parent)
        self._init_layout()
        self._customized_view()
        self._signal_connect()

    def _init_layout(self, *args, **kwargs):
        self.language_label = QtWidgets.QLabel("Language : ", self)
        self.language_combobox = QtWidgets.QComboBox(self)
        for language_text, language_var in constant.INSTANCE_UI_TRANSLATOR.language_category.items():
            self.language_combobox.addItem(language_text, userData=language_var)
        main_lay = QtWidgets.QGridLayout(self)
        main_lay.setColumnMinimumWidth(0, 60 * constant.UI_SCALE_FACTOR[0])
        self.setLayout(main_lay)

        main_lay.addWidget(self.language_label, 0, 0)
        main_lay.addWidget(self.language_combobox, 0, 1)

    def _customized_view(self, *args, **kwargs):
        pass

    def _signal_connect(self, *args, **kwargs):
        self.language_combobox.currentIndexChanged.connect(partial(self.current_language_change))

    def current_language_change(self, *args, **kwargs):
        constant.CONFIG_GLOBAL_LANGUAGE = self.language_combobox.currentData()
        SignalManager.language_changed.emit()


class ApplySettingDialog(AnimLibBaseSubUI):
    """ Apply Setting Dialog """

    def __init__(self, parent):
        super(ApplySettingDialog, self).__init__(parent)
        self._init_layout()
        self._customized_view()
        self._do_signal_slot_connection()
        self.setStyleSheet("QDialog::selected{background-color: #2b2b2b;}")
        self.setFocus()

    def _init_layout(self, *args, **kwargs):
        super(ApplySettingDialog, self)._init_layout()
        self.setWindowTitle("Apply Setting")

        # create widget
        self.section_ctrl = AnimLibSettingSectionCtrl()
        self.section_hik = AnimLibSettingSectionHIK()
        self.section_fbx = AnimLibSettingSectionFBXConstraint()
        self.section_filter = AnimLibSettingSectionFilter()
        self.section_language = AnimLibLanguageWidget()

        # ok cancel btn
        self.button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Cancel | QtWidgets.QDialogButtonBox.Ok)

        # layout
        main_layout = QtWidgets.QGridLayout(self)
        main_layout.addWidget(self.section_ctrl, 1, 0)
        main_layout.addWidget(self.section_hik, 2, 0)
        main_layout.addWidget(self.section_fbx, 3, 0)
        main_layout.addWidget(self.section_filter, 4, 0)
        main_layout.addWidget(self.section_language, 5, 0)
        main_layout.addWidget(self.button_box, 7, 0, 7, 0)
        self.setLayout(main_layout)

    def _customized_view(self, *args, **kwargs):
        super(ApplySettingDialog, self)._customized_view()
        self.resize(400 * constant.UI_SCALE_FACTOR[0], 500)

    def _do_signal_slot_connection(self, *args, **kwargs):
        super(ApplySettingDialog, self)._do_signal_slot_connection()
        self.button_box.accepted.connect(partial(self.slot_apply))
        self.button_box.rejected.connect(partial(self.hide))
        SignalManager.sub_ui_apply_setting_dialog_launched.connect(partial(self.slot_show))
        SignalManager.setting_radio_option_changed.connect(partial(self.toggle_ratio_options))

    def toggle_ratio_options(self, *args, **kwargs):
        sender = args[0]
        for section in [self.section_ctrl, self.section_hik, self.section_fbx]:
            if sender != section.section_enable_cbox:
                section.section_grpbox.setEnabled(False)
                section.section_enable_label.setEnabled(False)
                section.section_enable_cbox.setChecked(False)

    def slot_apply(self, *args, **kwargs):
        self.hide()

    def collcet_ui_input(self, *args, **kwargs):
        config_path = self.section_fbx.line_path.combo_path.currentText()
        config_data = {}
        ui_data = {
            "use_ctrl": self.section_ctrl.section_enable_cbox.isChecked(),
            "use_hik": self.section_hik.section_enable_cbox.isChecked(),
            "use_fbx": self.section_fbx.section_enable_cbox.isChecked(),
            "recent_config": self.section_fbx.line_path.recent_files,
            "filter_enable": self.section_filter.section_enable_cbox.isChecked(),
            "filter_method": self.section_filter.filter_method_combo.currentIndex(),
            "one_to_one_config": config_path,
        }
        constant.INSTANCE_UI_INPUT_COLLECTION.update(ui_data)

        if os.path.isfile(config_path):
            with open(config_path, "r") as file_obj:
                config_data = json.load(file_obj)
        constant.INSTANCE_UI_INPUT_COLLECTION["one_to_one_config"] = config_data

        return ui_data

    def save_settings(self, *args, **kwargs):
        settings = constant.INSTANCE_WINDOW_ROOT._qsettings
        settings.beginGroup('tool_setting')
        ui_data = self.collcet_ui_input()
        for key, value in ui_data.items():
            settings.setValue(key, value)
        settings.setValue('language', self.section_language.language_combobox.currentIndex())
        settings.setValue('global_var_language', constant.CONFIG_GLOBAL_LANGUAGE)
        settings.endGroup()
        settings.sync()
        return settings

    def load_settings(self, *args, **kwargs):
        settings = constant.INSTANCE_WINDOW_ROOT._qsettings
        settings.beginGroup('tool_setting')

        use_ctrl = uti_pyside.safe_load_bool_qsetting(key='use_ctrl', settings=settings)
        use_hik = uti_pyside.safe_load_bool_qsetting(key='use_hik', settings=settings)
        use_fbx = uti_pyside.safe_load_bool_qsetting(key='use_fbx', settings=settings)
        filter_enable = uti_pyside.safe_load_bool_qsetting(key='filter_enable', settings=settings)
        filter_method = uti_pyside.safe_load_int_qsetting(key='filter_method', settings=settings)
        language_method = uti_pyside.safe_load_int_qsetting(key='language_method', settings=settings)
        global_language_var = uti_pyside.safe_load_str_qsetting(key='global_var_language', settings=settings)
        global_language_var = "en_US" if not global_language_var else global_language_var
        current_config = uti_pyside.safe_load_str_qsetting(key='one_to_one_config', settings=settings)
        recent_files = uti_pyside.safe_load_list_qsetting(key='recent_config', settings=settings)

        if not any([use_ctrl, use_hik, use_fbx]):
            use_ctrl = True

        self.section_ctrl.section_enable_cbox.setChecked(use_ctrl)
        self.section_hik.section_enable_cbox.setChecked(use_hik)
        self.section_fbx.section_enable_cbox.setChecked(use_fbx)
        self.section_fbx.load_recent_path(recent_files=recent_files)
        self.section_fbx.load_curent_path(current_path=current_config)
        self.section_filter.section_enable_cbox.setChecked(filter_enable)
        self.section_filter.toggle_groupbox_visibility()
        self.section_filter.filter_method_combo.setCurrentIndex(filter_method)
        self.section_language.language_combobox.setCurrentIndex(language_method)
        constant.CONFIG_GLOBAL_LANGUAGE = global_language_var
        constant.INSTANCE_UI_TRANSLATOR.translate_widget(constant.INSTANCE_WINDOW_ROOT)
        settings.endGroup()

        return settings


