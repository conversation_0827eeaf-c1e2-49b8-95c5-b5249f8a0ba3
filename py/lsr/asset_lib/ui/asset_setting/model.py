"""
    This file contains the asset info model class.
"""


from Qt import QtCore
from lsr.asset_lib.data import var_global


class AssetLibTableModel(QtCore.QAbstractTableModel):
    def __init__(self, data, *args, **kwargs):
        super(AssetLibTableModel, self).__init__()
        self._data = data

    def rowCount(self, parent=QtCore.QModelIndex(), *args, **kwargs):
        if self._data:
            return len(self._data)
        return 0

    def columnCount(self, parent=QtCore.QModelIndex(), *args, **kwargs):
        return 2

    def data(self, index, role=QtCore.Qt.DisplayRole, *args, **kwargs):
        if role == QtCore.Qt.DisplayRole:
            row = index.row()
            col = index.column()
            key = list(self._data.keys())[row]
            value = list(self._data.values())[row]
            if col == 0:
                return key
            elif col == 1:
                return value

    def refresh_data(self):
        if var_global.ASSETS_CURRENT_IO_DATA:
            data = var_global.ASSETS_CURRENT_IO_DATA['asset_info']
        else:
            data = var_global.ASSETS_INIT_INFO
        self.beginResetModel()
        self._data = data
        self.endResetModel()
