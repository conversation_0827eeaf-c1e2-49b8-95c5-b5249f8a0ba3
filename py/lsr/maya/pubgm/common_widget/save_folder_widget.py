import os

from maya import cmds
from lsr.maya.pubgm.common_widget.config import Config
from lsr.qt.core import QtGui, QtCore, QtWidgets
import lsr.qt.core.utils as qtutil

from lsr.maya.nodezoo.constant import SurfaceAssociation
import lsr.maya.rig.workarea as wa
import lsr.maya.rig.data as dutil
import lsr.maya.rig.rig_global as rg

_LAST_PATH = None


class SaveFolder(QtWidgets.QWidget, Config):
    def __init__(self, *args, **kwargs):
        # super(SaveFolder, self).__init__(*args, **kwargs)
        Config.__init__(self, *args, **kwargs)
        QtWidgets.QWidget.__init__(self, *args, **kwargs)

        central_layout = QtWidgets.QVBoxLayout()
        self.setLayout(central_layout)

        widget = QtWidgets.QGroupBox('保存路径:')

        central_layout.addWidget(widget)
        grid = QtWidgets.QGridLayout()
        widget.setLayout(grid)

        grid.addWidget(QtWidgets.QLabel('位置选项: '), 0, 0, 1, 1)
        hbox = QtWidgets.QHBoxLayout()
        grid.addLayout(hbox, 0, 1, 1, 1)
        self.rb_local = QtWidgets.QRadioButton('项目')
        hbox.addWidget(self.rb_local)
        self.rb_world = QtWidgets.QRadioButton('桌面')
        hbox.addWidget(self.rb_world)
        self.rb_world = QtWidgets.QRadioButton('当前文件夹')
        hbox.addWidget(self.rb_world)
        self.rb_world = QtWidgets.QRadioButton('自定义')
        hbox.addWidget(self.rb_world)
        hbox.addStretch(10)

        grid.addWidget(QtWidgets.QLabel('保存路径: '), 1, 0, 1, 1)
        self.le_workarea = QtWidgets.QLineEdit()
        grid.addWidget(self.le_workarea, 1, 1, 1, 1)
        self.btn_browse = QtWidgets.QPushButton()
        self.btn_browse.setIcon(qtutil.get_icon(':/openLoadGeneric.png'))
        grid.addWidget(self.btn_browse, 1, 2, 1, 1)

        self.btn_browse = QtWidgets.QPushButton()
        self.btn_browse.setIcon(qtutil.get_icon(':/openLoadGeneric.png'))
        grid.addWidget(self.btn_browse, 1, 3, 1, 1)

        self.add_widget(self.le_workarea)

    def load_settings(self):
        pass

    def save_settings(self):
        pass
