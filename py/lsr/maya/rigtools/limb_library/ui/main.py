import os
import ast
from functools import partial

import maya.cmds as cmds
import maya.OpenMaya as OpenMaya

from lsr.maya.nodezoo.node import Node
from lsr.maya.standard.name import NodeName
import lsr.maya.rigtools.limb_library.paths as paths_util
from lsr.qt.core import QtCore, QtWidgets
from lsr.qt.icon_lib.api import get_icon
from lsr.maya.rigtools.limb_library.constants import SHELVE_ICON_SIZE
from lsr.protostar.lib import ActionLibrary as alib
from lsr.qt.core.base_main_window import get_window_class
from lsr.qt.core.layout.flow_layout import FlowLayout
from lsr.maya.rigtools.limb_library.data import utils
from lsr.maya.rigtools.limb_library.data import parse_graph
from lsr.maya.rigtools.limb_library.ui import build_setting_window
from lsr.maya.rigtools.limb_library.ui import limb_parameter_window
from lsr.maya.rigtools.limb_library.build_core.rig_re_builder import RigReBuilder

# get the base main window class
base_class = get_window_class(app_name='LSR-Maya Rigging Limb Library')


def execute_script(item, *args, **kwargs):
    """
    execute script
    Args:
        item (dict): json file content

    Returns:
        None
    """
    limb_type = item["type"]
    limb_data_path = item["data"]
    child_limbs = item['children']
    if limb_type == 'template':
        import_template(file_path=limb_data_path)
    else:
        limb_parameter_window.LimbParameterUI.launch(
            action_type=limb_type,
            limb_data_path=limb_data_path,
            child_limbs=child_limbs)


def import_template(file_path=None, *args, **kwargs):
    """
    Import template files in json format
    Args:
        file_path (str): limb data path

    Returns:
        None
    """
    if not file_path:
        file_path = QtWidgets.QFileDialog.getOpenFileName(
                    None,  "Template Format", MainUI.default_export_path, "JSON (*.json)")[0]
        if not file_path:
            OpenMaya.MGlobal.displayWarning(
                'No template files selected were imported.')
            return

    joint_temp = utils.Generate_JointTemplate(file_path=file_path)
    joint_temp.create()
    MainUI.default_export_path = os.path.dirname(file_path)
    OpenMaya.MGlobal.displayInfo(
        'template files: {} was imported.'.format(file_path))


class MainUI(base_class):
    """
    shelf_util main UI class
    """
    _REUSE_SINGLETON = False
    UI_NAME = "Rigging_Limb_Library"
    TRANSFER_DEFORM = True
    MIRROR_BY_PARAM = False
    MIRROR_DIRECTION = NodeName.SIDE_R
    default_export_path = None

    def __init__(self):
        """ Creates and initializes this window. """
        self.tab_widget = QtWidgets.QTabWidget()
        self.__thread = None
        self.__cur_progress_max = 0
        self.__save_path = None

        super(MainUI, self).__init__()

    def save_settings(self):
        """Updates the app settings and saves it to disk.

        Returns:
            QSettings: The settings object.
        """
        settings = super(MainUI, self).save_settings()
        settings.beginGroup('io_setting')
        settings.setValue('out_path', MainUI.default_export_path or "")
        settings.endGroup()
        settings.sync()
        return settings

    def load_settings(self):
        """Loads the app settings.

        Returns:
            QSettings: The settings object.
        """
        settings = super(MainUI, self).load_settings()

        settings.beginGroup('io_setting')
        MainUI.default_export_path = settings.value('out_path', '')
        settings.endGroup()
        return settings

    def setup_ui(self):
        """
        override setup_ui

        Returns:
            None
        """
        self.setObjectName('Rigging_Limb_Library')

        alib.refresh()
        self.create_menu_bar()
        self.create_tool_bar()

        main_splitter = QtWidgets.QSplitter(QtCore.Qt.Vertical)
        self.setCentralWidget(main_splitter)
        w = QtWidgets.QWidget()

        vbox = QtWidgets.QVBoxLayout()

        w.setLayout(vbox)
        main_splitter.addWidget(w)
        vbox.setSpacing(6)
        vbox.setContentsMargins(5, 5, 5, 5)

        vbox.addWidget(self.tab_widget)

        paths = paths_util.get_lib_paths()
        limbs = paths_util.resolve_paths(paths)
        data = paths_util.resolve_limbs(limbs)
        self.create_tabs(data)
        self.tab_widget.resize(600, 600)
        self.resize(700, 700)
        self.tab_widget.setCurrentIndex(self.get_tab_index())
        main_splitter.setSizes([550, 550])

        # Create a log dock widget
        # self.log_widget = LSR_LogWidget()

        self.create_connections()

    @classmethod
    def launch(cls, *args, **kwargs):
        """Override the launch method of the parent class"""
        instance = super(MainUI, cls).launch(*args, **kwargs)
        return instance

    def create_menu_bar(self, *args, **kwargs):

        # =========================================================
        # Actions
        # =========================================================

        revert_temp_action = QtWidgets.QAction("Revert Rig Template", self)
        revert_temp_action.setStatusTip("Revert Rig Template by Rig Node")
        revert_temp_action.triggered.connect(partial(self.revert_template))

        update_temp_action = QtWidgets.QAction("Save Rig Template", self)
        update_temp_action.setStatusTip("Save Rig Template to Rig Node")
        update_temp_action.triggered.connect(partial(self.update_template))

        deform_action = QtWidgets.QAction("Transfer Deform", self)
        deform_action.setStatusTip("If Transfer Deform?")
        deform_action.setCheckable(True)
        deform_action.setChecked(True)
        deform_action.triggered.connect(partial(self.deform_action_trigger))

        attr_mirror_action = QtWidgets.QAction("By \'mirror\' param", self)
        attr_mirror_action.setStatusTip(
            "only mirror the limb that activates the mirror parameter")
        attr_mirror_action.setCheckable(True)
        attr_mirror_action.setChecked(False)
        attr_mirror_action.toggled.connect(partial(self.attr_mirror_action_trigger))

        self.mirror_to_R_action = QtWidgets.QAction("From Left to Right", self)
        self.mirror_to_R_action.setCheckable(True)
        self.mirror_to_R_action.setChecked(True)
        self.mirror_to_R_action.toggled.connect(partial(self.mirror_to_R_action_trigger))

        self.mirror_to_L_action = QtWidgets.QAction("From Right to Left", self)
        self.mirror_to_L_action.setCheckable(True)
        self.mirror_to_L_action.setChecked(False)
        self.mirror_to_L_action.toggled.connect(partial(self.mirror_to_L_action_trigger))

        """ Creates the menu bar. """
        bar = self.menuBar()
        project_menu = bar.addMenu('Project')
        temp_menu = bar.addMenu('Template')
        temp_menu.addAction(revert_temp_action)
        temp_menu.addAction(update_temp_action)

        bo_menu = bar.addMenu('Build Option')
        bo_menu.addAction(deform_action)

        mirror_menu = bar.addMenu('Mirror Option')
        mirror_menu.addAction(attr_mirror_action)
        mirror_menu.addAction(self.mirror_to_R_action)
        mirror_menu.addAction(self.mirror_to_L_action)

        attr_editor_action = QtWidgets.QAction("Help", self)
        bar.addAction(attr_editor_action)

    def create_tool_bar(self, *args, **kwargs):
        icon = get_icon(
            sub_dir='/library/update.png',
            color=(178, 233, 235))
        update_action = QtWidgets.QAction(icon, "Update Limb Parameters", self)
        update_action.setToolTip("Update Limb Parameters")

        icon = get_icon(
            sub_dir='/library/export2.png',
            color=(38, 255, 204))
        export_action = QtWidgets.QAction(icon, "Export Limb Parameters", self)
        export_action.setToolTip("Export Limb")

        icon = get_icon(
            sub_dir='/library/skeleton.png',
            color=(238, 255, 24))
        export_temp_action = QtWidgets.QAction(icon, "Export Scene Template", self)
        export_temp_action.setToolTip("Export Scene Template")

        icon = get_icon(
            sub_dir='/library/import2.png',
            color=(238, 255, 24))
        import_temp_action = QtWidgets.QAction(icon, "Import Scene Template", self)
        import_temp_action.setToolTip("import Scene Template")

        icon = get_icon(
            sub_dir='/library/script_editor.png',
            color=(38, 204, 255))
        editor_action = QtWidgets.QAction(icon, "Open Limb Editor", self)
        editor_action.setToolTip("Open Limb Editor")

        icon = get_icon(
            sub_dir='/library/refresh.png',
            color=(126, 181, 222))
        refresh_action = QtWidgets.QAction(icon, "Refresh", self)
        refresh_action.setToolTip("Refresh the protostar library")

        icon = get_icon(
            sub_dir='/library/reflect_horizontal.png',
            color=(178, 233, 235))
        mirror_action = QtWidgets.QAction(icon, "Mirror", self)
        mirror_action.setToolTip("Mirror All Limbs")

        icon = get_icon(
            sub_dir='/library/play_2.png',
            color=[88, 255, 172])
        execute_all_action = QtWidgets.QAction(icon, "ExeAll", self)
        execute_all_action.setToolTip("Execute the all Limbs")

        icon = get_icon(
            sub_dir='/library/play.png',
            color=[188, 255, 172])
        execute_sel_action = QtWidgets.QAction(icon, "ExeSel", self)
        execute_sel_action.setToolTip("Execute the selected Limbs")

        icon = get_icon(
            sub_dir='/library/settings.png',
            color=[255, 88, 172])
        setting_action = QtWidgets.QAction(icon, "Setting", self)
        setting_action.setToolTip("Execute Settings")

        icon = get_icon(
            sub_dir='/library/back_up.png',
            color=[50, 88, 255])
        rebuild_action = QtWidgets.QAction(icon, "ReBuild", self)
        rebuild_action.setToolTip("Re-Build Rig")

        toolbar = QtWidgets.QToolBar("Limb Creation toolbar")
        self.addToolBar(toolbar)

        toolbar.setIconSize(QtCore.QSize(28, 28))

        # Add Actions
        toolbar.addAction(update_action)
        toolbar.addAction(refresh_action)
        toolbar.addSeparator()

        toolbar.addAction(export_action)
        toolbar.addSeparator()

        toolbar.addAction(export_temp_action)
        toolbar.addAction(import_temp_action)
        toolbar.addSeparator()

        toolbar.addAction(mirror_action)
        toolbar.addSeparator()

        toolbar.addAction(editor_action)
        toolbar.addSeparator()

        toolbar.addAction(execute_all_action)
        toolbar.addAction(execute_sel_action)
        toolbar.addAction(setting_action)
        toolbar.addAction(rebuild_action)

        # =========================================================
        # Connect Signals
        # =========================================================
        export_action.triggered.connect(partial(self.export_limb_data))
        export_temp_action.triggered.connect(partial(self.export_scene_template))
        import_temp_action.triggered.connect(partial(self.import_scene_template))
        editor_action.triggered.connect(partial(self.refresh_parameterUI))
        refresh_action.triggered.connect(partial(self.refresh_library))
        setting_action.triggered.connect(partial(self.open_build_tool))
        mirror_action.triggered.connect(partial(self.mirror_all_limbs))
        execute_all_action.triggered.connect(partial(self.build_all_limbs))
        execute_sel_action.triggered.connect(partial(self.build_selected_limbs))
        rebuild_action.triggered.connect(partial(self.re_build_rig))

    def deform_action_trigger(self, state=None, *args, **kwargs):
        """Todo doc."""
        if state is None:
            state = True

        MainUI.TRANSFER_DEFORM = state

    def attr_mirror_action_trigger(self, state, *args, **kwargs):
        """Todo doc."""
        if state is None:
            state = False

        MainUI.MIRROR_BY_PARAM = state

    def mirror_to_R_action_trigger(self, state, *args, **kwargs):
        """Todo doc."""
        if state is None:
            state = True

        self.mirror_to_R_action.setChecked(state)
        self.mirror_to_L_action.setChecked(not state)

        if state:
            MainUI.MIRROR_DIRECTION = NodeName.SIDE_R
        else:
            MainUI.MIRROR_DIRECTION = NodeName.SIDE_L

    def mirror_to_L_action_trigger(self, state, *args, **kwargs):
        """Todo doc."""
        self.mirror_to_L_action.setChecked(state)
        self.mirror_to_R_action.setChecked(not state)

        if state:
            MainUI.MIRROR_DIRECTION = NodeName.SIDE_L
        else:
            MainUI.MIRROR_DIRECTION = NodeName.SIDE_R

    def create_connections(self, *args, **kwargs):
        """
        create connections and signal
        Returns:

        """
        self.tab_widget.currentChanged.connect(partial(self.tab_change))

    @classmethod
    def revert_template(cls, *args, **kwargs):
        """revert template"""
        RigReBuilder.revert_rig_template()

    @classmethod
    def update_template(cls, *args, **kwargs):
        """save template"""
        RigReBuilder.update_template()

    def tab_change(self, *args, **kwargs):
        """
        tab change event
        Returns:

        """
        option_name = '{}_tab_value'.format(MainUI.UI_NAME)
        cmds.optionVar(intValue=(option_name, self.tab_widget.currentIndex()))

    def get_tab_index(self, *args, **kwargs):
        """get tab index"""
        option_name = '{}_tab_value'.format(MainUI.UI_NAME)
        if cmds.optionVar(exists=option_name):
            return cmds.optionVar(q=option_name)
        else:
            return 0

    def build_all_limbs(self, *args, **kwargs):
        """
        Create rig graph based on the 'LSRLimbTransform' nodes of the current scene
        Returns:
            None
        """
        self.limb_build = parse_graph.LSR_Limb_Build()
        self.limb_build.refresh_graph()
        self.limb_build.execute_graph()

    def build_selected_limbs(self, *args, **kwargs):
        """
        Create rig graph of selected LSRLimbTransform
        Returns:
            None
        """
        sel_nodes = cmds.ls(selection=True)
        if not sel_nodes:
            OpenMaya.MGlobal.displayWarning(
                'Nothing is selected.')
            return

        limb_node = Node(sel_nodes[0])
        if limb_node.fn_node.typeName() != 'LSRLimbTransform':
            OpenMaya.MGlobal.displayWarning(
                'No node of type LSRLimbTransform was selected.')
            return
        self.limb_build = parse_graph.LSR_Limb_Build()
        self.limb_build.limbs_nodes = limb_node
        self.limb_build.refresh_graph()
        self.limb_build.execute_graph()

    @staticmethod
    def re_build_rig(*args, **kwargs):
        """re build rig"""
        build_setting = {'transfer_deformers': MainUI.TRANSFER_DEFORM}
        rig_builder = RigReBuilder(**build_setting)
        rig_builder.re_build_rig()

    @staticmethod
    def refresh_library(*args, **kwargs):
        """
        refresh LSR ProtoStar library
        Returns:
            None
        """
        alib.refresh()

    @staticmethod
    def mirror_all_limbs(*args, **kwargs):
        """mirror all limbs"""

        kwargs = {'by_param': MainUI.MIRROR_BY_PARAM,
                  'to_direction': MainUI.MIRROR_DIRECTION}
        parse_graph.mirror_scene_limbs(**kwargs)

    @staticmethod
    def export_limb_data(*args, **kwargs):
        """
        Method: Export limb data
        Returns:

        """
        limb_nodes = cmds.ls(selection=True, type='LSRLimbTransform') or []
        if not limb_nodes:
            cmds.warning('No LSRLimbTransform node is selected!')
            return

        limb_node = limb_nodes[0]
        file_path, _ = QtWidgets.QFileDialog.getSaveFileName(
            None,
            "Choose export file name",
            MainUI.default_export_path,
            "data files (*.json) ;;")

        if not file_path:
            return

        limb_chain = utils.Limb_JointChain(limb_transform=limb_node)
        limb_chain.export(file_path=file_path)
        MainUI.default_export_path = os.path.dirname(file_path)
        OpenMaya.MGlobal.displayInfo(
            'Data file: {} is saved'.format(file_path))

    @staticmethod
    def open_build_tool(*args, **kwargs):
        """
        Open LSR_Maya_RIG_Build_UI Tool
        Returns:

        """
        build_window = build_setting_window.LSR_Maya_RIG_Build_UI.launch()

    @staticmethod
    def export_scene_template(*args, **kwargs):
        """
        Export all limbs of current scene to a template.
        Write the data to a json file.
        """
        file_path, _ = QtWidgets.QFileDialog.getSaveFileName(
            None,
            "Choose export file name",
            MainUI.default_export_path,
            "data files (*.json) ;;")
        if file_path:
            parse_graph.LSR_Limb_Build.scene_template_data(
                file_path=file_path)
            MainUI.default_export_path = os.path.dirname(file_path)
            OpenMaya.MGlobal.displayInfo(
                'Data file: {} is saved'.format(file_path))
        else:
            OpenMaya.MGlobal.displayWarning(
                'No template files selected were exported.')

    @staticmethod
    def import_scene_template(*args, **kwargs):
        """
        Import scene template
        """
        import_template()

    @staticmethod
    def set_icon(button, icon_file, *args, **kwargs):
        """Set the icon to script item."""
        icon = paths_util.get_limb_icon(icon_file, color=None)

        button.setIcon(icon)

    @staticmethod
    def refresh_parameterUI(*args, **kwargs):
        """
        refresh parameter UI
        Returns:
            None
        """

        limb = limb_parameter_window.LimbParameterUI.get_limb_by_selected()
        if not limb:
            return

        limb_type = limb.limb_type.value
        action_inst = alib.create_action(limb_type)
        ui = limb_parameter_window.LimbParameterUI.launch(action=action_inst)

        ui.parameter_panel.update_actions([ui.action])
        for param in ui.parameter_panel.param_list.limb_temp_params:
            if not limb.has_attr(param.name):
                continue

            if param.param_type == 'list':
                list_value = getattr(limb, param.name).value
                if not list_value:
                    list_value = '[]'
                getattr(ui.action, param.name).value = ast.literal_eval(list_value)
            else:
                getattr(ui.action, param.name).value = getattr(limb, param.name).value
        ui.change_lsr_LimbTransform()

    def script_plane(self, items, *args, **kwargs):
        """
        script plane
        Args:
            items (dict):

        Returns:
            QtWidgets.QWidget
        """
        widget = QtWidgets.QWidget()
        layout = FlowLayout()
        for item in items:
            but_label = ButtonLabel(parent=self, item=item)
            layout.addWidget(but_label)
        # layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_tabs(self, data, *args, **kwargs):
        """
        create shelf_util tabs
        Args:
            data (dict):

        Returns:
            self.tab_widget(QtWidgets.QTabWidget)
        """
        tab_info = {}
        main_tab = self.tab_widget
        for key, value in data.items():
            widget = self.script_plane(value)
            main_tab.addTab(widget, key)
            tab_info[key] = widget
        return main_tab

    def showEvent(self, event):
        """Save settings before showing."""
        # self.addDockWidget(QtCore.Qt.BottomDockWidgetArea, self.log_widget.log_dock)
        super(MainUI, self).showEvent(event)

    def closeEvent(self, event):
        """Save settings before closing."""
        # self.log_widget.LOG.removeHandler(self.log_widget)
        super(MainUI, self).closeEvent(event)

    def hideEvent(self, event):
        """ Save settings before hiding. """
        # self.log_widget.LOG.removeHandler(self.log_widget)
        super(MainUI, self).hideEvent(event)


class ButtonLabel(QtWidgets.QWidget):
    """
    Button Label QWidget
    """

    def __init__(self, parent=None, **kwargs):
        super(ButtonLabel, self).__init__(parent=parent)
        self.item = kwargs.get('item', None)

        if 'icon_size' in self.item:
            if isinstance(self.item['icon_size'], int):
                self.item_size = self.item['icon_size']
            else:
                self.item_size = SHELVE_ICON_SIZE
        else:
            self.item_size = SHELVE_ICON_SIZE

        vbox = QtWidgets.QVBoxLayout(self)
        self.button = QtWidgets.QPushButton()
        self.button.clicked.connect(partial(partial(execute_script, self.item)))
        self.button.setMinimumSize(self.item_size, self.item_size)
        self.button.setMaximumSize(self.item_size, self.item_size)
        self.button.setIconSize(QtCore.QSize(self.item_size, self.item_size))
        self.button.setToolTip(self.item.get("description", self.item["description"]))
        icon = self.item["icon"]
        if os.path.isfile(icon):
            self.set_icon(self.button, self.item["icon"])
        vbox.addWidget(self.button)

        self.limb_label = QtWidgets.QLabel(self)
        self.limb_label.setText(self.item['label'])
        self.limb_label.setAlignment(QtCore.Qt.AlignHCenter)
        vbox.addWidget(self.limb_label)

    @staticmethod
    def set_icon(button, icon_file, *args, **kwargs):
        """Set the icon to script item."""
        icon = paths_util.get_limb_icon(icon_file, color=None)
        button.setIcon(icon)


def show():
    """show the UI"""
    MainUI.launch()
