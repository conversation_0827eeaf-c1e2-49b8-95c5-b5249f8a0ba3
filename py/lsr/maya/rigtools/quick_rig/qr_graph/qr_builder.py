import json
import uuid

import lsr.maya.rig.meta_limb as meta_limb
import lsr.python.core.logger as logger
from lsr.protostar.lib import ActionLibrary as alib


class QRBuildNode(object):
    """
    A class for building a node in the graph.
    """

    def __init__(self, node_type, name):
        """Initializes a new object with an unique uuid."""
        self.__uuid = uuid.uuid4()
        self.__parent = None
        self.__children = set()
        self.__node_type = node_type
        self.__parameters = {}
        self.__position = [0, 0]
        self.name = name
        self.__intial_default_parameters()

    def __intial_default_parameters(self):
        """
        Initializes the parameters of this object.
        """
        action = alib.create_action(self.__node_type, name=self.name)
        node_parameters = action._get_data()['parameters']
        self.set_parameters(node_parameters)

    def __eq__(self, other):
        """
        Compares this object with another object.
        """
        return self.__class__ == other.__class__ and self.uuid == other.uuid

    def __ne__(self, other):
        """
        Compares this object with another object.
        """
        return not self.__eq__(other)

    def __hash__(self):
        """
        Returns the hash value of this object.
        """
        return hash(self.__uuid)

    def __str__(self):
        """
        Returns the string representation of this object.
        """
        return self.name

    @property
    def node_type(self):
        """
        Returns the node type of this object.
        """
        return self.__node_type

    @property
    def uuid(self):
        """
        Returns the unique identifier of this object.
        """
        return self.__uuid

    def __getattr__(self, name):
        """
        Returns the value of the specified parameter.
        args:
            name: str
        returns:
            object
        """
        for param in self.__parameters:
            if param['name'] == name:
                return param.get('value', None)

        raise AttributeError(
            '"{}" object have no attribute or parameter "{}"'.format(
                self.__node_type, name))

    def has_param(self, name):
        """
        Returns True if the specified parameter exists.
        args:
            name: str
        returns:
            bool
        """
        for param in self.__parameters:
            if param['name'] == name:
                return True
        return False

    def set_param(self, name, value):
        """
        Sets the value of the specified parameter.
        args:
            name: str
            value: object
        returns:
            bool
        """
        for param in self.__parameters:
            if param['name'] == name:
                param['value'] = value
                return True
        return False

    def add_param(self, param):
        """
        Adds a new parameter to this object.
        args:
            param: dict
        return:
            None
        """

        self.__parameters.append(param)

    def set_parameters(self, parameters):
        """
        Sets the parameters of this object.
        args:
            parameters: list
        return:
            None
        """
        self.__parameters = parameters

    def get_parameters(self):
        """
        Returns the parameters of this object.
        return:
            list
        """
        return self.__parameters

    def get_write_parameters(self):
        """
        Returns the parameters of this object.
        return:
            list
        """

        for param in self.__parameters:
            param['script_enabled'] = False
            param.pop("script", None)
        node = alib.get_action(self.__node_type)
        if self.__parent is None:
            return self.__parameters
        else:
            # set the parent limb or execution
            if self.__parent is not None:
                is_meta_limb = issubclass(node, meta_limb.MetaLimb)
                for param in self.__parameters:
                    if is_meta_limb and (not self.node_type == 'lsr:Root'):
                        if param.get('name') == 'parent_limb':
                            param['script_enabled'] = True
                            param['script'] = "{{{}}}".format(self.__parent.name)
                            break
                    else:
                        if param.get('name') == 'execution':
                            param['script_enabled'] = True
                            param['script'] = "{{{}.execution}}".format(self.__parent.name)
                            break

        return self.__parameters

    def set_parent_node(self, parent):
        """
        Sets the parent node of this object.
        args:
            parent: QRBuildNode
        return:
            None
        """
        self.__parent = parent
        if parent:
            parent.add_child(self)

    @property
    def parent_node(self):
        """
        Returns the parent node of this object.
        return:
            QRBuildNode
        """
        return self.__parent

    @property
    def children(self):
        """
        Returns the children of this object.
        return:
            set
        """
        return self.__children

    def add_child(self, child):
        """
        Adds a new child to this object.
        args:
            child: QRBuildNode
        """
        self.__children.add(child)

    @property
    def position(self):
        """
        Returns the position of this object.
        return:
            list
        """
        return self.__position

    def set_position(self, x, y):
        """
        Sets the position of this object.
        args:
            x: int
            y: int
        return:
            None

        """
        self.__position = [x, y]


class QRGraphBuilder(object):
    """
    A class for building a graph.
    """

    def __init__(self, graph_name):
        alib.refresh()
        self.__graph = alib.create_graph(name=graph_name)
        self.__nodes = []

    @property
    def graph(self):
        """
        Returns the graph of this object.
        """
        return self.__graph

    def __parse_graph(self, data):
        """
        Parses the graph from the specified data.
        args:
            data: dict
        return:
            None
        """
        for json_node in data['objects']:
            name = json_node['name']
            node_type = json_node['source']
            node_parameters = json_node['parameters']

            node = self.create_node(node_type, name)
            node.set_parameters(node_parameters)

        self.caculate_parent_node_by_param()

    def parse_graph_from_str(self, str):
        """
        Parses the graph from the specified string.
        args:
            str: str
        return:
            None
        """
        data = json.loads(str)
        self.__parse_graph(data)

    def parse_graph_from_file(self, file):
        """
        Parses the graph from the specified file.
        args:
            file: str
        return:
            None
        """
        with open(file, "r") as f:
            data = json.load(f)
            self.__parse_graph(data)

    def refresh_graph(self):
        """
        Refreshes the graph.
        """
        self.__graph.clear_objects()

        # get top nodes in the graph
        top_nodes = []
        for node in self.__nodes:
            if not node.parent_node:
                if node.node_type == 'lsr:NewMayaScene':
                    top_nodes.insert(0, node)
                else:
                    top_nodes.append(node)

        offset_x = 0
        offset_y = 0
        for node in top_nodes:
            node.set_position(offset_x, offset_y)
            self.__caculate_node_pos(node)
            offset_x -= 2000
            offset_y -= 1000

        for node in top_nodes:
            self.__write_node_to_graph(node)

    def caculate_parent_node_by_param(self):
        """
        Caculates the parent node by parameter.
        args:
            None
        return:
            None
        """

        for node in self.__nodes:
            for param in node.get_parameters():
                if param.get('script_enabled'):
                    connect_data = param.get('script')
                    if connect_data:
                        connect_str = connect_data[1:len(connect_data) - 1]
                        connect_list = connect_str.split('.')
                        parent_name = connect_list[0]
                        parent_node = self.find_node_by_name(parent_name)
                        if parent_node:
                            node.set_parent_node(parent_node)
                        else:
                            logger.info('The parent node({}) of the {} does not exist'.format(parent_name, node.name))
                    else:
                        logger.info('Parent is not found in {}'.format(node.name))

    def __caculate_node_pos(self, node):
        """
        Caculates the position of the node.
        args:
            node: QRBuildNode
        return:
            None
        """
        pos = node.position
        num = len(node.children)
        x = pos[0]
        y = pos[1]
        x += 500
        if num > 1:
            y = y + num * 800 / 2
        for node in node.children:
            node.set_position(x, y)
            self.__caculate_node_pos(node)

            if num > 1:
                y -= 800

    def __write_node_to_graph(self, node):
        """
        Writes the node to the graph.
        args:
            node: QRBuildNode
        return:
            None
        """
        action = alib.create_action(node.node_type, name=node.name, graph=self.__graph)
        paramters = node.get_write_parameters()
        pos = node.position
        data = {"ui_data": {"pos": pos, "LOD": 0}, "parameters": paramters}
        action._set_data(data)

        for node in node.children:
            self.__write_node_to_graph(node)

    def save_graph(self, file):
        """
        Saves the graph to the specified file.
        args:
            file: str
        return:
            None
        """
        self.__graph.write(file)

    def find_node_by_name(self, name):
        """
        Finds a node by the specified name.
        args:
            name: str
        return:
            QRBuildNode
        """
        for node in self.__nodes:
            if node.name == name:
                return node
        return None

    def find_node_by_type(self, name):
        """
        Finds a node by the specified type.
        args:
            name: str
        return:
            QRBuildNode
        """
        for node in self.__nodes:
            if node.node_type == name:
                return node
        return None

    def create_node(self, node_type, node_name):
        """
        Creates a new node.
        args:
            node_type: str
            node_name: str
            parent: QRBuildNode
        return:
            QRBuildNode
        """
        node = QRBuildNode(node_type, node_name)
        self.__nodes.append(node)
        return node

    def __dis_relation(self, node):
        """
        Dis-relates the node.
        args:
            node: QRBuildNode
        return:
            None
        """
        if node.parent_node is None:
            for child in node.children:
                child.set_parent_node(None)

        elif len(node.children) == 0:
            if node.parent_node:
                node.parent_node.children.remove(node)

        else:
            parent_node = node.parent_node
            parent_node.children.remove(node)
            for child in node.children:
                child.set_parent_node(parent_node)

        node.set_parent_node(None)
        node.children.clear()

    def __remove_node(self, node):
        """
        Removes the node.
        args:
            node: QRBuildNode
        return:
            None
        """
        self.__dis_relation(node)
        self.__nodes.remove(node)

    def get_hierarchy_nodes(self, node):
        """
        Gets the hierarchy nodes.
        args:
            node: QRBuildNode
        return:
            list
        """
        nodes = []
        nodes.append(node)
        for child in node.children:
            nodes.extend(self.get_hierarchy_nodes(child))
        return nodes

    def remove_node(self, node, recursive=False):
        """
        Removes the node.
        args:
            node: QRBuildNode
            recursive: bool
        return:
            None
        """
        if recursive:
            nodes = self.get_hierarchy_nodes(node)
            for node in nodes:
                self.__remove_node(node)
        else:
            self.__remove_node(node)

    def move_node(self, src_node, dst_node, after=True):
        """
        Moves the node.
        args:
            src_node: QRBuildNode
            dst_node: QRBuildNode
            after: bool
        return:
            None
        """

        if src_node == dst_node:
            return

        self.__dis_relation(src_node)

        if after:
            for child in dst_node.children:
                child.set_parent_node(src_node)
            dst_node.children.clear()
            src_node.set_parent_node(dst_node)
        else:
            if dst_node.parent_node is None:
                dst_node.set_parent_node(src_node)
            else:
                dst_parent_node = dst_node.parent_node
                dst_parent_node.children.remove(dst_node)
                src_node.children.add(dst_node)
                dst_node.set_parent_node(src_node)
                src_node.set_parent_node(dst_parent_node)

    def get_first_node(self, node):
        """
        Gets the first node.
        args:
            node: QRBuildNode
        return:
            QRBuildNode
        """
        if node.parent_node is None:
            return node
        else:
            return self.get_first_node(node.parent_node)

    def get_end_node(self, node):
        """
        Gets the end node.
        args:
            node: QRBuildNode
        return:
            QRBuildNode
        """
        if len(node.children) == 0:
            return node
        else:
            for child in node.children:
                return self.get_end_node(child)

    def execute(self):
        """
        Executes the graph.
        return:
            None
        """
        self.__graph.execute()
