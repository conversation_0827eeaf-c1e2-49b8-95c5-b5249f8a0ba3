import maya.cmds as cmds
import maya.api.OpenMaya as OpenMaya
from collections import OrderedDict


def record_pose(skeleton_root):
    child_joints = cmds.ls(cmds.listRelatives(skeleton_root, ad=True), type="joint")

    joint_matrix = OrderedDict()

    for joint in child_joints:
        selist_dag = OpenMaya.MSelectionList()
        selist_dag.add(joint)
        dagPath = selist_dag.getDagPath(0)
        mfn_trans = OpenMaya.MFnTransform(dagPath)
        w_matrix = mfn_trans.transformation()
        transformation = OrderedDict()
        transformation['translation'] = mfn_trans.translation(OpenMaya.MSpace.kTransform)
        transformation['rotation'] = mfn_trans.rotation(space=OpenMaya.MSpace.kTransform,
                                                        asQuaternion=True)
        # mtx = cmds.xform(joint, q=True, ws=True, matrix=True)
        # m_mtx = OpenMaya.MMatrix(mtx)
        joint_matrix[joint] = transformation

    return joint_matrix


def set_pose(joint_matrix):
    for joint, transformation in joint_matrix.items():
        selist_dag = OpenMaya.MSelectionList()
        selist_dag.add(joint)
        dagPath = selist_dag.getDagPath(0)
        mfn_trans = OpenMaya.MFnTransform(dagPath)
        # tmatrix = OpenMaya.MTransformationMatrix(transformation)
        # print joint, tmatrix.translation(OpenMaya.MSpace.kObject)
        mfn_trans.setTranslation(transformation['translation'], OpenMaya.MSpace.kTransform)
        mfn_trans.setRotation(transformation['rotation'], OpenMaya.MSpace.kTransform)


def diff_pose(jm01, jm02):
    # jm01 = OrderedDict()
    for joint in jm01.keys():
        selist_dag = OpenMaya.MSelectionList()
        selist_dag.add(joint)
        dagPath = selist_dag.getDagPath(0)
        mfn_trans = OpenMaya.MFnTransform(dagPath)
        pos = (jm02[joint]['translation'] + jm01[joint]['translation']) * 0.5
        rot = 0.5 * (jm02[joint]['rotation'] + jm01[joint]['rotation'])

        mfn_trans.setTranslation(pos, OpenMaya.MSpace.kTransform)
        mfn_trans.setRotation(rot, OpenMaya.MSpace.kTransform)


def delta_pose(jm01, jm02):
    for joint in jm01.keys():
        selist_dag = OpenMaya.MSelectionList()
        selist_dag.add(joint)
        dagPath = selist_dag.getDagPath(0)
        mfn_trans = OpenMaya.MFnTransform(dagPath)
        delta_pos = (jm02[joint]['translation'] - jm01[joint]['translation'])
        delta_rot = (jm02[joint]['rotation'] - jm01[joint]['rotation'])

        pos_now = mfn_trans.translation(OpenMaya.MSpace.kTransform)
        rot_now = mfn_trans.rotation(space=OpenMaya.MSpace.kTransform,
                                     asQuaternion=True)
        pos_new = pos_now - delta_pos
        rot_new = rot_now - delta_rot

        mfn_trans.setTranslation(pos_new, OpenMaya.MSpace.kTransform)
        mfn_trans.setRotation(rot_new, OpenMaya.MSpace.kTransform)


if __name__ == "__main__":
    ref_poses = record_pose('DHIhead:spine_05')
