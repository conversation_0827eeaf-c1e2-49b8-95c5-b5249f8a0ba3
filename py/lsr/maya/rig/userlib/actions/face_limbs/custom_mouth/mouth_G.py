"""
Mouth G Limb
"""

import maya.cmds as cmds

import lsr.protostar.core.parameter as pa

from lsr.maya.nodezoo.node import Joint
from lsr.maya.rig.userlib.actions.face_limbs.custom_mouth.mouth_C import Mouth_C_Limb
from lsr.maya.rig.userlib.actions.face_limbs.custom_mouth.mouth_base import Mouth_Custom_Base_Limb


class Mouth_G_Limb(Mouth_C_Limb):
    """
    Mouth_G_Limb limb class
    Create a rig controller for mouth G type

    :limb type: Mouth_G_Limb
    """

    _up_index = -9
    _low_index = -7
    _left_index = -5
    _right_index = -3

    @pa.list_param(item_type='str', default=('PLC', 'SDK', 'UPOFFSET', 'UPDRV', 'LOWOFFSET', 'LOWDRV',
                                             'INNEROFFSET', 'INNERDRV', 'OUTEROFFSET', 'OUTERDRV', 'OFFSET'))
    def group_exts(self):
        """Ctrl group extensions."""

    def marker_data(self):
        """Skip marker data as this limb depends on a pre-built
        joint hierarchy."""
        return

    def limb_template_data(self, *args, **kwargs):
        """limb template data"""
        limb_name = kwargs.get('name_part', 'mouthG')
        name_side = kwargs.get('name_side', 'M')
        cmds.select(deselect=True)
        joint_chain = []
        for i in range(2):
            jt_node = Joint.create(
                name='{}_JAW_{:02d}_{}_RIGJNT'.format(limb_name, i, name_side),
                p=(0, 0, i * 5))
            joint_chain.append(jt_node)
        joint_chain[0].orient_chain(
            orientJoint='zyx', secondaryAxisOrient='yup', zeroScaleOrient=True, children=True)

        joint_chain[1].jointOrient.value = (0, 0, 0)

        _joint_part = ['UpLip_00', 'UpLip_01', 'UpLip_02', 'LowLip_00', 'LowLip_01', 'LowLip_02', 'CornerLip']
        _joint_side = ['M', 'L', 'L', 'M', 'L', 'L', 'L']
        _joint_pos = [
            (0, 2, 2),
            (1.2, 1.7, 2),
            (2.2, 1.0, 2),
            (0, -2, 2),
            (1.2, -1.7, 2),
            (2.2, -1.0, 2),
            (3, 0, 2)
        ]

        for i, (part_name, pos_data, _side) in enumerate(zip(_joint_part, _joint_pos, _joint_side)):
            cmds.select(deselect=True)
            jt_node = Joint.create(
                name='{}_{}_{}_RIGJNT'.format(limb_name, part_name, _side),
                p=pos_data)
            joint_chain.append(jt_node)
            jt_node.set_parent(joint_chain[0])

        template_data = [
            {'type': 18, 'otherType': 'Jaw', 'overrideColor': 17},
            {'type': 18, 'otherType': 'Jaw_End', 'overrideColor': 17},
            {'type': 18, 'otherType': 'UpLip', 'overrideColor': 18},
            {'type': 18, 'otherType': 'Left_UpLip_01', 'overrideColor': 18},
            {'type': 18, 'otherType': 'Left_UpLip_02', 'overrideColor': 18},
            {'type': 18, 'otherType': 'LowLip', 'overrideColor': 18},
            {'type': 18, 'otherType': 'Left_LowLip_01', 'overrideColor': 18},
            {'type': 18, 'otherType': 'Left_LowLip_02', 'overrideColor': 18},
            {'type': 18, 'otherType': 'Left_CornerLip', 'overrideColor': 18}
        ]

        for jni_index, jnt in enumerate(joint_chain):
            jnt.set_attr('overrideEnabled', True)
            jnt.set_attr('drawLabel', True)
            for key, value in template_data[jni_index].items():
                jnt.set_attr(key, value)

        joint_chain = [joint_chain[i] for i in [0, 2, 3, 4, 5, 6, 7, 8, 1]]

        cmds.select(deselect=True)
        return joint_chain

    def run(self):
        """Builds the limb ctrl rig."""
        if len(self.group_exts.value) < 11:
            self.group_exts.value = ('PLC', 'SDK', 'UPOFFSET', 'UPDRV', 'LOWOFFSET', 'LOWDRV',
                                     'INNEROFFSET', 'INNERDRV', 'OUTEROFFSET', 'OUTERDRV', 'OFFSET')
        super(Mouth_G_Limb, self).run()

    def post_up_lip_rig(self, *args, **kwargs):
        """Post up lip rig"""
        grp_index = kwargs.get('grp_index', self._up_index)
        mid_index = kwargs.get('mid_index', 0)

        return super(Mouth_G_Limb, self).post_up_lip_rig(
            grp_index=grp_index, mid_index=mid_index)

    def post_low_lip_rig(self, *args, **kwargs):
        grp_index = kwargs.get('grp_index', self._low_index)
        mid_index = kwargs.get('mid_index', 0)

        return super(Mouth_G_Limb, self).post_low_lip_rig(
            grp_index=grp_index, mid_index=mid_index)

    def post_corner_lip_rig(self, *args, **kwargs):
        """Post corner lip rig"""
        left_grp_index = kwargs.get('grp_index', self._left_index)
        right_grp_index = kwargs.get('grp_index', self._right_index)

        left_ctrl = self.corner_ctrls[0]
        right_ctrl = self.corner_ctrls[1]

        # left side
        left_ctrls = [left_ctrl] + self.up_ctrls[0:3] + self.low_ctrls[0:3]
        t_matrix = self.get_mix_matrix([left_ctrl])
        self.left_main_ctrl = self.add_area_main_ctrl(
            matrix=t_matrix, desc='CornerLipMain', offset_vec=(1, 0, 2), scale=(1, 1, 1), side='L')

        self.fix_ext_matrix(left_ctrls, self.left_main_ctrl, left_grp_index)
        self.create_main_follow_weights(left_ctrls, self.left_main_ctrl, grp_index=left_grp_index+1)

        # right side
        r_up_ctrls = [self.up_ctrls[0]] + self.up_ctrls[3:5]
        r_low_ctrls = [self.low_ctrls[0]] + self.low_ctrls[3:5]
        right_ctrls = [right_ctrl] + r_up_ctrls + r_low_ctrls
        t_matrix = self.get_mix_matrix([right_ctrl])
        self.right_main_ctrl = self.add_area_main_ctrl(
            matrix=t_matrix, desc='CornerLipMain', offset_vec=(-1, 0, 2), scale=(1, 1, 1), side='R')
        self.fix_ext_matrix(right_ctrls, self.right_main_ctrl, right_grp_index)
        self.create_main_follow_weights(right_ctrls, self.right_main_ctrl, grp_index=right_grp_index+1)

        return [self.left_main_ctrl, self.right_main_ctrl]

    def end(self):
        """End of rig build"""
        Mouth_Custom_Base_Limb.end(self)
        weight_list = [0.0, 0.03, 0.08, 0.03, 0.08, 1, 0.85, 0.75, 0.85, 0.75, 0.5, 0.5]
        if self.all_lip_ctrls:
            self.change_jaw_follow_weights(ctrls=self.all_lip_ctrls, weight_list=weight_list)

        # optimize main ctrl
        # up lip
        weight_list = [0.7, 0.55, 0.35, 0.55, 0.35, 0.2, 0.2]
        self.optimize_main_ctrl(main_ctrl=self.up_main_ctrl,
                                lip_ctrls=self.up_ctrls+self.corner_ctrls, weight_list=weight_list)

        # low lip
        low_lips = self.low_ctrls+self.corner_ctrls
        weight_list = [0.7, 0.55, 0.35, 0.55, 0.35, 0.2, 0.2]
        self.optimize_main_ctrl(main_ctrl=self.low_main_ctrl,
                                lip_ctrls=self.low_ctrls+self.corner_ctrls, weight_list=weight_list)

        # left lip
        weight_list = [0.7, 0.1, 0.25, 0.45, 0.1, 0.25, 0.45]
        left_ctrls = [self.corner_ctrls[0]] + self.up_ctrls[0:3] + self.low_ctrls[0:3]
        self.optimize_main_ctrl(main_ctrl=self.left_main_ctrl, lip_ctrls=left_ctrls, weight_list=weight_list)

        # right
        r_up_ctrls = [self.up_ctrls[0]] + self.up_ctrls[3:5]
        r_low_ctrls = [self.low_ctrls[0]] + self.low_ctrls[3:5]
        right_ctrls = [self.corner_ctrls[1]] + r_up_ctrls + r_low_ctrls
        self.optimize_main_ctrl(main_ctrl=self.right_main_ctrl, lip_ctrls=right_ctrls, weight_list=weight_list)
