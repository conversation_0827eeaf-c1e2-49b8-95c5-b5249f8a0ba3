from lsr.maya.tsgame.animlinker.animlinker_widget import TSAnimLinkerWidget
from lsr.qt.core.widgets.qr_widgets.qr_window import get_qr_window_class

# Get the base window class with TSAnimLinkerWidget as the content
base_class = get_qr_window_class(app_name='TS AnimLinker v1.0', qr_widget=TSAnimLinkerWidget)
from maya.OpenMaya import MSceneMessage as ms


class TSAnimLinkerWindow(base_class):
    """
    The main window class for TS Animation Linker tool.
    This window provides an interface for linking animations in Maya.

    Inherits from the QR window base class and integrates Maya callbacks
    for file operations.
    """

    _REUSE_SINGLETON = False  # Whether to reuse existing window instance

    def __init__(self):
        """
        Initialize the TSAnimLinkerWindow instance.
        Sets up the window and initializes required attributes.
        """
        self.__thread = None  # Thread for background processes
        self.__save_path = None  # Path for saving files
        self.qrwidget = None  # Reference to the widget instance
        self.maya_callbacks = []  # List to store Maya callback IDs
        super().__init__(set_style=False)  # Initialize parent class without default styling

    def closeEvent(self, event):
        """
        Handle the window close event.
        Cleans up Maya callbacks before closing the window.

        Args:
            event: The close event object
        """
        for callback in self.maya_callbacks:
            ms.removeCallback(callback)  # Remove all registered callbacks
        self.maya_callbacks = []  # Clear the callback list
        super().closeEvent(event)  # Call parent's closeEvent method

    def showEvent(self, event):
        """
        Handle the window show event.
        Sets up Maya callbacks when the window is shown.

        Args:
            event: The show event object
        """
        super().showEvent(event)  # Call parent's showEvent method
        self.create_maya_callback()  # Create Maya callbacks

    def __after_open_cb(self, args):
        """
        Callback function executed after a Maya file is opened.
        Refreshes the UI to reflect the current file state.

        Args:
            args: Arguments passed by the Maya callback system (not used)
        """
        del args  # Delete unused arguments
        if self.qrwidget:
            self.qrwidget.refresh_ui()  # Refresh the UI if widget exists

    def create_maya_callback(self):
        """
        Create Maya callbacks for file operations.
        These callbacks will be triggered on various Maya file events.

        Currently empty - implementation to be added.
        """
        if not self.maya_callbacks:
            pass  # No callbacks added yet