from Qt import QtWidgets, QtGui, Qt<PERSON><PERSON>
from lsr.qt.core.widgets import qr_widgets as QRWidget
from lsr.maya.tsgame.animlinker.character_rig_list_context import TSCharacterRigListContext
from lsr.maya.tsgame.animlinker.character_anim_list_context import TSCharacterAnimListContext
from lsr.maya.rigtools.quick_rig.qr_list_context_maya import QRListContextMaya


class TSAnimLinkerPetWidget(QtWidgets.QWidget):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        ui_vl_main = QtWidgets.QVBoxLayout(self)
        self.setLayout(ui_vl_main)

        ui_gl_setting = QtWidgets.QGroupBox('Settings:')
        ui_gl_setting.setContentsMargins(0, 20, 0, 0)
        ui_vl_setting = QtWidgets.QVBoxLayout(ui_gl_setting)

        ui_hl_setting = QtWidgets.QHBoxLayout()
        ui_hl_setting.addWidget(QRWidget.QRCheckBox('None'))
        ui_vl_setting.addLayout(ui_hl_setting)
        ui_vl_main.addWidget(ui_gl_setting)

        ui_hl_rig = QtWidgets.QWidget()
        ui_hl_rig_layout = QtWidgets.QVBoxLayout(ui_hl_rig)
        ui_hl_rig_layout.setContentsMargins(0, 0, 0, 0)

        self.ui_h_splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)

        ui_gl_rig_files = QtWidgets.QGroupBox('Rig Files:')
        ui_vl_rig_files = QtWidgets.QVBoxLayout(ui_gl_rig_files)
        ui_gl_rig_files.setContentsMargins(0, 20, 0, 0)
        self.ui_fl_rig = QRWidget.QRFileListWidget('rig_list')
        ui_vl_rig_files.addWidget(self.ui_fl_rig)
        self.ui_h_splitter.addWidget(ui_gl_rig_files)

        ui_gl_active_files = QtWidgets.QGroupBox('Active Files:')
        ui_vl_active_files = QtWidgets.QVBoxLayout(ui_gl_active_files)
        ui_gl_active_files.setContentsMargins(0, 20, 0, 0)
        self.ui_fl_active = QRWidget.QRFileListWidget('active_list')
        ui_vl_active_files.addWidget(self.ui_fl_active)
        self.ui_h_splitter.addWidget(ui_gl_active_files)

        total_width = 600
        self.ui_h_splitter.setSizes([total_width * 0.5, total_width * 0.5])

        ui_hl_rig_layout.addWidget(self.ui_h_splitter)

        ui_gl_animation_files = QtWidgets.QGroupBox('Animation Files:')
        ui_vl_animation_files = QtWidgets.QVBoxLayout(ui_gl_animation_files)
        ui_gl_animation_files.setContentsMargins(0, 20, 0, 0)
        self.ui_fl_animation = QRWidget.QRFileListWidget('animation_list')
        ui_vl_animation_files.addWidget(self.ui_fl_animation)

        self.ui_splitter = QtWidgets.QSplitter(QtCore.Qt.Vertical)
        self.ui_splitter.addWidget(ui_hl_rig)
        self.ui_splitter.addWidget(ui_gl_animation_files)

        total_height = 500
        self.ui_splitter.setSizes([total_height * 0.2, total_height * 0.8])

        ui_vl_main.addWidget(self.ui_splitter)

        self._connect_signals()

        char_context = TSCharacterRigListContext()
        char_context.set_ref_list_widget(self.ui_fl_active)
        self.ui_fl_rig.set_pop_context_menu(char_context)

        char_anin_context = TSCharacterAnimListContext()
        char_anin_context.set_ref_list_widget(self.ui_fl_active)
        self.ui_fl_animation.set_pop_context_menu(char_anin_context)

        self.ui_fl_active.set_pop_context_menu(QRListContextMaya())

    def _connect_signals(self):
        pass


