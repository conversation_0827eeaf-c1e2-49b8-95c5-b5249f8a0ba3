import re
import maya.cmds as cmds
from lsr.maya.nodezoo.node import Node
import lsr.python.core.logger as logger
from lsr.maya.tsgame import ts_utils
from lsr.maya.tsgame.exporter.ts_export_fbx.export_base import IExport
from pathlib import Path


class TSExportMaleBody(IExport):
    def __init__(self, name_flag, maya_file, export_meshes, rig_mesh, skeleton):
        super().__init__(name_flag, maya_file, export_meshes, rig_mesh, skeleton)

        self.body_joints = ['item_ref_01', 'ik_foot_l', 'ik_foot_r', 'ik_foot_root',
                            'calfback_l', 'pinky_04_l', 'pinky_03_l', 'pinky_02_l', 'pinky_01_l', 'middle_04_l',
                            'middle_03_l', 'middle_02_l', 'middle_01_l', 'thumb_04_l', 'thumb_03_l', 'thumb_02_l',
                            'thumb_01_l', 'weapon_ref_l', 'index_04_l', 'index_03_l', 'index_02_l', 'index_01_l',
                            'ring_04_l', 'ring_03_l', 'ring_02_l', 'ring_01_l', 'hand_l', 'lowerarm_twist_01_l',
                            'lowerarm_l', 'upperarm_twist_01_l', 'upperarm_l', 'clavicle_l', 'head', 'neck_01',
                            'calfback_r', 'middle_04_r', 'middle_03_r', 'middle_02_r', 'middle_01_r', 'index_04_r',
                            'index_03_r', 'index_02_r', 'index_01_r', 'pinky_04_r', 'pinky_03_r', 'pinky_02_r',
                            'pinky_01_r', 'thumb_04_r', 'thumb_03_r', 'thumb_02_r', 'thumb_01_r', 'ring_04_r',
                            'ring_03_r', 'ring_02_r', 'ring_01_r', 'weapon_ref_r', 'hand_r', 'lowerarm_twist_01_r',
                            'lowerarm_r', 'upperarm_twist_01_r', 'upperarm_r', 'clavicle_r', 'spine_03', 'spine_02',
                            'spine_01', 'ball_l', 'foot_l', 'calf_twist_01_l', 'calf_l', 'thigh_twist_01_l',
                            'thigh_l', 'ball_r', 'foot_r', 'calf_twist_01_r', 'calf_r', 'thigh_twist_01_r',
                            'thigh_r', 'pelvis', 'item_ref_02', 'ik_hand_r', 'ik_hand_l', 'ik_hand_gun',
                            'ik_hand_root', 'root']

    def get_export_skeleton(self):
        has_full_joints = True
        for joint in self.body_joints:
            if not cmds.objExists(joint):
                logger.error('    ***Joint not found: {}'.format(joint))
                has_full_joints = False

        if not has_full_joints:
            return None

        # add face joints to the body joints
        face_joints = ts_utils.get_face_joints(self._skeleton)
        return list(set(face_joints +[Node(joint) for joint in self.body_joints]))

    def get_export_mesh(self):
        return self._export_meshes

    def get_export_fbx_file(self):
        maya_file = self._maya_file

        parent_dir = maya_file.parent.parent.parent.parent / 'FBX'
        if not parent_dir.parent.exists():
            parent_dir.parent.mkdir(parents=True)

        fbx_name = self._name_flag
        if re.match(r'^sm', fbx_name, re.I):
            # replace sm with sk
            fbx_name = re.sub(r'^sm', 'SK', fbx_name, flags=re.I)

        fbx_file = parent_dir / Path(fbx_name + '.fbx')
        return fbx_file
