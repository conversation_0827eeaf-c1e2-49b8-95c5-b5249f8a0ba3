# -*- coding: utf-8 -*-
# @Author: <PERSON>
# @Date:   2023-05-15 19:32:31
# @Last Modified by:   <PERSON>
# @Last Modified time: 2023-11-28 21:28:14
"""
Export Skin action of ES_Game
"""

import os
import re

from maya import cmds
from maya import mel

from lsr.maya.anim_lib.base_actions import BaseFbxExportAction
from lsr.maya.nodezoo.node import Node
import lsr.protostar.core.parameter as pa
import lsr.maya.animtools.fbx_exporter.export_utils as utils
import lsr.protostar.core.exception as exp
import lsr.maya.rig.rig_global as rg
from lsr.fbxsdk.FBX_Scene import FBX_Class
import lsr.maya.rig.constants as const
from lsr.maya.nodezoo.node import HIKCharacterNode
from lsr.maya.rig.userlib.actions import rig_character_definition
import lsr.maya.rig.quadruped_marking_menu as mm


class ES_exp_skin_action(BaseFbxExportAction):
    """
    ES Export Skin
    """

    # --- input parameters

    @pa.bool_param(default=True)
    def export_body(self):
        """If True, enable body animation export."""

    @pa.bool_param(default=True)
    def export_face(self):
        """If True, enable face animation export."""

    @pa.bool_param(default=True)
    def export_prop(self):
        """If True, enable prop(weapon) animation export."""
        
    @pa.list_param(item_type='str')
    def exported_bones(self):
        """exported bone list."""

    @pa.list_param(item_type='str')
    def exported_meshes(self):
        """exported meshes."""

    @pa.list_param(item_type='str')
    def exported_rigs(self):
        """exported rigs list."""

    @pa.str_param()
    def file_name(self):
        """maya animation file path."""
        
    @pa.bool_param(default=True)
    def export_end(self):
        """If True, enable export end."""


    def export_skin(self, rig_type, bone_grp, mesh_grp):
        """
        export anim method
        Args:
            rig_type (str): rig type
            bone_grp (Node): grp node of bind skeleton
            mesh_grp (Node): grp node of rig meshes

        Returns:

        """
        # hidemesh 里是绑定的头部模型
        hide_mesh_grp = 'HideMesh'
        
        cur_file_name = cmds.file(query=True, sceneName=True)
        base_name = os.path.basename(cur_file_name)
        dir_name = os.path.dirname(cur_file_name)

        cmds.select([bone_grp, mesh_grp], replace=True)
        cmds.select(cmds.ls(type='HIKCharacterNode'), add=True)


        non_exp_nodes = utils.deselect_non_export() or []
        non_exp_nodes = [node.split(':')[-1] for node in non_exp_nodes]

        ext = re.search(r'.*(\.\D+)', base_name).group(1)
        exp_folder = '{0}/resource'.format(dir_name)
        char_fbx_exp_folder = os.path.dirname(exp_folder)
        if not os.path.exists(exp_folder):
            os.makedirs(exp_folder)
            
        if rig_type == 'face':
            self.root_key_frame()
            self.export_end.value=0

            
        maya_path = '{0}/{1}'.format(exp_folder, base_name.replace(ext, '.fbx'))
        save_paths = [maya_path]

        if rig_type != 'face':
            #输出C1 fbx 肩膀不是和世界坐标平行的 带手指末端骨骼
            mobu_path_c2 = '{0}/{1}'.format(char_fbx_exp_folder, base_name.replace(ext, '_C2.fbx'))
            save_paths.append(mobu_path_c2)

            #输出C1 fbx 肩膀是和世界坐标平行的 带手指末端骨骼
            mobu_path_c1 = '{0}/{1}'.format(char_fbx_exp_folder, base_name.replace(ext, '_C.fbx'))
            save_paths.append(mobu_path_c1)

        for nub, save_path in enumerate(save_paths):
            
            if nub == 0:
                mm._reset_pose("RIG",selected=False, root_offset=True)

            if nub == 1:
                kwargs = {  'if_adjust_leg': True,
                            'if_adjust_neck': True,
                            'if_adjust_clavicle': False,
                            "if_fk_state": True,
                            "to_TPose": True,
                            }
            if nub == 2:
                kwargs = {  'if_adjust_leg': True,
                            'if_adjust_neck': True,
                            'if_adjust_clavicle': True,
                            "if_fk_state": True,
                            "to_TPose": True,
                            }
            if nub != 0:
                sel=cmds.ls(sl=1)
                #输出时变换成不同的 骨骼pose
                self.create_humanik_char(kwargs)
                cmds.select(sel)
                #输出物体添加头部模型
                if cmds.objExists(hide_mesh_grp): cmds.select(hide_mesh_grp, add=True)


            mel.eval('FBXExport -f \"{0}\" -s'.format(save_path))
            fbx_file = FBX_Class(save_path)
            nodes = [node.name for node in bone_grp.get_parent_hierarchy()]
            nodes += [node.name for node in mesh_grp.get_parent_hierarchy()]
            nodes = list(set(nodes))
            fbx_file.unparent_nodes(parent_nodes=nodes)
            fbx_file.remove_namespace()

            if nub == 0:
                fbx_file.remove_nodes_by_names(non_exp_nodes)
                
            fbx_file.save_scene_file()
        cmds.file(new=True, force=True)

    def open_maya_file(self):
        """Open Maya File"""
        file_name = self.file_name.value
        if not os.path.exists(file_name):
            raise exp.ActionError(
                'file is not exists {}'.format(file_name))
        cmds.refresh(suspend=True)
        # Open Scene
        cmds.file(file_name, open=True, force=True, prompt=False)

        utils.skin_exp_sdk_parameter()

    def run(self):
        """
        ES Skinning Export run method
        """
        self.open_maya_file()
        rig_roots = cmds.ls(
            'RIG.{}'.format(const.RIG_TYPE_ATTR),
            objectsOnly=True, recursive=True) or []
        if not rig_roots:
            cmds.warning('not find RIG Group to export')
            return
        self.rig_type = str()
        if len(rig_roots)==1:
            rg_node = rg.RigGlobal(rig_roots[0]) 
            self.rig_type = rg_node.rig_type

        self.exported_rigs.value = rig_roots
        self.export_rig()

    def export_rig(self):
        """Export rig core"""
        for rig_node in self.exported_rigs.value:
            rg_node = rg.RigGlobal(rig_node)

            rig_type = rg_node.rig_type
            bone_grp = rg_node.export_bone_grp
            mesh_grp = rg_node.export_mesh_grp

            if not (rig_type and bone_grp and mesh_grp):
                cmds.warning('{} is not legal rig'.format(rig_node))
                continue

            self.export_skin(rig_type, bone_grp, mesh_grp)

    def end(self):
        """define export task"""
        super(ES_exp_skin_action, self).end()
        cmds.refresh(suspend=False)



    def root_key_frame(self):
        '''给表情Rig的Root Keyframe 为了给UE导入的时候含有自定义属性
        '''        
        for rig_node in self.exported_rigs.value:
            rg_node = rg.RigGlobal(rig_node)
            root_grp = rg_node.export_bone_grp
            rig_roots = cmds.listRelatives(root_grp, c=True)
            if len(rig_roots)==1:
                rig_root = rig_roots[0]
                attrs = cmds.listAttr(rig_root, k=1)
                for attr in attrs:
                    obj_attr = "{0}.{1}".format(rig_root, attr)
                    
                    source_obj_attr = cmds.listConnections(obj_attr, source=True, p=True)
                    if source_obj_attr:
                        cmds.disconnectAttr(source_obj_attr[0], obj_attr)
                    cmds.setKeyframe(rig_root, attribute=attr, t=0)
                    cmds.setKeyframe(rig_root, attribute=attr, t=1)


    def create_humanik_char(self, kwargs):
        '''删除旧的角色化信息 创建一个新 Characters
        :param kwargs: _description_
        :type kwargs: _type_
        '''        
        char_nodes = HIKCharacterNode.get_all_hik_characters()
        if char_nodes:
            character_name = char_nodes[0].name
        else:
            character_name = 'LSR_character'
            
        if_adjust_leg = kwargs.get('if_adjust_leg', True)
        if_adjust_neck = kwargs.get('if_adjust_neck', True)
        if_adjust_clavicle = kwargs.get('if_adjust_clavicle', True)
        if_fk_state = kwargs.get('if_fk_state', [])
        to_TPose = kwargs.get('to_TPose', True)
    
        hik_chr = rig_character_definition.CharacterDefinition()
        hik_chr.if_adjust_leg.value = if_adjust_leg
        hik_chr.if_adjust_neck.value = if_adjust_neck
        hik_chr.if_adjust_clavicle.value = if_adjust_clavicle
        hik_chr.if_fk_state.value = if_fk_state
        hik_chr.to_TPose.value = to_TPose
        hik_chr.character_name.value = character_name
        hik_chr.execute()