"""
MatrixConstraint filter nodezoo class

"""

import maya.cmds as cmds
import maya.OpenMaya as OpenMaya

from lsr.maya.standard.name import NodeName
from lsr.maya.nodezoo.node import DependencyNode
from lsr.maya.nodezoo.node import Node


class MatrixConstraint(DependencyNode):
    """
    Matrix Constraint class.

    """

    __NODETYPE__ = 'lsr_matrixConstraint'
    __FNCLS__ = OpenMaya.MFnDependencyNode

    @classmethod
    def create(cls, *args, **kwargs):
        """Wrapper create function."""
        if not args:
            return cls._create_default(**kwargs)
        else:
            return cls._create_from_paras(*args, **kwargs)

    @classmethod
    def _create_default(cls, **kwargs):
        """Create and connect matrix constraint node

        Returns:
            MatrixConstraint: The matrix constraint node
        """
        cns_m = cls(cmds.createNode('lsr_matrixConstraint', **kwargs))
        return cns_m

    @classmethod
    def _create_from_paras(cls, in_obj, out_obj, connect_srt='',
                           rot_off=[0, 0, 0],
                           rot_mult=[1, 1, 1],
                           scl_mult=[1, 1, 1]):
        """Create and connect matrix constraint node

        Args:
            in_obj (transform): the driver object or matrix
            out_obj (transform, optional): the driven object
            connect_srt (str, optional): scale rotation translation flag
            rot_off (list, optional): rotation offset for XYZ
            rot_mult (list, optional): rotation multiplier for XYZ
            scl_mult (list, optional): scale multiplier for XYZ

        Returns:
            MatrixConstraint: The matrix constraint node
        """
        try:
            name = NodeName(out_obj, ext='LSRMC')
        except ValueError:
            if isinstance(out_obj, Node):
                name = '{}_LSRMC'.format(out_obj.name)
            else:
                name = '{}_LSRMC'.format(out_obj)
                out_obj = Node(out_obj)
        cns_m = cls(cmds.createNode('lsr_matrixConstraint',
                                    name=name))

        cns_m.isHistoricallyInteresting.value = False
        in_obj.worldMatrix[0] >> cns_m.driverMatrix

        # setting rot and scl config
        cns_m.driverRotationOffsetX.value = rot_off[0]
        cns_m.driverRotationOffsetY.value = rot_off[1]
        cns_m.driverRotationOffsetZ.value = rot_off[2]

        cns_m.rotationMultX.value = rot_mult[0]
        cns_m.rotationMultY.value = rot_mult[1]
        cns_m.rotationMultZ.value = rot_mult[2]

        cns_m.scaleMultX = scl_mult[0]
        cns_m.scaleMultY = scl_mult[1]
        cns_m.scaleMultZ = scl_mult[2]

        out_obj.parentInverseMatrix[0] >> cns_m.drivenParentInverseMatrix

        # calculate rest pose
        # we use the  outputDriverOffsetMatrix to have in account the offset
        # rotation when the rest pose is calculated
        driver_m = OpenMaya.MMatrix(
            cmds.getAttr(cns_m.outputDriverOffsetMatrix))

        driven_m = OpenMaya.MMatrix(
            cmds.getAttr(out_obj.parentInverseMatrix[0]))

        if out_obj.type_name == 'joint':
            mult_mat = driver_m * driven_m
            rest_plug = cns_m.fn_node.findPlug('drivenRestMatrix')
            mfnMatData = OpenMaya.MFnMatrixData()
            matMObj = mfnMatData.create(mult_mat)
            rest_plug.setMObject(matMObj)

        # connect srt (scale, rotation, translation)
        if 't' in connect_srt:
            cns_m.translate >> out_obj.translate
        if 'r' in connect_srt:
            cns_m.rotate >> out_obj.rotate
        if 's' in connect_srt:
            cns_m.scale >> out_obj.scale
            cns_m.shear >> out_obj.shear

        return cns_m

    def reset_drivenRestMatrix(self, out_obj):
        """Reset attribute: drivenRestMatrix"""
        driver_m = OpenMaya.MMatrix(
            cmds.getAttr(self.outputDriverOffsetMatrix))

        driven_m = OpenMaya.MMatrix(
            cmds.getAttr(out_obj.parentInverseMatrix[0]))

        mult_mat = driver_m * driven_m
        rest_plug = self.fn_node.findPlug('drivenRestMatrix')
        mfnMatData = OpenMaya.MFnMatrixData()
        matMObj = mfnMatData.create(mult_mat)
        rest_plug.setMObject(matMObj)
