<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SpringMagic_mainWindow</class>
 <widget class="QMainWindow" name="SpringMagic_mainWindow">
  <property name="windowModality">
   <enum>Qt::NonModal</enum>
  </property>
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>380</width>
    <height>425</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>380</width>
    <height>425</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>380</width>
    <height>655</height>
   </size>
  </property>
  <property name="cursor">
   <cursorShape>ArrowCursor</cursorShape>
  </property>
  <property name="mouseTracking">
   <bool>false</bool>
  </property>
  <property name="focusPolicy">
   <enum>Qt::NoFocus</enum>
  </property>
  <property name="windowTitle">
   <string notr="true">Spring Magic 3.5a        Yanbin Bai</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>icons/Title.png</normaloff>icons/Title.png</iconset>
  </property>
  <property name="iconSize">
   <size>
    <width>24</width>
    <height>24</height>
   </size>
  </property>
  <property name="toolButtonStyle">
   <enum>Qt::ToolButtonIconOnly</enum>
  </property>
  <property name="animated">
   <bool>true</bool>
  </property>
  <property name="tabShape">
   <enum>QTabWidget::Rounded</enum>
  </property>
  <property name="dockNestingEnabled">
   <bool>false</bool>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QTabWidget" name="main_tab">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>381</width>
      <height>341</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
      <weight>50</weight>
      <bold>false</bold>
     </font>
    </property>
    <property name="mouseTracking">
     <bool>false</bool>
    </property>
    <property name="contextMenuPolicy">
     <enum>Qt::DefaultContextMenu</enum>
    </property>
    <property name="acceptDrops">
     <bool>true</bool>
    </property>
    <property name="toolTip">
     <string/>
    </property>
    <property name="statusTip">
     <string/>
    </property>
    <property name="whatsThis">
     <string/>
    </property>
    <property name="accessibleName">
     <string/>
    </property>
    <property name="accessibleDescription">
     <string/>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="tabPosition">
     <enum>QTabWidget::North</enum>
    </property>
    <property name="tabShape">
     <enum>QTabWidget::Rounded</enum>
    </property>
    <property name="currentIndex">
     <number>1</number>
    </property>
    <property name="elideMode">
     <enum>Qt::ElideNone</enum>
    </property>
    <property name="usesScrollButtons">
     <bool>true</bool>
    </property>
    <property name="documentMode">
     <bool>false</bool>
    </property>
    <property name="tabsClosable">
     <bool>false</bool>
    </property>
    <property name="movable">
     <bool>false</bool>
    </property>
    <property name="tabBarAutoHide" stdset="0">
     <bool>true</bool>
    </property>
    <widget class="QWidget" name="donate_tab">
     <attribute name="icon">
      <iconset>
       <normaloff>icons/donut.png</normaloff>icons/donut.png</iconset>
     </attribute>
     <attribute name="title">
      <string>Donate</string>
     </attribute>
     <widget class="QGroupBox" name="eng_groupBox">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>10</y>
        <width>351</width>
        <height>291</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="title">
       <string>Need Your Supports! </string>
      </property>
      <property name="flat">
       <bool>false</bool>
      </property>
      <widget class="QPushButton" name="donatePayPal_button">
       <property name="geometry">
        <rect>
         <x>150</x>
         <y>200</y>
         <width>51</width>
         <height>51</height>
        </rect>
       </property>
       <property name="cursor">
        <cursorShape>PointingHandCursor</cursorShape>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;PayPal Link&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>icons/paypal.png</normaloff>icons/paypal.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>40</width>
         <height>40</height>
        </size>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QLabel" name="label_3">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>160</y>
         <width>121</width>
         <height>121</height>
        </rect>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;AliPay QR Code&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="pixmap">
        <pixmap>icons/ali_pay.png</pixmap>
       </property>
      </widget>
      <widget class="QLabel" name="label_21">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>160</y>
         <width>121</width>
         <height>121</height>
        </rect>
       </property>
       <property name="mouseTracking">
        <bool>true</bool>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;WeChat Pay QR Code&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="pixmap">
        <pixmap>icons/wechat_pay.png</pixmap>
       </property>
      </widget>
      <widget class="QLabel" name="label_22">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>20</y>
         <width>321</width>
         <height>81</height>
        </rect>
       </property>
       <property name="text">
        <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'SimSun'; font-size:8pt; font-weight:600; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'MS Shell Dlg 2'; font-weight:400;&quot;&gt;Spring Magic is free for everyone, you can use&lt;/span&gt;&lt;span style=&quot; font-family:'MS Shell Dlg 2';&quot;&gt; Full Function&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'MS Shell Dlg 2'; font-weight:400;&quot;&gt; as free! However if this make your life easier...&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'MS Shell Dlg 2'; font-size:10pt;&quot;&gt;How about bought me cup of coffee?&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
      </widget>
      <widget class="QLabel" name="label">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>110</y>
         <width>331</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft Sans Serif</family>
         <pointsize>10</pointsize>
         <weight>50</weight>
         <bold>false</bold>
        </font>
       </property>
       <property name="text">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:8pt;&quot;&gt;Special Thanks：&lt;/span&gt;&lt;span style=&quot; font-size:8pt; font-weight:600;&quot;&gt;Benoit Degand&lt;/span&gt;&lt;span style=&quot; font-size:8pt;&quot;&gt; help improve performance&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
      </widget>
     </widget>
    </widget>
    <widget class="QWidget" name="spring_tab">
     <attribute name="icon">
      <iconset>
       <normaloff>icons/spring.png</normaloff>icons/spring.png</iconset>
     </attribute>
     <attribute name="title">
      <string>Spring Magic</string>
     </attribute>
     <widget class="QGroupBox" name="spring_groupBox">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>-30</y>
        <width>361</width>
        <height>341</height>
       </rect>
      </property>
      <property name="title">
       <string> Spring Magic </string>
      </property>
      <property name="flat">
       <bool>true</bool>
      </property>
      <widget class="QLineEdit" name="springSpring_lineEdit">
       <property name="geometry">
        <rect>
         <x>46</x>
         <y>50</y>
         <width>31</width>
         <height>20</height>
        </rect>
       </property>
       <property name="inputMask">
        <string/>
       </property>
       <property name="text">
        <string>0.7</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QLabel" name="label_24">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>52</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;set between 0 to 1&lt;/p&gt;&lt;p&gt;define how soft the spring is, bigger value, softer result.&lt;/p&gt;&lt;p&gt;Important: only working for &amp;quot;X-axis aiming to child&amp;quot; joint chain.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string>Spring</string>
       </property>
      </widget>
      <widget class="QLabel" name="label_25">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>-68</y>
         <width>31</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>Subs</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="springSubs_lineEdit">
       <property name="geometry">
        <rect>
         <x>190</x>
         <y>-70</y>
         <width>31</width>
         <height>20</height>
        </rect>
       </property>
       <property name="text">
        <string>0</string>
       </property>
      </widget>
      <widget class="QLabel" name="label_26">
       <property name="geometry">
        <rect>
         <x>90</x>
         <y>52</y>
         <width>61</width>
         <height>16</height>
        </rect>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;set between 0 to 1&lt;/p&gt;&lt;p&gt;define how soft the twist (X-axis) is&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string>Twist</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="springXspring_lineEdit">
       <property name="geometry">
        <rect>
         <x>120</x>
         <y>50</y>
         <width>31</width>
         <height>20</height>
        </rect>
       </property>
       <property name="inputMask">
        <string/>
       </property>
       <property name="text">
        <string>0.7</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QGroupBox" name="springBonePose_groupBox">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>230</y>
         <width>141</width>
         <height>101</height>
        </rect>
       </property>
       <property name="title">
        <string> Bone Pose </string>
       </property>
       <widget class="QPushButton" name="springBindPose_button">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>20</y>
          <width>61</width>
          <height>31</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>Go to joint bind pose</string>
        </property>
        <property name="text">
         <string>Bind Pose</string>
        </property>
       </widget>
       <widget class="QPushButton" name="springStraight_button">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>60</y>
          <width>61</width>
          <height>31</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
        <property name="toolTip">
         <string>Set joint chain as straight</string>
        </property>
        <property name="text">
         <string>Straight</string>
        </property>
       </widget>
       <widget class="QPushButton" name="springCopy_button">
        <property name="geometry">
         <rect>
          <x>80</x>
          <y>20</y>
          <width>51</width>
          <height>31</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>Copy bone poses</string>
        </property>
        <property name="text">
         <string>Copy</string>
        </property>
       </widget>
       <widget class="QPushButton" name="springPaste_button">
        <property name="geometry">
         <rect>
          <x>80</x>
          <y>60</y>
          <width>51</width>
          <height>31</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>Paste bone poses</string>
        </property>
        <property name="text">
         <string>Paste</string>
        </property>
       </widget>
      </widget>
      <widget class="QPushButton" name="springApply_Button">
       <property name="geometry">
        <rect>
         <x>260</x>
         <y>280</y>
         <width>81</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Affect select object only&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string>Apply</string>
       </property>
      </widget>
      <widget class="QLabel" name="label_29">
       <property name="geometry">
        <rect>
         <x>420</x>
         <y>40</y>
         <width>41</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>UpAxis</string>
       </property>
      </widget>
      <widget class="QGroupBox" name="collision_groupBox">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>160</y>
         <width>331</width>
         <height>61</height>
        </rect>
       </property>
       <property name="title">
        <string> Collisions </string>
       </property>
       <widget class="QCheckBox" name="springCapsule_checkBox">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>30</y>
          <width>61</width>
          <height>17</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Active collision calculation with capsule body in scene.&lt;/p&gt;&lt;p&gt;May slow down the calculation&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string>Active</string>
        </property>
       </widget>
       <widget class="QPushButton" name="springAddBody_Button">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>180</x>
          <y>14</y>
          <width>41</width>
          <height>41</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Add a capsule at original point if select notion, or add a capsule match with selected bone.&lt;/p&gt;&lt;p&gt;Note: you can adjust the capsule with move or scale the cylinder at middle. Parent capsule to diffecrent object to move follow it.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>icons/addCapsule.png</normaloff>icons/addCapsule.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>32</width>
          <height>32</height>
         </size>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
        <property name="flat">
         <bool>false</bool>
        </property>
       </widget>
       <widget class="QPushButton" name="springClearBody_Button">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>280</x>
          <y>14</y>
          <width>41</width>
          <height>41</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Clear all capsules in scene&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>icons/clearCapsule.png</normaloff>icons/clearCapsule.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>32</width>
          <height>32</height>
         </size>
        </property>
       </widget>
       <widget class="QCheckBox" name="springFastMove_checkBox">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>80</x>
          <y>30</y>
          <width>75</width>
          <height>17</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Click on this if capsule attach on a fast move bone, like sprinting legs, to reduce the clip through.&lt;/p&gt;&lt;p&gt;Note: May cause more poping if capsule moving slowly.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string>Fast Move</string>
        </property>
       </widget>
       <widget class="QCheckBox" name="springFloor_checkBox">
        <property name="geometry">
         <rect>
          <x>380</x>
          <y>22</y>
          <width>50</width>
          <height>16</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Add a floor collition for joint chain&lt;/p&gt;&lt;p&gt;Script will trying to keep all the joints above the hight during caculation&lt;/p&gt;&lt;p&gt;启用地面碰撞，计算时会尽量将骨骼保持在设定高度之上&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string>Floor</string>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
       <widget class="QLineEdit" name="springFloor_lineEdit">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>430</x>
          <y>20</y>
          <width>31</width>
          <height>20</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Floor height&lt;/p&gt;&lt;p&gt;地面高度&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string>0.0</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
       <widget class="QPushButton" name="springAddPlane_Button">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>230</x>
          <y>14</y>
          <width>41</width>
          <height>41</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Create a collision plane&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>icons/addPlane.png</normaloff>icons/addPlane.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>32</width>
          <height>32</height>
         </size>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
        <property name="flat">
         <bool>false</bool>
        </property>
       </widget>
      </widget>
      <widget class="QLabel" name="label_30">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="geometry">
        <rect>
         <x>232</x>
         <y>0</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Tension of the chain, means how much the force of bend will be path through the chain, only take effect when collision happends. Can reduce poping and clip through of collision result.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string>Tension</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="springTension_lineEdit">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="geometry">
        <rect>
         <x>271</x>
         <y>0</y>
         <width>31</width>
         <height>20</height>
        </rect>
       </property>
       <property name="inputMask">
        <string/>
       </property>
       <property name="text">
        <string>0.5</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QGroupBox" name="keyRange_groupBox">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>80</y>
         <width>331</width>
         <height>71</height>
        </rect>
       </property>
       <property name="title">
        <string>Key Setting</string>
       </property>
       <widget class="QCheckBox" name="springClearSubFrame_checkBox">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>120</x>
          <y>50</y>
          <width>71</width>
          <height>16</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Remove precision keys on sub frame after calculation&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string>Wipe Sub</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
       <widget class="QLabel" name="label_31">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>50</y>
          <width>71</width>
          <height>16</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;How many sub frame that will be go through during 1 frame, that for capture really fast move&lt;/p&gt;&lt;p&gt;collision body. Default value is 1, if set as 5, means will step as 1/5 frame to calculation.&lt;/p&gt;&lt;p&gt;will increase calculation time by times&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string>Sub-Frame</string>
        </property>
       </widget>
       <widget class="QLineEdit" name="springSubDiv_lineEdit">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>80</x>
          <y>48</y>
          <width>31</width>
          <height>20</height>
         </rect>
        </property>
        <property name="inputMask">
         <string/>
        </property>
        <property name="text">
         <string>1</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
       <widget class="QRadioButton" name="springActive_radioButton">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>20</y>
          <width>61</width>
          <height>17</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>Current time line range</string>
        </property>
        <property name="text">
         <string>Active</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
       <widget class="QRadioButton" name="springFrom_radioButton">
        <property name="geometry">
         <rect>
          <x>140</x>
          <y>20</y>
          <width>51</width>
          <height>17</height>
         </rect>
        </property>
        <property name="text">
         <string>From</string>
        </property>
       </widget>
       <widget class="QLineEdit" name="springFrom_lineEdit">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>190</x>
          <y>20</y>
          <width>31</width>
          <height>20</height>
         </rect>
        </property>
        <property name="inputMask">
         <string/>
        </property>
        <property name="text">
         <string>0</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
       <widget class="QLabel" name="label_28">
        <property name="geometry">
         <rect>
          <x>230</x>
          <y>20</y>
          <width>21</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>To</string>
        </property>
       </widget>
       <widget class="QLineEdit" name="springEnd_lineEdit">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>250</x>
          <y>20</y>
          <width>31</width>
          <height>20</height>
         </rect>
        </property>
        <property name="inputMask">
         <string/>
        </property>
        <property name="text">
         <string>200</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
       <widget class="QCheckBox" name="springLoop_checkBox">
        <property name="geometry">
         <rect>
          <x>280</x>
          <y>50</y>
          <width>51</width>
          <height>16</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Will caculate twice to get looped result&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string>Loop</string>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
       <widget class="QCheckBox" name="springPoseMatch_checkBox">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>194</x>
          <y>50</y>
          <width>81</width>
          <height>16</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;make result close to exists keyframe pose&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string>Pose Match</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
      </widget>
      <widget class="QPushButton" name="springWind_Button">
       <property name="geometry">
        <rect>
         <x>260</x>
         <y>240</y>
         <width>81</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
         <weight>50</weight>
         <bold>false</bold>
        </font>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Add wind controller&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string>Wind</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>icons/wind.png</normaloff>icons/wind.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
      </widget>
      <widget class="QGroupBox" name="ctrl_groupBox">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>230</y>
         <width>91</width>
         <height>101</height>
        </rect>
       </property>
       <property name="title">
        <string>Controller</string>
       </property>
       <widget class="QPushButton" name="springBind_Button">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>20</y>
          <width>71</width>
          <height>31</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Bind bone chain to selected rigging controller&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string>Bind</string>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>icons/ctrl_bind.png</normaloff>icons/ctrl_bind.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>24</width>
          <height>24</height>
         </size>
        </property>
       </widget>
       <widget class="QPushButton" name="springBake_Button">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>60</y>
          <width>71</width>
          <height>31</height>
         </rect>
        </property>
        <property name="toolTip">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Bake bone chain animation back to controller&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="text">
         <string>Bake</string>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>icons/ctrl_bake.png</normaloff>icons/ctrl_bake.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>24</width>
          <height>24</height>
         </size>
        </property>
       </widget>
      </widget>
      <widget class="QLabel" name="label_27">
       <property name="geometry">
        <rect>
         <x>166</x>
         <y>52</y>
         <width>61</width>
         <height>16</height>
        </rect>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;set between 0 to 1&lt;/p&gt;&lt;p&gt;define flexibility of spring, which can produce strech and squash animation&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string>Flex</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="springExtend_lineEdit">
       <property name="geometry">
        <rect>
         <x>192</x>
         <y>50</y>
         <width>31</width>
         <height>20</height>
        </rect>
       </property>
       <property name="inputMask">
        <string/>
       </property>
       <property name="text">
        <string>0.0</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QLabel" name="label_32">
       <property name="geometry">
        <rect>
         <x>240</x>
         <y>52</y>
         <width>61</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;set between 0 to 1&lt;/p&gt;&lt;p&gt;define inertia of spring, which can produce weight result&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string>Inertia</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="springInertia_lineEdit">
       <property name="geometry">
        <rect>
         <x>277</x>
         <y>50</y>
         <width>31</width>
         <height>20</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <property name="text">
        <string>0.0</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QPushButton" name="shelf_button">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>40</y>
         <width>30</width>
         <height>30</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Creat shelf button of Spring Magic&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="layoutDirection">
        <enum>Qt::LeftToRight</enum>
       </property>
       <property name="autoFillBackground">
        <bool>false</bool>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>icons/Shelf.png</normaloff>icons/Shelf.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
       <property name="checkable">
        <bool>false</bool>
       </property>
       <property name="autoRepeat">
        <bool>false</bool>
       </property>
       <property name="autoExclusive">
        <bool>false</bool>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
      <zorder>label_24</zorder>
      <zorder>keyRange_groupBox</zorder>
      <zorder>springSpring_lineEdit</zorder>
      <zorder>label_25</zorder>
      <zorder>springSubs_lineEdit</zorder>
      <zorder>label_26</zorder>
      <zorder>springXspring_lineEdit</zorder>
      <zorder>springBonePose_groupBox</zorder>
      <zorder>springApply_Button</zorder>
      <zorder>label_29</zorder>
      <zorder>collision_groupBox</zorder>
      <zorder>label_30</zorder>
      <zorder>springTension_lineEdit</zorder>
      <zorder>springWind_Button</zorder>
      <zorder>ctrl_groupBox</zorder>
      <zorder>label_27</zorder>
      <zorder>springExtend_lineEdit</zorder>
      <zorder>label_32</zorder>
      <zorder>springInertia_lineEdit</zorder>
      <zorder>shelf_button</zorder>
     </widget>
    </widget>
   </widget>
   <widget class="QTextEdit" name="main_textEdit">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>410</y>
      <width>345</width>
      <height>200</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
      <horstretch>200</horstretch>
      <verstretch>200</verstretch>
     </sizepolicy>
    </property>
    <property name="minimumSize">
     <size>
      <width>345</width>
      <height>40</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>345</width>
      <height>200</height>
     </size>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
     </font>
    </property>
    <property name="readOnly">
     <bool>true</bool>
    </property>
    <property name="html">
     <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'SimSun'; font-size:8pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'MS Shell Dlg 2'; font-size:9pt;&quot;&gt;Created by Bai Yanbin&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-family:'MS Shell Dlg 2'; font-size:9pt;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-family:'MS Shell Dlg 2'; font-size:9pt;&quot;&gt;&lt;br /&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
   </widget>
   <widget class="QProgressBar" name="main_progressBar">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>375</y>
      <width>361</width>
      <height>27</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Consolas</family>
      <pointsize>12</pointsize>
     </font>
    </property>
    <property name="acceptDrops">
     <bool>false</bool>
    </property>
    <property name="value">
     <number>0</number>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="textVisible">
     <bool>true</bool>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
    <property name="textDirection">
     <enum>QProgressBar::TopToBottom</enum>
    </property>
   </widget>
   <widget class="QLabel" name="main_processLabel">
    <property name="geometry">
     <rect>
      <x>13</x>
      <y>353</y>
      <width>231</width>
      <height>16</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
     </font>
    </property>
    <property name="text">
     <string notr="true"/>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="textInteractionFlags">
     <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
    </property>
   </widget>
   <widget class="QPushButton" name="link_pushButton">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>340</x>
      <y>342</y>
      <width>31</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <underline>false</underline>
     </font>
    </property>
    <property name="cursor">
     <cursorShape>PointingHandCursor</cursorShape>
    </property>
    <property name="mouseTracking">
     <bool>false</bool>
    </property>
    <property name="focusPolicy">
     <enum>Qt::NoFocus</enum>
    </property>
    <property name="toolTip">
     <string>Author Profile</string>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset>
      <normaloff>icons/linkedin.png</normaloff>icons/linkedin.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>25</width>
      <height>25</height>
     </size>
    </property>
    <property name="checkable">
     <bool>false</bool>
    </property>
    <property name="checked">
     <bool>false</bool>
    </property>
    <property name="autoDefault">
     <bool>false</bool>
    </property>
    <property name="default">
     <bool>false</bool>
    </property>
    <property name="flat">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="miscUpdate_pushButton">
    <property name="geometry">
     <rect>
      <x>220</x>
      <y>342</y>
      <width>31</width>
      <height>30</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="cursor">
     <cursorShape>PointingHandCursor</cursorShape>
    </property>
    <property name="toolTip">
     <string>New Version Available!</string>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset>
      <normaloff>icons/update.png</normaloff>icons/update.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>25</width>
      <height>25</height>
     </size>
    </property>
    <property name="checkable">
     <bool>false</bool>
    </property>
    <property name="autoRepeat">
     <bool>false</bool>
    </property>
    <property name="autoExclusive">
     <bool>false</bool>
    </property>
    <property name="flat">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="vimeo_pushButton">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>280</x>
      <y>342</y>
      <width>31</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <underline>false</underline>
     </font>
    </property>
    <property name="cursor">
     <cursorShape>PointingHandCursor</cursorShape>
    </property>
    <property name="mouseTracking">
     <bool>false</bool>
    </property>
    <property name="focusPolicy">
     <enum>Qt::NoFocus</enum>
    </property>
    <property name="toolTip">
     <string>Tutorials on YouTube</string>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset>
      <normaloff>icons/youtube.png</normaloff>icons/youtube.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>25</width>
      <height>25</height>
     </size>
    </property>
    <property name="checkable">
     <bool>false</bool>
    </property>
    <property name="checked">
     <bool>false</bool>
    </property>
    <property name="autoDefault">
     <bool>false</bool>
    </property>
    <property name="default">
     <bool>false</bool>
    </property>
    <property name="flat">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="bilibili_pushButton">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>342</y>
      <width>31</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <underline>false</underline>
     </font>
    </property>
    <property name="cursor">
     <cursorShape>PointingHandCursor</cursorShape>
    </property>
    <property name="mouseTracking">
     <bool>false</bool>
    </property>
    <property name="focusPolicy">
     <enum>Qt::NoFocus</enum>
    </property>
    <property name="toolTip">
     <string>Tutorials on bilibili</string>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset>
      <normaloff>icons/bilibili.png</normaloff>icons/bilibili.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>25</width>
      <height>25</height>
     </size>
    </property>
    <property name="checkable">
     <bool>false</bool>
    </property>
    <property name="checked">
     <bool>false</bool>
    </property>
    <property name="autoDefault">
     <bool>false</bool>
    </property>
    <property name="default">
     <bool>false</bool>
    </property>
    <property name="flat">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="language_button">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>310</x>
      <y>342</y>
      <width>31</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <underline>false</underline>
     </font>
    </property>
    <property name="cursor">
     <cursorShape>PointingHandCursor</cursorShape>
    </property>
    <property name="mouseTracking">
     <bool>false</bool>
    </property>
    <property name="focusPolicy">
     <enum>Qt::NoFocus</enum>
    </property>
    <property name="toolTip">
     <string>Set Language</string>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset>
      <normaloff>icons/language.png</normaloff>icons/language.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>25</width>
      <height>25</height>
     </size>
    </property>
    <property name="checkable">
     <bool>false</bool>
    </property>
    <property name="checked">
     <bool>false</bool>
    </property>
    <property name="autoDefault">
     <bool>false</bool>
    </property>
    <property name="default">
     <bool>false</bool>
    </property>
    <property name="flat">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="main_lang_id">
    <property name="geometry">
     <rect>
      <x>-50</x>
      <y>360</y>
      <width>21</width>
      <height>16</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
     </font>
    </property>
    <property name="text">
     <string notr="true">eng</string>
    </property>
   </widget>
   <widget class="QListWidget" name="spring_language_list">
    <property name="geometry">
     <rect>
      <x>307</x>
      <y>235</y>
      <width>36</width>
      <height>111</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <kerning>false</kerning>
     </font>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="frameShape">
     <enum>QFrame::NoFrame</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
    <property name="lineWidth">
     <number>0</number>
    </property>
    <property name="verticalScrollBarPolicy">
     <enum>Qt::ScrollBarAlwaysOff</enum>
    </property>
    <property name="horizontalScrollBarPolicy">
     <enum>Qt::ScrollBarAlwaysOff</enum>
    </property>
    <property name="flow">
     <enum>QListView::LeftToRight</enum>
    </property>
    <property name="gridSize">
     <size>
      <width>36</width>
      <height>36</height>
     </size>
    </property>
    <property name="viewMode">
     <enum>QListView::IconMode</enum>
    </property>
    <item>
     <property name="text">
      <string notr="true"/>
     </property>
     <property name="toolTip">
      <string>简体中文</string>
     </property>
     <property name="textAlignment">
      <set>AlignHCenter|AlignVCenter|AlignCenter</set>
     </property>
     <property name="icon">
      <iconset>
       <normaloff>icons/China Flag.png</normaloff>icons/China Flag.png</iconset>
     </property>
     <property name="flags">
      <set>ItemIsSelectable|ItemIsEnabled</set>
     </property>
    </item>
    <item>
     <property name="text">
      <string notr="true"/>
     </property>
     <property name="toolTip">
      <string>English</string>
     </property>
     <property name="textAlignment">
      <set>AlignHCenter|AlignVCenter|AlignCenter</set>
     </property>
     <property name="icon">
      <iconset>
       <normaloff>icons/english.png</normaloff>icons/english.png</iconset>
     </property>
     <property name="flags">
      <set>ItemIsSelectable|ItemIsEnabled</set>
     </property>
    </item>
    <item>
     <property name="text">
      <string notr="true"/>
     </property>
     <property name="textAlignment">
      <set>AlignHCenter|AlignVCenter|AlignCenter</set>
     </property>
     <property name="icon">
      <iconset>
       <normaloff>icons/japanese.png</normaloff>icons/japanese.png</iconset>
     </property>
     <property name="flags">
      <set>ItemIsSelectable|ItemIsEnabled</set>
     </property>
    </item>
   </widget>
   <zorder>main_processLabel</zorder>
   <zorder>link_pushButton</zorder>
   <zorder>main_tab</zorder>
   <zorder>main_textEdit</zorder>
   <zorder>main_progressBar</zorder>
   <zorder>miscUpdate_pushButton</zorder>
   <zorder>vimeo_pushButton</zorder>
   <zorder>bilibili_pushButton</zorder>
   <zorder>language_button</zorder>
   <zorder>main_lang_id</zorder>
   <zorder>spring_language_list</zorder>
  </widget>
  <widget class="QStatusBar" name="statusbar">
   <property name="enabled">
    <bool>true</bool>
   </property>
  </widget>
  <action name="main_actionReset">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Reset</string>
   </property>
  </action>
  <action name="main_actionAbout">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="checked">
    <bool>false</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>About...</string>
   </property>
   <property name="menuRole">
    <enum>QAction::AboutRole</enum>
   </property>
  </action>
 </widget>
 <tabstops>
  <tabstop>main_tab</tabstop>
  <tabstop>main_textEdit</tabstop>
 </tabstops>
 <resources/>
 <connections>
  <connection>
   <sender>springCapsule_checkBox</sender>
   <signal>clicked(bool)</signal>
   <receiver>springFastMove_checkBox</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>62</x>
     <y>193</y>
    </hint>
    <hint type="destinationlabel">
     <x>127</x>
     <y>191</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>springCapsule_checkBox</sender>
   <signal>clicked(bool)</signal>
   <receiver>label_31</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>70</x>
     <y>196</y>
    </hint>
    <hint type="destinationlabel">
     <x>82</x>
     <y>146</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>springCapsule_checkBox</sender>
   <signal>clicked(bool)</signal>
   <receiver>springSubDiv_lineEdit</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>57</x>
     <y>194</y>
    </hint>
    <hint type="destinationlabel">
     <x>125</x>
     <y>139</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>springCapsule_checkBox</sender>
   <signal>clicked(bool)</signal>
   <receiver>label_30</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>65</x>
     <y>191</y>
    </hint>
    <hint type="destinationlabel">
     <x>277</x>
     <y>66</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>springCapsule_checkBox</sender>
   <signal>clicked(bool)</signal>
   <receiver>springTension_lineEdit</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>50</x>
     <y>191</y>
    </hint>
    <hint type="destinationlabel">
     <x>274</x>
     <y>72</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>springFrom_radioButton</sender>
   <signal>toggled(bool)</signal>
   <receiver>springFrom_lineEdit</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>185</x>
     <y>114</y>
    </hint>
    <hint type="destinationlabel">
     <x>222</x>
     <y>111</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>springFrom_radioButton</sender>
   <signal>toggled(bool)</signal>
   <receiver>springEnd_lineEdit</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>182</x>
     <y>116</y>
    </hint>
    <hint type="destinationlabel">
     <x>277</x>
     <y>116</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>springCapsule_checkBox</sender>
   <signal>clicked(bool)</signal>
   <receiver>springClearSubFrame_checkBox</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>67</x>
     <y>192</y>
    </hint>
    <hint type="destinationlabel">
     <x>212</x>
     <y>147</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
