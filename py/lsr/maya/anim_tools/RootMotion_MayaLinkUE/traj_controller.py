# -*- coding: utf-8 -*-
from functools import partial
from Qt import QtWidgets, QtCore
from lsr.maya.anim_tools.RootMotion_MayaLinkUE.ui.collect_character_ui import CollectCharacterUI
from lsr.maya.anim_tools.RootMotion_MayaLinkUE.ui.traj_anim_table.controller import AnimTableController
from lsr.maya.anim_tools.RootMotion_MayaLinkUE.utils.maya_utils import set_key_matrix, reset_node_motion, get_node_by_name
from lsr.maya.anim_tools.RootMotion_MayaLinkUE.ui.traj_anim_table.data.config import TrajAnimTableConfig


class TrajTabWidget(QtWidgets.QWidget):
    def __init__(self, parent=None, *args, **kwargs):
        """
        Initialize the TrajTabWidget.

        Args:
            parent (QtWidgets.QWidget, optional): The parent widget. Defaults to None.
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        super(TrajTabWidget, self).__init__(parent)
        self.setObjectName("Traj")
        self._init_layout()
        self._init_status_tips()

    def _init_layout(self, *args, **kwargs):
        """
        Initialize the layout of the TrajTabWidget.

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        layout = QtWidgets.QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setAlignment(QtCore.Qt.AlignTop)

        self._init_widget()
        self._init_tec_doc_btn()
        self._init_status_tips()
        self._init_signal()

    def _init_widget(self, *args, **kwargs):
        """
        Initialize the widgets of the TrajTabWidget.

        Args:
        """

        # main ui
        self.char_collection_edit = CollectCharacterUI(self)
        self.anim_table = AnimTableController(self)

        self.layout().addWidget(self.char_collection_edit)
        self.layout().addWidget(self.anim_table)

        # button
        self.add_btn = QtWidgets.QPushButton("Add", self)
        self.remove_btn = QtWidgets.QPushButton("Remove", self)
        self.clear_btn = QtWidgets.QPushButton("Clear", self)
        self.build_btn = QtWidgets.QPushButton("[Build/Refresh Traj]", self)
        self.reset_root_btn = QtWidgets.QPushButton("[Reset Root]", self)
        self.reset_root_btn.setFixedWidth(100)

        btn_layout = QtWidgets.QHBoxLayout()
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.remove_btn)
        btn_layout.addWidget(self.clear_btn)

        root_btn_layout = QtWidgets.QHBoxLayout()
        root_btn_layout.addWidget(self.build_btn)
        root_btn_layout.addWidget(self.reset_root_btn)

        self.layout().addLayout(btn_layout)
        self.layout().addLayout(root_btn_layout)

    def _init_signal(self, *args, **kwargs):
        """
        Initialize the signals of the TrajTabWidget.

        Args:
        """
        self.add_btn.clicked.connect(partial(self.anim_table.view.add_row))
        self.remove_btn.clicked.connect(partial(self.anim_table.view.remove_selected_rows))
        self.clear_btn.clicked.connect(partial(self.anim_table.view.remove_all_rows))
        self.build_btn.clicked.connect(self.anim_table.build_traj)
        self.reset_root_btn.clicked.connect(partial(self.reset_root))
        self.anim_table.apply_traj_signal.connect(partial(self.traj_apply))

    def _init_status_tips(self, *args, **kwargs):
        """
        Initialize the status tips of the TrajTabWidget.

        Args:
        """
        def __register_event_filter():
            """
            register eventFilter
            """
            self.obj_msg = {
                self.char_collection_edit: '单击Load Object按钮获取当前选择控制器.',
                self.anim_table: '动画列表.',
                self.add_btn: '添加.',
                self.remove_btn: '移除.',
                self.clear_btn: '清空.',
                self.build_btn: '构建/重建轨迹.',
                self.reset_root_btn: '重置根骨骼运动.',
                self.tec_doc_btn: '打开工具使用文档.',
            }
            for obj, msg in self.obj_msg.items():
                obj.installEventFilter(self)

        __register_event_filter()
        self.status_tips_bar = QtWidgets.QStatusBar(self)
        self.layout().addWidget(self.status_tips_bar)

    def _init_tec_doc_btn(self, *args, **kwargs):
        """
        Initialize the technical documentation button of the TrajTabWidget.

        Args:
        """
        def _open_tec_doc(*args, **kwargs):
            """
            Open the technical documentation.

            Args:
            """
            import webbrowser
            webbrowser.open(TrajAnimTableConfig.TEC_DOC_URL)

        self.tec_doc_btn = QtWidgets.QPushButton("Help", self)
        self.tec_doc_btn.clicked.connect(partial(_open_tec_doc))

        btn_h_layout = QtWidgets.QHBoxLayout()
        btn_h_layout.setAlignment(QtCore.Qt.AlignRight)
        btn_h_layout.addWidget(self.tec_doc_btn)
        self.layout().addLayout(btn_h_layout)

    def _check_character(self, *args, **kwargs):
        node = self.char_collection_edit.char_name_line.text()
        if not node:
            QtWidgets.QMessageBox.warning(self, "错误", "请先拾取对象")
            return False
        else:
            node = get_node_by_name(node)
            if not node:
                QtWidgets.QMessageBox.warning(self, "错误", "未在场景中找到对象，请重新拾取")
                return False
        return True


    def traj_apply(self, traj_data, *args, **kwargs):
        node_name = self.char_collection_edit.char_name_line.text()
        if not self._check_character():
            return
        set_key_matrix(traj_data, TrajAnimTableConfig.IN_TIME_STEP, node_name)

    def reset_root(self, *args, **kwargs):
        node_name = self.char_collection_edit.char_name_line.text()
        if not self._check_character():
            return
        reset_node_motion(node_name)
        print(node_name)


    def save_settings(self, settings, *args, **kwargs):
        self.anim_table.save_settings(settings)

    def load_settings(self, settings, *args, **kwargs):
        self.anim_table.load_settings(settings)

    def eventFilter(self, object, event, *args, **kwargs):
        if event.type() is QtCore.QEvent.HoverEnter:
            for obj, msg in self.obj_msg.items():
                if object is obj:
                    self.status_tips_bar.showMessage(msg)
                    return True

        elif event.type() is QtCore.QEvent.HoverLeave:
            self.status_tips_bar.showMessage(' ')
            return True
        return False


