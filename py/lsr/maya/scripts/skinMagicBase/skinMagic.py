######################################################################################################
#                                                                                                    #
#   This is a skinMagic Secondary development, for only compatible with both Python2 and Python3     #
#                                                                                                    #
######################################################################################################
import json
import os
import webbrowser

import maya.cmds as cmds

from Qt import QtWidgets, QtGui, QtCore
from .skinMagicGlobal import GLOBAL_VAR, GLOBAL_METHOD, GLOBAL_UI_WIDGET_MANAGER
from .skinMagicWeight import WeightTab
from .skinMagicRename import Ren<PERSON><PERSON><PERSON>
from .skinMagicLod import LodTab
from .skinMagicMisc import MiscTab


class DonateTab(QtWidgets.QWidget):

    def __init__(self, parent=None):
        super(DonateTab, self).__init__(parent)
        self.bitcoin_url = GLOBAL_VAR.BITCOIN_URL
        self.paypal_url = GLOBAL_VAR.PAYPAL_URL
        self._init_layout()
        self._do_signal_connection()

    def _init_layout(self):
        main_lay = QtWidgets.QVBoxLayout()
        self.setLayout(main_lay)

        group_box_layout = QtWidgets.QVBoxLayout()
        group_box_layout.setAlignment(QtCore.Qt.AlignmentFlag.AlignTop)

        self.eng_group_box = QtWidgets.QGroupBox(self)
        self.eng_group_box.setLayout(group_box_layout)
        self.eng_group_box.setTitle('Need Your Supports!')
        self.eng_group_box.setAlignment(QtCore.Qt.AlignmentFlag.AlignLeft)

        # donate tips
        text_label = QtWidgets.QLabel(self)
        text_label.setText("Skin Magic is free for everyone, "
                           "you can use Full Function as free! "
                           "However if this make your life easier... "
                           "How about bought me cup of coffee?")
        text_label.setWordWrap(True)

        # donate bitcoin
        bitcoin_layout = QtWidgets.QHBoxLayout()
        self.bitcoin_label = QtWidgets.QLabel(self)
        self.donate_bitcoin_lineedit = QtWidgets.QLineEdit(self.bitcoin_url, self)
        self.donate_bitcoin_lineedit.setFixedHeight(20)
        self.donate_bitcoin_lineedit.setReadOnly(True)
        bitcoin_img = QtGui.QPixmap(os.path.join(GLOBAL_VAR.ICON_PATH, "bitcoin.png"))
        self.bitcoin_label.setPixmap(bitcoin_img)
        self.bitcoin_label.setFixedSize(bitcoin_img.width(), bitcoin_img.height())

        # payable widget
        self.donate_alipay_label = QtWidgets.QLabel(self)
        self.donate_wechat_label = QtWidgets.QLabel(self)
        alipay_img = QtGui.QPixmap(os.path.join(GLOBAL_VAR.ICON_PATH, "ali_pay.png"))
        wechat_img = QtGui.QPixmap(os.path.join(GLOBAL_VAR.ICON_PATH, "wechat_pay.png"))
        self.donate_alipay_label.setPixmap(alipay_img)
        self.donate_wechat_label.setPixmap(wechat_img)
        self.bitcoin_label.setFixedSize(alipay_img.size())
        self.bitcoin_label.setFixedSize(wechat_img.size())

        self.paypal_btn = QtWidgets.QPushButton(self)
        self.paypal_btn.setIcon(QtGui.QIcon(os.path.join(GLOBAL_VAR.ICON_PATH, "paypal.png")))
        self.paypal_btn.setFixedSize(40, 40)
        self.paypal_btn.setIconSize(QtCore.QSize(30, 30))

        bitcoin_layout.addWidget(self.bitcoin_label)
        bitcoin_layout.addWidget(self.donate_bitcoin_lineedit)
        pay_layout = QtWidgets.QHBoxLayout()
        pay_layout.addWidget(self.donate_alipay_label)
        pay_layout.addWidget(self.paypal_btn)
        pay_layout.addWidget(self.donate_wechat_label)
        pay_layout.setAlignment(QtCore.Qt.AlignmentFlag.AlignLeft)

        group_box_layout.addWidget(text_label)
        group_box_layout.addLayout(bitcoin_layout)
        group_box_layout.addLayout(pay_layout)
        main_lay.addWidget(self.eng_group_box)

    def _do_signal_connection(self):
        self.paypal_btn.clicked.connect(self.donate_paypal_btn_command)

    def donate_paypal_btn_command(self):
        webbrowser.open(self.paypal_url, new=2)


class MainTabWidget(QtWidgets.QTabWidget):
    def __init__(self, parent=None):
        super(MainTabWidget, self).__init__(parent)
        self.donate_tab = DonateTab(self)
        self.weight_tab = WeightTab(self)
        self.rename_tab = RenameTab(self)
        self.lods_tab = LodTab(self)
        self.misc_tab = MiscTab(self)

        self.addTab(self.donate_tab, 'Donate')
        self.addTab(self.weight_tab, 'Weight')
        self.addTab(self.rename_tab, 'Rename')
        self.addTab(self.lods_tab, 'LoDs')
        self.addTab(self.misc_tab, 'Misc')


class LanguageSelectWidget(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super(LanguageSelectWidget, self).__init__()
        self.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint | QtCore.Qt.WindowStaysOnTopHint)
        self.language_type = "en_US"
        self._init_layout()
        self._custom_widget_style()

    def _init_layout(self):
        main_lay = QtWidgets.QVBoxLayout()
        self.setLayout(main_lay)
        main_lay.setContentsMargins(0, 0, 0, 0)

        self.btn_group = QtWidgets.QButtonGroup()

        self.language_zh_CN_btn = QtWidgets.QCheckBox(self)
        self.language_zh_CN_btn.setObjectName("zh_CN")
        self.language_en_US_btn = QtWidgets.QCheckBox(self)
        self.language_en_US_btn.setObjectName("en_US")

        self.language_en_US_btn.setChecked(True)

        self.btn_group.addButton(self.language_en_US_btn)
        self.btn_group.addButton(self.language_zh_CN_btn)
        main_lay.addWidget(self.language_zh_CN_btn)
        main_lay.addWidget(self.language_en_US_btn)

    def _custom_widget_style(self):
        self.language_zh_CN_btn.setIcon(QtGui.QIcon(os.path.join(GLOBAL_VAR.ICON_PATH, "China Flag.png")))
        self.language_zh_CN_btn.setIconSize(QtCore.QSize(20, 20))
        self.language_zh_CN_btn.setFixedSize(30, 30)

        self.language_en_US_btn.setIcon(QtGui.QIcon(os.path.join(GLOBAL_VAR.ICON_PATH, "english.png")))
        self.language_en_US_btn.setIconSize(QtCore.QSize(20, 20))
        self.language_en_US_btn.setFixedSize(30, 30)

        self.setStyleSheet("""
            QCheckBox::indicator {
                width: 0px;
                height: 0px;
            }
        """)

    def showEvent(self, event):
        self.raise_()
        global_mouse_pos = QtGui.QCursor.pos()
        window_pos = QtCore.QPoint(global_mouse_pos.x() - 20, global_mouse_pos.y() - 20)
        self.move(window_pos)

    def leaveEvent(self, event):
        self.hide()


class CentralWidget(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super(CentralWidget, self).__init__(parent)
        self._init_layout()
        self._custom_widget_style()
        self._do_signal_connection()

    def _init_layout(self):
        main_lay = QtWidgets.QVBoxLayout()
        main_lay.setAlignment(QtCore.Qt.AlignmentFlag.AlignTop | QtCore.Qt.AlignmentFlag.AlignCenter)
        self.setLayout(main_lay)
        self.tab_widget = MainTabWidget(self)

        # button
        self.vimeo_btn = QtWidgets.QPushButton(self)
        self.link_btn = QtWidgets.QPushButton(self)
        self.language_btn = QtWidgets.QPushButton(self)
        self.language_widget = LanguageSelectWidget(self)
        self.bilibili_btn = QtWidgets.QPushButton(self)

        GLOBAL_UI_WIDGET_MANAGER.GLOBAL_CENTRAL_PROCESS_LABEL_WIDGET = self.main_process_label = QtWidgets.QLabel(self)
        GLOBAL_UI_WIDGET_MANAGER.GLOBAL_CENTRAL_PROGRESS_BAR_WIDGET = self.main_progress_bar = QtWidgets.QProgressBar(self)
        GLOBAL_UI_WIDGET_MANAGER.GLOBAL_CENTRAL_LINEEDIT_WIDGET = self.main_tips_edit = QtWidgets.QTextEdit(self)
        self.main_tips_edit.insertPlainText("Created by Bai Yanbin\n\n\n")
        self.main_tips_edit.setReadOnly(True)

        btn_lay = QtWidgets.QHBoxLayout()
        btn_lay.setAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignCenter)
        btn_lay.addWidget(self.vimeo_btn)
        btn_lay.addWidget(self.link_btn)
        btn_lay.addWidget(self.language_btn)

        btn_lay.addWidget(self.bilibili_btn)

        main_lay.addWidget(self.tab_widget)
        main_lay.addLayout(btn_lay)

        main_lay.addWidget(self.main_process_label)
        main_lay.addWidget(self.main_progress_bar)
        main_lay.addWidget(self.main_tips_edit)

        GLOBAL_VAR.GLOBAL_TOOL_PROCESS_LABEL_WIDGET = self.main_process_label

    def _custom_widget_style(self):
        self.main_progress_bar.setStyleSheet('QProgressBar {text-align: center}')

        self.vimeo_btn.setIcon(QtGui.QIcon(os.path.join(GLOBAL_VAR.ICON_PATH, "youtube.png")))
        self.vimeo_btn.setIconSize(QtCore.QSize(25, 25))
        self.vimeo_btn.setFixedSize(30, 30)

        self.link_btn.setIcon(QtGui.QIcon(os.path.join(GLOBAL_VAR.ICON_PATH, "linkedin.png")))
        self.link_btn.setIconSize(QtCore.QSize(25, 25))
        self.link_btn.setFixedSize(30, 30)

        self.language_btn.setIcon(QtGui.QIcon(os.path.join(GLOBAL_VAR.ICON_PATH, "language.png")))
        self.language_btn.setIconSize(QtCore.QSize(25, 25))
        self.language_btn.setFixedSize(30, 30)

        self.bilibili_btn.setIcon(QtGui.QIcon(os.path.join(GLOBAL_VAR.ICON_PATH, "bilibili.png")))
        self.bilibili_btn.setIconSize(QtCore.QSize(25, 25))
        self.bilibili_btn.setFixedSize(30, 30)

    def _do_signal_connection(self):
        self.vimeo_btn.clicked.connect(self.call_youtube_command)
        self.link_btn.clicked.connect(self.call_linkedin_command)
        self.language_btn.clicked.connect(self.call_language_widget)
        self.bilibili_btn.clicked.connect(self.call_bilibili_command)

    @staticmethod
    def call_youtube_command():
        # youtubeCmd
        webbrowser.open(GLOBAL_VAR.YOUTUBE_URL, new=2)

    @staticmethod
    def call_linkedin_command():
        # linkinCmd
        webbrowser.open(GLOBAL_VAR.LINKEDIN_URL, new=2)

    @staticmethod
    def call_bilibili_command():
        # bilibiliCmd
        webbrowser.open(GLOBAL_VAR.BILIBILI_URL, new=2)

    def call_language_widget(self):
        self.language_widget.show()


class SkinMagicMainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super(SkinMagicMainWindow, self).__init__()
        self.setWindowFlags(QtCore.Qt.WindowStaysOnTopHint)
        self.setWindowTitle("skinMagic")
        self._init_layout()
        self._do_signal_connection()

    def _init_layout(self):
        self.central_widget = CentralWidget(self)
        self.setCentralWidget(self.central_widget)

    def _custom_style_preset(self):
        pass

    def _do_signal_connection(self):
        self.central_widget.language_widget.language_en_US_btn.clicked.connect(self.change_language)
        self.central_widget.language_widget.language_zh_CN_btn.clicked.connect(self.change_language)
        pass

    def change_language(self):
        GLOBAL_VAR.UI_TRANSLATOR.language_type = self.central_widget.language_widget.btn_group.checkedButton().objectName()
        self.central_widget.language_widget.hide()
        GLOBAL_VAR.UI_TRANSLATOR.translate_widget(self)

    def closeEvent(self, event):
        self.save_settings()
        self.close()

    def showEvent(self, event):
        self.show()
        self.resize(QtCore.QSize(460, 700))

    def load_settings(self, setting_data, *args, **kwargs):
        data_keys = list(setting_data.keys())

        GLOBAL_VAR.UI_TRANSLATOR.language_type = setting_data['language_type'] if 'language_type' in data_keys else 'en_US'
        GLOBAL_VAR.UI_TRANSLATOR.translate_widget(self)

        self.central_widget.tab_widget.setCurrentIndex(setting_data['tab_widget'] if 'tab_widget' in data_keys else 0)
        self.central_widget.tab_widget.weight_tab.load_settings(setting_data['skinMagicWeight'] if 'skinMagicWeight' in data_keys else dict())
        self.central_widget.tab_widget.rename_tab.load_settings(setting_data['skinMagicRename'] if 'skinMagicRename' in data_keys else dict())

    def save_settings(self):
        data_path = os.path.join(GLOBAL_VAR.UI_PATH, 'skin_magic_ui_setting.json')
        data = dict()

        data['tab_widget'] = self.central_widget.tab_widget.currentIndex()
        data['language_type'] = GLOBAL_VAR.UI_TRANSLATOR.language_type
        data['skinMagicWeight'] = self.central_widget.tab_widget.weight_tab.save_settings()
        data['skinMagicRename'] = self.central_widget.tab_widget.rename_tab.save_settings()

        GLOBAL_METHOD.save_json(data, data_path)


def launch():
    data_path = os.path.join(GLOBAL_VAR.UI_PATH, 'skin_magic_ui_setting.json')
    if not os.path.exists(data_path):
        setting_data = {}
    else:
        setting_data = GLOBAL_METHOD.load_json(data_path)

    if cmds.window('SkinMagicMainWindow', exists=1):
        cmds.deleteUI('SkinMagicMainWindow')

    GLOBAL_VAR.SKIN_MAGIC_UI = SkinMagicMainWindow()
    GLOBAL_VAR.SKIN_MAGIC_UI.setObjectName('SkinMagicMainWindow')

    GLOBAL_VAR.SKIN_MAGIC_UI.setParent(QtWidgets.QApplication.activeWindow())
    GLOBAL_VAR.SKIN_MAGIC_UI.setWindowFlags(QtCore.Qt.Window)

    script_job_ui_close_window = cmds.scriptJob(uiDeleted=('SkinMagicMainWindow', GLOBAL_METHOD.close_ui), runOnce=True)
    script_job_weight_update_selection = cmds.scriptJob(
        event=["SelectionChanged", GLOBAL_METHOD.selection_changed],
        protected=True,
        parent='SkinMagicMainWindow')
    script_job_main_update_ui = cmds.scriptJob(event=["SceneOpened", GLOBAL_METHOD.reset_ui],
                                               protected=True,
                                               parent='SkinMagicMainWindow')

    GLOBAL_METHOD.selection_changed()
    GLOBAL_METHOD.change_vert_priority()
    GLOBAL_VAR.SKIN_MAGIC_UI.load_settings(setting_data)
    GLOBAL_VAR.SKIN_MAGIC_UI.show()
