#!/usr/bin/env ptuyhon

from maya import cmds
from maya import mel
from lsr.maya.anim_tools.keyfram_reduction import utils

import logging

logging.basicConfig()
logger = logging.getLogger('keyframeEditor')
logger.setLevel(logging.DEBUG)


def setKeyframesTimeOffset(anim_curve=None, time=0):
    """
    This function will move all the given curves along the timeline with certain distance.
    Args:
        anim_curve: the curve which will be moved
        time: offset value

    Returns: the number of curves which have been moved successfully

    """
    num = 0
    if not anim_curve:
        logger.error("There is no anim curve selected!")
    else:
        num = cmds.keyframe(anim_curve, time=(), edit=True, timeChange=time, relative=True)

    logger.info("The number of curves which have been moved: %s." % num)

    return num


def getAllAnimCurveWithTimeAsInput(obj=None, selection=True):
    """
    This function will get all the anim curves which take time as input.

    Args:
        obj: objects which anim curves would be searched from
        selection: only valid when obj param is none. True- with objects selected currently; False- with all the objects in the current scene.

    Returns: a list of anim curves

    """

    anim_curve = []

    if not obj:
        if not selection:
            # select all the objects in the scene
            obj = cmds.ls(dag=True)
        else:
            # get objects which have been selected (including attributes)
            obj = cmds.selectionConnection('graphEditor1FromOutliner', query=True, object=True)

    logger.debug(obj)

    anim_curve.extend(getAnimCurveFromObj(obj, curve_type='animCurveTA'))
    anim_curve.extend(getAnimCurveFromObj(obj, curve_type='animCurveTU'))
    anim_curve.extend(getAnimCurveFromObj(obj, curve_type='animCurveTL'))
    anim_curve.extend(getAnimCurveFromObj(obj, curve_type='animCurveTT'))

    return anim_curve


def getAnimCurveFromObj(obj, curve_type='animCurve'):
    """
    This function will return specified type of anim curves
    Args:
        obj: where the anim curves come from
        curve_type: the specified type of anim curves

    Returns: a list of anim curves

    """
    anim_curve = []

    # This obj could also be some attributes
    # Note: Attribute Name is like "pSphere1.rotateY" and Curve Name is like "pSphere1_rotateY"
    curves = cmds.listConnections(obj, type=curve_type)

    if curves:
        # Note: list.extend(None) will arise error
        anim_curve.extend(curves)

    return anim_curve


def get_seeleted_timenrange():
    """
    Get the selected time range
    Returns: the selected time range

    """
    time_slider = mel.eval("$tmpVar = $gPlayBackSlider;")
    time_range = cmds.timeControl(time_slider, query=True, rangeArray=True)

    return time_range


def GetChannelBoxAttrs():
    """
    Get the selected attributes in the channel box
    Returns:

    """
    if (cmds.channelBox('mainChannelBox', query=True, selectedMainAttributes=True) or
            cmds.channelBox('mainChannelBox', query=True, selectedShapeAttributes=True) or
            cmds.channelBox('mainChannelBox', query=True, selectedHistoryAttributes=True)):
        if cmds.channelBox('mainChannelBox', query=True, selectedMainAttributes=True):
            return cmds.channelBox('mainChannelBox', query=True, selectedMainAttributes=True)
        if cmds.channelBox('mainChannelBox', query=True, selectedShapeAttributes=True):
            return cmds.channelBox('mainChannelBox', query=True, selectedShapeAttributes=True)
        if cmds.channelBox('mainChannelBox', query=True, selectedHistoryAttributes=True):
            return cmds.channelBox('mainChannelBox', query=True, selectedHistoryAttributes=True)
    else:
        return None


def OffsetKeysProc(objects, framesOffset, direction, changeMode):
    """
    This function will offset the keyframes of the selected objects.
    objects: the selected objects
    framesOffset: the offset value
    direction: the direction of the offset
    changeMode: time change or value change,tv,tc
        Returns:
    """
    # global gPlayBackSlider
    gPlayBackSlider = mel.eval("$tmpVar = $gPlayBackSlider;")
    if not objects:
        return None

    if objects:
        OffsetKeysCBAttrs = GetChannelBoxAttrs()
        OffsetKeysAttrs = ""
        selectedKeysFlag = ""

        animCurves = cmds.keyframe(query=True, selected=True, name=True)
        if OffsetKeysCBAttrs:
            OffsetKeysAttrs += " attribute={}".format(OffsetKeysCBAttrs)
        if animCurves:
            sizeAnimCurves = len(animCurves)
            objects = animCurves

        timeRange = cmds.timeControl(gPlayBackSlider, query=True, rangeArray=True)
        channels = cmds.timeControl(gPlayBackSlider, query=True, animCurveNames=True)

        if channels:
            keys = cmds.keyframe(channels, time=(timeRange[0], timeRange[1]), query=True, timeChange=True)
        else:
            return

        framesOffset *= direction

        offset = 0

        count = 0
        if changeMode == "timeChange":
            # timeChange
            selectedKeysFlag += "timeChange={}, ".format(framesOffset)
        elif changeMode == "valueChange":
            # valueChange
            selectedKeysFlag += "valueChange={}, ".format(framesOffset)
        else:
            # both timeChange and valueChange
            # cmds.keyframe(obj, e=True, abd=1, option="over", r=True, tc=framesOffset, vc=framesOffset)
            pass

        for obj in objects:
            # The selected keyframe
            # [16.0, 17.0, 18.0, 19.0, 20.0]
            selectedKeys = cmds.keyframe(obj, query=True, selected=True)
            offset = framesOffset
            cmd = "cmds.keyframe(\"{}\", e=True, abd=1, option=\"over\", r=True, {} ".format(obj,
                                                                                             selectedKeysFlag)
            timeFlag = ""
            if timeRange[1] > (timeRange[0] + 1):
                framesRange = (timeRange[0], timeRange[1])
                if selectedKeys:
                    timeFlag += "time={}".format(framesRange)
                else:
                    timeFlag += "time={}, {}".format(framesRange, OffsetKeysAttrs)
            else:
                if selectedKeys:
                    t = []
                    for key in selectedKeys:
                        t.append((key, key))
                    timeFlag = "time={}".format(t)
                else:
                    timeFlag = "{}".format(OffsetKeysAttrs)

            cmd += timeFlag
            cmd += ")"
            exec(cmd)
            count += 1


def OffsetKeysWrappe(framesOffset, direction, changeMode):
    """ Offset the keyframes of the selected objects.
    Args:
        framesOffset: the offset value
        direction: the direction of the offset
        changeMode: time change or value change,tv,tc
        """
    with utils.UndoChunkContext():
        objects = cmds.ls(selection=True)
        OffsetKeysProc(objects, framesOffset, direction, changeMode)


def get_animated(mode=0, obj_type=None):
    """
    Get the animated objects according to the mode and obj_type
    Args:
        mode:
        obj_type:

    Returns:

    """
    ani_objects = []

    # Gets the selected object, or if none is selected, gets the object according to mode
    objects = cmds.ls(selection=True) if cmds.ls(selection=True) else []

    if not objects:
        if mode:
            if obj_type == "nurbsCurve":
                shapes = cmds.ls(type=obj_type)
                objects = cmds.listRelatives(shapes, parent=True) or []
            else:
                objects = cmds.ls(type=obj_type) if obj_type else cmds.ls()
        else:
            objects = cmds.ls()

    for obj in objects:
        # Check whether the object has keyframes
        if cmds.keyframe(obj, query=True):
            # Make sure the object is not the animation curve itself
            if not cmds.ls(obj, type="animCurve"):
                ani_objects.append(obj)

    if not ani_objects:
        cmds.warning("No animated nodes found")

    return ani_objects
