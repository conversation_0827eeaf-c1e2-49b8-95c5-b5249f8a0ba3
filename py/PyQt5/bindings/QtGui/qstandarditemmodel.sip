// qstandarditemmodel.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2021 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStandardItemModel : QAbstractItemModel
{
%TypeHeaderCode
#include <qstandarditemmodel.h>
%End

public:
    explicit QStandardItemModel(QObject *parent /TransferThis/ = 0);
    QStandardItemModel(int rows, int columns, QObject *parent /TransferThis/ = 0);
    virtual ~QStandardItemModel();
    virtual QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const;
    virtual QModelIndex parent(const QModelIndex &child) const;
    QObject *parent() const;
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual int columnCount(const QModelIndex &parent = QModelIndex()) const;
    virtual bool hasChildren(const QModelIndex &parent = QModelIndex()) const;
    virtual QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const;
    virtual bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    virtual QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const;
    virtual bool setHeaderData(int section, Qt::Orientation orientation, const QVariant &value, int role = Qt::EditRole);
    virtual bool insertRows(int row, int count, const QModelIndex &parent = QModelIndex());
    virtual bool insertColumns(int column, int count, const QModelIndex &parent = QModelIndex());
    virtual bool removeRows(int row, int count, const QModelIndex &parent = QModelIndex());
    virtual bool removeColumns(int column, int count, const QModelIndex &parent = QModelIndex());
    virtual Qt::ItemFlags flags(const QModelIndex &index) const;
    void clear();
    virtual Qt::DropActions supportedDropActions() const;
    virtual QMap<int, QVariant> itemData(const QModelIndex &index) const;
    virtual bool setItemData(const QModelIndex &index, const QMap<int, QVariant> &roles);
    virtual void sort(int column, Qt::SortOrder order = Qt::AscendingOrder);
    QStandardItem *itemFromIndex(const QModelIndex &index) const;
    QModelIndex indexFromItem(const QStandardItem *item) const;
    QStandardItem *item(int row, int column = 0) const;
    void setItem(int row, int column, QStandardItem *item /Transfer/);
    void setItem(int arow, QStandardItem *aitem /Transfer/);
    QStandardItem *invisibleRootItem() const /Transfer/;
    QStandardItem *horizontalHeaderItem(int column) const;
    void setHorizontalHeaderItem(int column, QStandardItem *item /Transfer/);
    QStandardItem *verticalHeaderItem(int row) const;
    void setVerticalHeaderItem(int row, QStandardItem *item /Transfer/);
    void setHorizontalHeaderLabels(const QStringList &labels);
    void setVerticalHeaderLabels(const QStringList &labels);
    void setRowCount(int rows);
    void setColumnCount(int columns);
    void appendRow(const QList<QStandardItem *> &items /Transfer/);
    void appendColumn(const QList<QStandardItem *> &items /Transfer/);
    void insertRow(int row, const QList<QStandardItem *> &items /Transfer/);
    void insertColumn(int column, const QList<QStandardItem *> &items /Transfer/);
    QStandardItem *takeItem(int row, int column = 0) /TransferBack/;
    QList<QStandardItem *> takeRow(int row) /TransferBack/;
    QList<QStandardItem *> takeColumn(int column) /TransferBack/;
    QStandardItem *takeHorizontalHeaderItem(int column) /TransferBack/;
    QStandardItem *takeVerticalHeaderItem(int row) /TransferBack/;
    const QStandardItem *itemPrototype() const;
    void setItemPrototype(const QStandardItem *item /Transfer/);
    QList<QStandardItem *> findItems(const QString &text, Qt::MatchFlags flags = Qt::MatchExactly, int column = 0) const;
    int sortRole() const;
    void setSortRole(int role);
    void appendRow(QStandardItem *aitem /Transfer/);
    void insertRow(int arow, QStandardItem *aitem /Transfer/);
    bool insertRow(int row, const QModelIndex &parent = QModelIndex());
    bool insertColumn(int column, const QModelIndex &parent = QModelIndex());
    virtual QStringList mimeTypes() const;
    virtual QMimeData *mimeData(const QModelIndexList &indexes) const /TransferBack/;
    virtual bool dropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent);
    virtual QModelIndex sibling(int row, int column, const QModelIndex &idx) const;
    void setItemRoleNames(const QHash<int, QByteArray> &roleNames);

signals:
    void itemChanged(QStandardItem *item);

public:
%If (Qt_5_12_0 -)
    bool clearItemData(const QModelIndex &index);
%End
};

class QStandardItem /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qstandarditemmodel.h>
%End

public:
    QStandardItem();
    explicit QStandardItem(const QString &text);
    QStandardItem(const QIcon &icon, const QString &text);
    QStandardItem(int rows, int columns = 1);
    virtual ~QStandardItem();
    virtual QVariant data(int role = Qt::UserRole + 1) const;
    virtual void setData(const QVariant &value, int role = Qt::UserRole + 1);
    QString text() const;
    QIcon icon() const;
    QString toolTip() const;
    QString statusTip() const;
    QString whatsThis() const;
    QSize sizeHint() const;
    QFont font() const;
    Qt::Alignment textAlignment() const;
    QBrush background() const;
    QBrush foreground() const;
    Qt::CheckState checkState() const;
    QString accessibleText() const;
    QString accessibleDescription() const;
    Qt::ItemFlags flags() const;
    void setFlags(Qt::ItemFlags flags);
    bool isEnabled() const;
    void setEnabled(bool enabled);
    bool isEditable() const;
    void setEditable(bool editable);
    bool isSelectable() const;
    void setSelectable(bool selectable);
    bool isCheckable() const;
    void setCheckable(bool checkable);
    bool isTristate() const;
    void setTristate(bool tristate);
    bool isDragEnabled() const;
    void setDragEnabled(bool dragEnabled);
    bool isDropEnabled() const;
    void setDropEnabled(bool dropEnabled);
    QStandardItem *parent() const;
    int row() const;
    int column() const;
    QModelIndex index() const;
    QStandardItemModel *model() const;
    int rowCount() const;
    void setRowCount(int rows);
    int columnCount() const;
    void setColumnCount(int columns);
    bool hasChildren() const;
    QStandardItem *child(int row, int column = 0) const;
    void setChild(int row, int column, QStandardItem *item /Transfer/);
    void setChild(int arow, QStandardItem *aitem /Transfer/);
    void insertRow(int row, const QList<QStandardItem *> &items /Transfer/);
    void insertRow(int arow, QStandardItem *aitem /Transfer/);
    void insertRows(int row, int count);
    void insertColumn(int column, const QList<QStandardItem *> &items /Transfer/);
    void insertColumns(int column, int count);
    void removeRow(int row);
    void removeColumn(int column);
    void removeRows(int row, int count);
    void removeColumns(int column, int count);
    QStandardItem *takeChild(int row, int column = 0) /TransferBack/;
    QList<QStandardItem *> takeRow(int row) /TransferBack/;
    QList<QStandardItem *> takeColumn(int column) /TransferBack/;
    void sortChildren(int column, Qt::SortOrder order = Qt::AscendingOrder);
    virtual QStandardItem *clone() const /Factory/;

    enum ItemType
    {
        Type,
        UserType,
    };

    virtual int type() const;
    virtual void read(QDataStream &in);
    virtual void write(QDataStream &out) const;
    virtual bool operator<(const QStandardItem &other /NoCopy/) const;
    void setText(const QString &atext);
    void setIcon(const QIcon &aicon);
    void setToolTip(const QString &atoolTip);
    void setStatusTip(const QString &astatusTip);
    void setWhatsThis(const QString &awhatsThis);
    void setSizeHint(const QSize &asizeHint);
    void setFont(const QFont &afont);
    void setTextAlignment(Qt::Alignment atextAlignment);
    void setBackground(const QBrush &abrush);
    void setForeground(const QBrush &abrush);
    void setCheckState(Qt::CheckState acheckState);
    void setAccessibleText(const QString &aaccessibleText);
    void setAccessibleDescription(const QString &aaccessibleDescription);
    void appendRow(const QList<QStandardItem *> &items /Transfer/);
    void appendRow(QStandardItem *aitem /Transfer/);
    void appendColumn(const QList<QStandardItem *> &items /Transfer/);
    void insertRows(int row, const QList<QStandardItem *> &items /Transfer/);
    void appendRows(const QList<QStandardItem *> &items /Transfer/);

protected:
    QStandardItem(const QStandardItem &other);
    void emitDataChanged();

public:
%If (Qt_5_6_0 -)
    bool isAutoTristate() const;
%End
%If (Qt_5_6_0 -)
    void setAutoTristate(bool tristate);
%End
%If (Qt_5_6_0 -)
    bool isUserTristate() const;
%End
%If (Qt_5_6_0 -)
    void setUserTristate(bool tristate);
%End
%If (Qt_5_12_0 -)
    void clearData();
%End
};

QDataStream &operator>>(QDataStream &in, QStandardItem &item /Constrained/) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &out, const QStandardItem &item /Constrained/) /ReleaseGIL/;
